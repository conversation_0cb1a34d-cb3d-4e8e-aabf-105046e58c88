{"name": "voctana", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "postinstall": "prisma generate", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@aws-sdk/client-s3": "^3.883.0", "@aws-sdk/s3-request-presigner": "^3.883.0", "@google/genai": "^1.17.0", "@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.15.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.69.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.4", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "lucide-react": "^0.542.0", "motion": "^12.23.12", "next": "^15.2.3", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.62.0", "server-only": "^0.0.1", "sonner": "^2.0.7", "superjson": "^2.2.1", "tailwind-merge": "^3.3.1", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4.0.15", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.14.10", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.3.1", "@vitest/ui": "^1.3.1", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "jsdom": "^24.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.5.0", "tailwindcss": "^4.0.15", "tw-animate-css": "^1.3.8", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0", "vitest": "^1.3.1"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "npm@11.4.2"}