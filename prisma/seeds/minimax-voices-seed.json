[{"slug": "english-expressive-narrator", "name": "Expressive Narrator", "nameEn": "Expressive Narrator", "nameZh": "表现力叙述者", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An expressive adult male voice with a British accent, perfect for engaging audiobook narration.", "descriptionEn": "An expressive adult male voice with a British accent, perfect for engaging audiobook narration.", "descriptionZh": "一个富有表现力的 成年男性的声音 ，带有英式口音, ，非常适合 引人入胜的 有声书 叙述.", "styles": ["Expressive", "Narrative"], "stylesEn": ["Expressive", "Narrative"], "stylesZh": ["表现力", "叙述"], "voiceName": "English_expressive_narrator", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-radiant-girl", "name": "Radiant Girl", "nameEn": "Radiant Girl", "nameZh": "活力女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A radiant and lively young adult female voice with a general American accent, full of energy and brightness.", "descriptionEn": "A radiant and lively young adult female voice with a general American accent, full of energy and brightness.", "descriptionZh": "一个光彩照人的 and 活泼的 young 成年女性的声音 ，带有标准 美式口音, full of energy and 明亮的ness.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "English_radiant_girl", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-magnetic-voiced-man", "name": "Magnetic-voiced Male", "nameEn": "Magnetic-voiced Male", "nameZh": "磁性男声", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A magnetic and persuasive adult male voice with a general American accent, ideal for advertisements and promotions.", "descriptionEn": "A magnetic and persuasive adult male voice with a general American accent, ideal for advertisements and promotions.", "descriptionZh": "一个磁性的 and persuasive 成年男性的声音 ，带有标准 美式口音, ，理想用于 广告s and 推广s.", "styles": ["Expressive"], "stylesEn": ["Expressive"], "stylesZh": ["表现力"], "voiceName": "English_magnetic_voiced_man", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-compelling-lady1", "name": "Compelling Lady", "nameEn": "Compelling Lady", "nameZh": "引人注目的女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A compelling adult female voice with a British accent, suitable for broadcast and formal announcements. Clear and authoritative.", "descriptionEn": "A compelling adult female voice with a British accent, suitable for broadcast and formal announcements. Clear and authoritative.", "descriptionZh": "一个引人注目的 成年女性的声音 ，带有英式口音, ，适合 广播 and 正式公告. Clear and 权威的.", "styles": ["Authoritative", "Expressive", "Serious"], "stylesEn": ["Authoritative", "Expressive", "Serious"], "stylesZh": ["权威", "表现力", "严肃"], "voiceName": "English_compelling_lady1", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-aussie-bloke", "name": "Au<PERSON>", "nameEn": "Au<PERSON>", "nameZh": "澳洲小伙", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A friendly, bright adult male voice with a distinct Australian accent, conveying a cheerful and approachable tone.", "descriptionEn": "A friendly, bright adult male voice with a distinct Australian accent, conveying a cheerful and approachable tone.", "descriptionZh": "A 友好的, 明亮的 成年男性 voice ，带有独特的 澳式口音, ，传达 a 愉快的 and approachable tone.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "English_<PERSON><PERSON>_B<PERSON>ke", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-captivating-female1", "name": "Captivating Female", "nameEn": "Captivating Female", "nameZh": "迷人女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A captivating adult female voice with a general American accent, ideal for news reporting and documentary narration.", "descriptionEn": "A captivating adult female voice with a general American accent, ideal for news reporting and documentary narration.", "descriptionZh": "一个迷人的 成年女性的声音 ，带有标准 美式口音, ，理想用于 新闻播报 and 纪录片 叙述.", "styles": ["Expressive", "Narrative"], "stylesEn": ["Expressive", "Narrative"], "stylesZh": ["表现力", "叙述"], "voiceName": "English_captivating_female1", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-upbeat-woman", "name": "Upbeat Woman", "nameEn": "Upbeat Woman", "nameZh": "乐观女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An upbeat and bright adult female voice with a general American accent, perfect for energetic and positive messaging.", "descriptionEn": "An upbeat and bright adult female voice with a general American accent, perfect for energetic and positive messaging.", "descriptionZh": "一个upbeat and 明亮的 成年女性的声音 ，带有标准 美式口音, ，非常适合 充满活力的 and positive messaging.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "English_Upbeat_Woman", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-trustworth-man", "name": "Trustworthy Man", "nameEn": "Trustworthy Man", "nameZh": "可信赖男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A trustworthy and resonant adult male voice with a general American accent, conveying sincerity and reliability.", "descriptionEn": "A trustworthy and resonant adult male voice with a general American accent, conveying sincerity and reliability.", "descriptionZh": "一个可信赖的 and 洪亮的 成年男性的声音 ，带有标准 美式口音, ，传达 sincerity and reliability.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "English_Trustworth_Man", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-calmwoman", "name": "Calm Woman", "nameEn": "Calm Woman", "nameZh": "平静女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A calm and soothing adult female voice with a general American accent, excellent for audiobooks and meditation guides.", "descriptionEn": "A calm and soothing adult female voice with a general American accent, excellent for audiobooks and meditation guides.", "descriptionZh": "一个平静的 and 舒缓的 成年女性的声音 ，带有标准 美式口音, 非常适合 有声书s and 冥想 guides.", "styles": ["Gentle", "Narrative"], "stylesEn": ["Gentle", "Narrative"], "stylesZh": ["温和", "叙述"], "voiceName": "English_CalmWoman", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-upsetgirl", "name": "Upset Girl", "nameEn": "Upset Girl", "nameZh": "Upset 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A young adult female voice with a British accent, effectively conveying sadness and distress.", "descriptionEn": "A young adult female voice with a British accent, effectively conveying sadness and distress.", "descriptionZh": "一个young 成年女性的声音 ，带有英式口音, 有效地 ，传达 sadness and distress.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "English_UpsetGirl", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-gentle-voiced-man", "name": "<PERSON>tle-voiced Man", "nameEn": "<PERSON>tle-voiced Man", "nameZh": "温和-voiced 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A gentle and resonant adult male voice with a general American accent, warm and reassuring.", "descriptionEn": "A gentle and resonant adult male voice with a general American accent, warm and reassuring.", "descriptionZh": "一个温和的 and 洪亮的 成年男性的声音 ，带有标准 美式口音, 温暖的 and 令人安心的.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "English_<PERSON><PERSON>-voiced_man", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-whispering-girl", "name": "Whispering girl", "nameEn": "Whispering girl", "nameZh": "Whispering girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "", "descriptionEn": "", "descriptionZh": null, "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "English_Whispering_girl", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-diligent-man", "name": "Diligent Man", "nameEn": "Diligent Man", "nameZh": "Diligent 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A diligent and sincere adult male voice with an Indian accent, conveying honesty and hard work.", "descriptionEn": "A diligent and sincere adult male voice with an Indian accent, conveying honesty and hard work.", "descriptionZh": "一个diligent and sincere 成年男性的声音 with an 印度 accent, ，传达 honesty and hard work.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "English_Diligent_Man", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-graceful-lady", "name": "Graceful Lady", "nameEn": "Graceful Lady", "nameZh": "Graceful 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A graceful and elegant middle-aged female voice with a classic British accent, exuding sophistication.", "descriptionEn": "A graceful and elegant middle-aged female voice with a classic British accent, exuding sophistication.", "descriptionZh": "一个graceful and 优雅的 中年女性的声音 ，带有经典 英式口音, ，散发 sophistication.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "English_Grace<PERSON>_Lady", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-<PERSON><PERSON><PERSON><PERSON>", "name": "Reserved <PERSON> Man", "nameEn": "Reserved <PERSON> Man", "nameZh": "Reserved Young 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A reserved and cold adult male voice with a general American accent, conveying distance and introspection.", "descriptionEn": "A reserved and cold adult male voice with a general American accent, conveying distance and introspection.", "descriptionZh": "一个reserved and cold 成年男性的声音 ，带有标准 美式口音, ，传达 distance and introspection.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "English_ReservedYoungMan", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-playfulgirl", "name": "Playful Girl", "nameEn": "Playful Girl", "nameZh": "Playful 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A playful female youth voice with a general American accent, ideal for cartoons and children's entertainment.", "descriptionEn": "A playful female youth voice with a general American accent, ideal for cartoons and children's entertainment.", "descriptionZh": "一个playful female 年轻人的声音 ，带有标准 美式口音, ，理想用于 cartoons and children's entertainment.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "English_PlayfulGirl", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-manwithdeepvoice", "name": "Man With Deep Voice", "nameEn": "Man With Deep Voice", "nameZh": "男性 With Deep Voice", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An adult male with a deep, commanding voice and a general American accent, projecting authority and strength.", "descriptionEn": "An adult male with a deep, commanding voice and a general American accent, projecting authority and strength.", "descriptionZh": "An 成年男性 with a 深沉的, 威严的 voice and a 标准 美式 accent, ，展现 authority and strength.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "English_ManWithDeepVoice", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-mature<PERSON><PERSON><PERSON>", "name": "Mature Partner", "nameEn": "Mature Partner", "nameZh": "Mature Partner", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A mature, gentle middle-aged male voice with a British accent, suitable for a caring and supportive partner role.", "descriptionEn": "A mature, gentle middle-aged male voice with a British accent, suitable for a caring and supportive partner role.", "descriptionZh": "A mature, 温和的 中年男性 voice ，带有英式口音, ，适合 a caring and supportive partner role.", "styles": ["Gentle", "Serious"], "stylesEn": ["Gentle", "Serious"], "stylesZh": ["温和", "严肃"], "voiceName": "English_MaturePartner", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-friendlyperson", "name": "Friendly Guy", "nameEn": "Friendly Guy", "nameZh": "Friendly 小伙", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A friendly and natural-sounding adult male voice with a general American accent, like an approachable guy-next-door.", "descriptionEn": "A friendly and natural-sounding adult male voice with a general American accent, like an approachable guy-next-door.", "descriptionZh": "一个友好的 and natural-sounding 成年男性的声音 ，带有标准 美式口音, like an approachable guy-next-door.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "English_<PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-matureboss", "name": "Bossy <PERSON>", "nameEn": "Bossy <PERSON>", "nameZh": "Bossy <PERSON>", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A mature, middle-aged female voice with a general American accent, conveying authority and a commanding presence.", "descriptionEn": "A mature, middle-aged female voice with a general American accent, conveying authority and a commanding presence.", "descriptionZh": "A mature, 中年女性 voice ，带有标准 美式口音, ，传达 authority and a 威严的 presence.", "styles": ["Authoritative", "Serious"], "stylesEn": ["Authoritative", "Serious"], "stylesZh": ["权威", "严肃"], "voiceName": "English_MatureBoss", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-debator", "name": "Male Debater", "nameEn": "Male Debater", "nameZh": "Male Debater", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A tough, middle-aged male voice with a general American accent, perfect for debates and assertive arguments.", "descriptionEn": "A tough, middle-aged male voice with a general American accent, perfect for debates and assertive arguments.", "descriptionZh": "A tough, 中年男性 voice ，带有标准 美式口音, ，非常适合 debates and assertive arguments.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "English_Debator", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-lovelygirl", "name": "Lovely Girl", "nameEn": "Lovely Girl", "nameZh": "Lovely 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A lovely and sweet female youth voice with a British accent, full of charm and innocence.", "descriptionEn": "A lovely and sweet female youth voice with a British accent, full of charm and innocence.", "descriptionZh": "一个lovely and sweet female 年轻人的声音 ，带有英式口音, full of charm and innocence.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "English_LovelyGirl", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-steadymentor", "name": "Reliable Man", "nameEn": "Reliable Man", "nameZh": "Reliable 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A young adult male voice with a general American accent, projecting an air of arrogant reliability.", "descriptionEn": "A young adult male voice with a general American accent, projecting an air of arrogant reliability.", "descriptionZh": "一个young 成年男性的声音 ，带有标准 美式口音, ，展现 an air of arrogant reliability.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "English_Steadymentor", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-deep-voiced<PERSON><PERSON><PERSON>", "name": "Deep-voiced Gentleman", "nameEn": "Deep-voiced Gentleman", "nameZh": "Deep-voiced 温和man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A deep-voiced and wise adult male gentleman with a classic British accent, sounding experienced and thoughtful.", "descriptionEn": "A deep-voiced and wise adult male gentleman with a classic British accent, sounding experienced and thoughtful.", "descriptionZh": "A 深沉的-voiced and wise 成年男性 温和的man ，带有经典 英式口音, sounding experienced and thoughtful.", "styles": ["Gentle", "Serious"], "stylesEn": ["Gentle", "Serious"], "stylesZh": ["温和", "严肃"], "voiceName": "English_Deep-<PERSON><PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-wiselady", "name": "<PERSON> Lady", "nameEn": "<PERSON> Lady", "nameZh": "智慧 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A wise and genial middle-aged female voice with a British accent, offering kind and insightful words.", "descriptionEn": "A wise and genial middle-aged female voice with a British accent, offering kind and insightful words.", "descriptionZh": "一个wise and genial 中年女性的声音 ，带有英式口音, 提供 kind and insightful words.", "styles": ["Gentle", "Serious"], "stylesEn": ["Gentle", "Serious"], "stylesZh": ["温和", "严肃"], "voiceName": "English_Wiselady", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-captivat<PERSON><PERSON><PERSON>ler", "name": "Captivating Storyteller", "nameEn": "Captivating Storyteller", "nameZh": "Captivating Storyteller", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A captivating senior male storyteller with a cold, detached tone and a general American accent.", "descriptionEn": "A captivating senior male storyteller with a cold, detached tone and a general American accent.", "descriptionZh": "A 迷人的 老年男性 storyteller with a cold, detached tone and a 标准 美式 accent.", "styles": ["Expressive", "Serious", "Narrative"], "stylesEn": ["Expressive", "Serious", "Narrative"], "stylesZh": ["表现力", "严肃", "叙述"], "voiceName": "English_CaptivatingStoryteller", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-<PERSON><PERSON><PERSON><PERSON>", "name": "Decent Young Man", "nameEn": "Decent Young Man", "nameZh": "Decent Young 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A decent and respectable adult male voice with a British accent, sounding polite and well-mannered.", "descriptionEn": "A decent and respectable adult male voice with a British accent, sounding polite and well-mannered.", "descriptionZh": "一个decent and respectable 成年男性的声音 ，带有英式口音, sounding polite and well-mannered.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "English_DecentYoungMan", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-sentimentallady", "name": "Sentimental Lady", "nameEn": "Sentimental Lady", "nameZh": "Sentimental 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A sentimental and elegant middle-aged female voice with a British accent, perfect for nostalgic or emotional readings.", "descriptionEn": "A sentimental and elegant middle-aged female voice with a British accent, perfect for nostalgic or emotional readings.", "descriptionZh": "一个sentimental and 优雅的 中年女性的声音 ，带有英式口音, ，非常适合 nostalgic or emotional readings.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "English_SentimentalLady", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-<PERSON><PERSON><PERSON>", "name": "Imposing Queen", "nameEn": "Imposing Queen", "nameZh": "Imposing Queen", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "The imposing voice of an adult queen with a powerful British accent, commanding respect and authority.", "descriptionEn": "The imposing voice of an adult queen with a powerful British accent, commanding respect and authority.", "descriptionZh": "The imposing voice of an adult queen ，带有powerful 英式口音, 威严的 respect and authority.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "English_ImposingManner", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-sadteen", "name": "Teen Boy", "nameEn": "Teen Boy", "nameZh": "Teen 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A frustrated young adult male voice with a British accent, perfect for a teen character expressing annoyance.", "descriptionEn": "A frustrated young adult male voice with a British accent, perfect for a teen character expressing annoyance.", "descriptionZh": "一个frustrated young 成年男性的声音 ，带有英式口音, ，非常适合 a teen character expressing annoyance.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "English_SadTeen", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-passionatewarrior", "name": "Passionate Warrior", "nameEn": "Passionate Warrior", "nameZh": "Passionate Warrior", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An energetic and passionate young adult male warrior voice with a general American accent, ready for battle.", "descriptionEn": "An energetic and passionate young adult male warrior voice with a general American accent, ready for battle.", "descriptionZh": "一个充满活力的 and passionate young 成年男性 warrior的声音 ，带有标准 美式口音, ready for battle.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "English_PassionateWarrior", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-wisescholar", "name": "Wise Scholar", "nameEn": "Wise Scholar", "nameZh": "智慧 Scholar", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A wise, conversational young adult scholar with a British accent, making complex topics accessible and engaging.", "descriptionEn": "A wise, conversational young adult scholar with a British accent, making complex topics accessible and engaging.", "descriptionZh": "A wise, conversational young adult scholar ，带有英式口音, making complex topics accessible and 引人入胜的.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "English_WiseScholar", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-soft-spokengirl", "name": "Soft-Spoken Girl", "nameEn": "Soft-Spoken Girl", "nameZh": "Soft-Spoken 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An adorable, soft-spoken female youth voice with a general American accent, gentle and sweet.", "descriptionEn": "An adorable, soft-spoken female youth voice with a general American accent, gentle and sweet.", "descriptionZh": "An adorable, soft-spoken female 年轻人 voice ，带有标准 美式口音, 温和的 and sweet.", "styles": ["Gentle", "Playful"], "stylesEn": ["Gentle", "Playful"], "stylesZh": ["温和", "活泼"], "voiceName": "English_Soft-spokenGirl", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-serenewoman", "name": "<PERSON><PERSON>", "nameEn": "<PERSON><PERSON>", "nameZh": "<PERSON><PERSON> 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A serene and friendly young adult female voice with a general American accent, calm and welcoming.", "descriptionEn": "A serene and friendly young adult female voice with a general American accent, calm and welcoming.", "descriptionZh": "一个serene and 友好的 young 成年女性的声音 ，带有标准 美式口音, 平静的 and welcoming.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "English_SereneWoman", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-confidentwoman", "name": "Confident Woman", "nameEn": "Confident Woman", "nameZh": "Confident 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A confident and firm young adult female voice with a general American accent, assertive and clear.", "descriptionEn": "A confident and firm young adult female voice with a general American accent, assertive and clear.", "descriptionZh": "一个confident and firm young 成年女性的声音 ，带有标准 美式口音, assertive and clear.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "English_ConfidentWoman", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-patientman", "name": "Patient Man", "nameEn": "Patient Man", "nameZh": "Patient 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A patient adult male voice with a British accent, speaking in a calm and understanding manner.", "descriptionEn": "A patient adult male voice with a British accent, speaking in a calm and understanding manner.", "descriptionZh": "一个patient 成年男性的声音 ，带有英式口音, speaking in a 平静的 and understanding manner.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "English_PatientMan", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-comedian", "name": "Comedian", "nameEn": "Comedian", "nameZh": "Comedian", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A breezy young adult male comedian with a British accent, delivering lines with a light and humorous touch.", "descriptionEn": "A breezy young adult male comedian with a British accent, delivering lines with a light and humorous touch.", "descriptionZh": "A breezy young 成年男性 comedian ，带有英式口音, delivering lines with a light and humorous touch.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "English_Comedian", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-boss<PERSON><PERSON>", "name": "Bossy Leader", "nameEn": "Bossy Leader", "nameZh": "Bossy Leader", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A bossy adult male leader with a general American accent, speaking unconcernedly with an air of command.", "descriptionEn": "A bossy adult male leader with a general American accent, speaking unconcernedly with an air of command.", "descriptionZh": "A bossy 成年男性 leader ，带有标准 美式口音, speaking unconcernedly with an air of command.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "English_BossyLeader", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-strong-willedboy", "name": "Strong-Willed Boy", "nameEn": "Strong-Willed Boy", "nameZh": "Strong-Willed 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A mature-sounding and strong-willed young adult male with a British accent, showing determination beyond his years.", "descriptionEn": "A mature-sounding and strong-willed young adult male with a British accent, showing determination beyond his years.", "descriptionZh": "A mature-sounding and strong-willed young 成年男性 ，带有英式口音, showing determination beyond his years.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "English_Strong-<PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-stressedlady", "name": "Stressed Lady", "nameEn": "Stressed Lady", "nameZh": "Stressed 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An unsure, stressed middle-aged female voice with a general American accent, conveying anxiety and uncertainty.", "descriptionEn": "An unsure, stressed middle-aged female voice with a general American accent, conveying anxiety and uncertainty.", "descriptionZh": "An unsure, stressed 中年女性 voice ，带有标准 美式口音, ，传达 anxiety and uncertainty.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "English_StressedLady", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-assertivequeen", "name": "Assertive Queen", "nameEn": "Assertive Queen", "nameZh": "Assertive Queen", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An assertive yet guarded young adult queen with a general American accent, projecting authority while remaining cautious.", "descriptionEn": "An assertive yet guarded young adult queen with a general American accent, projecting authority while remaining cautious.", "descriptionZh": "An assertive yet guarded young adult queen ，带有标准 美式口音, ，展现 authority while remaining cautious.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "English_AssertiveQueen", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-animecharacter", "name": "Female Narrator", "nameEn": "Female Narrator", "nameZh": "Female 叙述者", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A sincere middle-aged female narrator with a British accent, perfect for trustworthy and heartfelt storytelling.", "descriptionEn": "A sincere middle-aged female narrator with a British accent, perfect for trustworthy and heartfelt storytelling.", "descriptionZh": "A sincere 中年女性 narrator ，带有英式口音, ，非常适合 可信赖的 and heartfelt 讲故事.", "styles": ["Authoritative", "Narrative"], "stylesEn": ["Authoritative", "Narrative"], "stylesZh": ["权威", "叙述"], "voiceName": "English_AnimeCharacter", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-jovialman", "name": "Jovial Man", "nameEn": "Jovial Man", "nameZh": "Jovial 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A jovial and mature middle-aged male voice with a general American accent, cheerful and good-natured.", "descriptionEn": "A jovial and mature middle-aged male voice with a general American accent, cheerful and good-natured.", "descriptionZh": "一个jovial and mature 中年男性的声音 ，带有标准 美式口音, 愉快的 and good-natured.", "styles": ["Energetic", "Playful", "Serious"], "stylesEn": ["Energetic", "Playful", "Serious"], "stylesZh": ["活力", "活泼", "严肃"], "voiceName": "English_<PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-whimsicalgirl", "name": "Whimsical Girl", "nameEn": "Whimsical Girl", "nameZh": "Whimsical 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A whimsical yet wary young adult female voice with a general American accent, combining playfulness with caution.", "descriptionEn": "A whimsical yet wary young adult female voice with a general American accent, combining playfulness with caution.", "descriptionZh": "一个whimsical yet wary young 成年女性的声音 ，带有标准 美式口音, 结合 playfulness with caution.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "English_WhimsicalGirl", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "english-kind-heartedgirl", "name": "Kind-Hearted Girl", "nameEn": "Kind-Hearted Girl", "nameZh": "善良-Hearted 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A kind-hearted and calm young adult female with a general American accent, speaking with gentle warmth.", "descriptionEn": "A kind-hearted and calm young adult female with a general American accent, speaking with gentle warmth.", "descriptionZh": "A kind-hearted and 平静的 young 成年女性 ，带有标准 美式口音, 以...说话 温和的 温暖的th.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "English_Kind-<PERSON><PERSON><PERSON>l", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "chinese--mandarin--reliable-executive", "name": "Reliable Executive", "nameEn": "Reliable Executive", "nameZh": "Reliable Executive", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A steady and reliable middle-aged male executive voice in Standard Mandarin, conveying trustworthiness.", "descriptionEn": "A steady and reliable middle-aged male executive voice in Standard Mandarin, conveying trustworthiness.", "descriptionZh": "一个steady and reliable 中年男性 executive的声音 in 标准普通话, ，传达 trustworthiness.", "styles": ["Professional", "Authoritative"], "stylesEn": ["Professional", "Authoritative"], "stylesZh": ["专业", "权威"], "voiceName": "Chinese (Mandarin)_Reliable_Executive", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--news-anchor", "name": "News Anchor", "nameEn": "News Anchor", "nameZh": "新闻主播", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A professional, broadcaster-like middle-aged female news anchor in Standard Mandarin.", "descriptionEn": "A professional, broadcaster-like middle-aged female news anchor in Standard Mandarin.", "descriptionZh": "A 专业的, 广播er-like 中年女性 news anchor in 标准普通话.", "styles": ["Professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Chinese (Mandarin)_News_Anchor", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--unrestrained-young-man", "name": "Unrestrained <PERSON> Man", "nameEn": "Unrestrained <PERSON> Man", "nameZh": "Unrestrained Young 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An unrestrained and free-spirited adult male voice in Standard Mandarin.", "descriptionEn": "An unrestrained and free-spirited adult male voice in Standard Mandarin.", "descriptionZh": "一个unrestrained and free-spirited 成年男性的声音 in 标准普通话.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Chinese (Mandarin)_Unrestrained_Young_Man", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--mature-woman", "name": "Mature Woman", "nameEn": "Mature Woman", "nameZh": "Mature 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A charming and mature adult female voice in Standard Mandarin.", "descriptionEn": "A charming and mature adult female voice in Standard Mandarin.", "descriptionZh": "一个charming and mature 成年女性的声音 in 标准普通话.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Chinese (Mandarin)_Mature_Woman", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "arrogant-miss", "name": "Arrogant Miss", "nameEn": "Arrogant Miss", "nameZh": "Arrogant Miss", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An arrogant adult female voice in Standard Mandarin, projecting confidence and superiority.", "descriptionEn": "An arrogant adult female voice in Standard Mandarin, projecting confidence and superiority.", "descriptionZh": "一个arrogant 成年女性的声音 in 标准普通话, ，展现 confidence and superiority.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Arrogant_Miss", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "robot-armor", "name": "Robot Armor", "nameEn": "Robot Armor", "nameZh": "Robot Armor", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An electronic, robotic adult male voice, suitable for sci-fi or futuristic content in Standard Mandarin.", "descriptionEn": "An electronic, robotic adult male voice, suitable for sci-fi or futuristic content in Standard Mandarin.", "descriptionZh": "An electronic, robotic 成年男性 voice, ，适合 sci-fi or futuristic content in 标准普通话.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Robot_Armor", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "chinese--mandarin--kind-hearted-antie", "name": "Kind-hearted <PERSON><PERSON>", "nameEn": "Kind-hearted <PERSON><PERSON>", "nameZh": "善良-hearted <PERSON><PERSON>", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A gentle and kind-hearted middle-aged \"antie\" voice in Standard Mandarin, warm and caring.", "descriptionEn": "A gentle and kind-hearted middle-aged \"antie\" voice in Standard Mandarin, warm and caring.", "descriptionZh": "一个温和的 and kind-hearted middle-aged \"antie\"的声音 in 标准普通话, 温暖的 and caring.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Kind-hearted_<PERSON>e", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--hk-flight-attendant", "name": "HK Flight Attendant", "nameEn": "HK Flight Attendant", "nameZh": "HK Flight Attendant", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A polite middle-aged female flight attendant with a Southern Chinese accent, clear and courteous.", "descriptionEn": "A polite middle-aged female flight attendant with a Southern Chinese accent, clear and courteous.", "descriptionZh": "A polite 中年女性 flight attendant ，带有南方中文口音, clear and courteous.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Chinese (Mandarin)_HK_Flight_Attendant", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--humorous-elder", "name": "Humorous Elder", "nameEn": "Humorous Elder", "nameZh": "Humorous Elder", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A refreshing and humorous senior male voice with a Northern Chinese accent, full of character.", "descriptionEn": "A refreshing and humorous senior male voice with a Northern Chinese accent, full of character.", "descriptionZh": "一个refreshing and humorous 老年男性的声音 ，带有Northern Chinese口音, full of character.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Chinese (Mandarin)_Humorous_Elder", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--gentleman", "name": "Gentleman", "nameEn": "Gentleman", "nameZh": "温和man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A magnetic and charismatic adult male gentleman in Standard Mandarin.", "descriptionEn": "A magnetic and charismatic adult male gentleman in Standard Mandarin.", "descriptionZh": "A 磁性的 and charismatic 成年男性 温和的man in 标准普通话.", "styles": ["Gentle", "Expressive"], "stylesEn": ["Gentle", "Expressive"], "stylesZh": ["温和", "表现力"], "voiceName": "Chinese (Mandarin)_Gentleman", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--warm-bestie", "name": "Warm Bestie", "nameEn": "Warm Bestie", "nameZh": "温暖 Bestie", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A warm and crisp adult female \"bestie\" voice in Standard Mandarin, friendly and clear.", "descriptionEn": "A warm and crisp adult female \"bestie\" voice in Standard Mandarin, friendly and clear.", "descriptionZh": "一个温暖的 and crisp 成年女性 \"bestie\"的声音 in 标准普通话, 友好的 and clear.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Warm_<PERSON>ie", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--stubborn-friend", "name": "<PERSON><PERSON><PERSON> Friend", "nameEn": "<PERSON><PERSON><PERSON> Friend", "nameZh": "<PERSON><PERSON><PERSON> Friend", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An uninhibited and stubborn adult male friend's voice in Standard Mandarin.", "descriptionEn": "An uninhibited and stubborn adult male friend's voice in Standard Mandarin.", "descriptionZh": "一个uninhibited and stubborn 成年男性 friend's的声音 in 标准普通话.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Chinese (Mandarin)_<PERSON><PERSON><PERSON>_Friend", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--sweet-lady", "name": "Sweet Lady", "nameEn": "Sweet Lady", "nameZh": "甜美 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A tender and sweet adult female voice in Standard Mandarin.", "descriptionEn": "A tender and sweet adult female voice in Standard Mandarin.", "descriptionZh": "一个tender and sweet 成年女性的声音 in 标准普通话.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Chinese (Mandarin)_Sweet_Lady", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--southern-young-man", "name": "Southern Young Man", "nameEn": "Southern Young Man", "nameZh": "Southern Young 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An earnest adult male voice with a Southern Chinese accent.", "descriptionEn": "An earnest adult male voice with a Southern Chinese accent.", "descriptionZh": "一个earnest 成年男性的声音 ，带有南方中文口音.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Chinese (Mandarin)_Southern_Young_Man", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--wise-women", "name": "Wise Women", "nameEn": "Wise Women", "nameZh": "智慧 Women", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A lyrical and wise middle-aged female voice in Standard Mandarin.", "descriptionEn": "A lyrical and wise middle-aged female voice in Standard Mandarin.", "descriptionZh": "一个lyrical and wise 中年女性的声音 in 标准普通话.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Chinese (Mandarin)_Wise_Women", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--gentle-youth", "name": "Gentle Youth", "nameEn": "Gentle Youth", "nameZh": "温和 Youth", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A gentle adult male youth voice in Standard Mandarin.", "descriptionEn": "A gentle adult male youth voice in Standard Mandarin.", "descriptionZh": "一个温和的 成年男性 年轻人的声音 in 标准普通话.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Gentle_Youth", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--warm-girl", "name": "Warm Girl", "nameEn": "Warm Girl", "nameZh": "温暖 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A soft and warm young adult female voice in Standard Mandarin.", "descriptionEn": "A soft and warm young adult female voice in Standard Mandarin.", "descriptionZh": "一个soft and 温暖的 young 成年女性的声音 in 标准普通话.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Warm_Girl", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--male-announcer", "name": "Male Announcer", "nameEn": "Male Announcer", "nameZh": "Male Announcer", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A magnetic middle-aged male announcer voice in Standard Mandarin, clear and authoritative.", "descriptionEn": "A magnetic middle-aged male announcer voice in Standard Mandarin, clear and authoritative.", "descriptionZh": "一个磁性的 中年男性 announcer的声音 in 标准普通话, clear and 权威的.", "styles": ["Authoritative", "Expressive"], "stylesEn": ["Authoritative", "Expressive"], "stylesZh": ["权威", "表现力"], "voiceName": "Chinese (Mandarin)_Male_Announcer", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--kind-hearted-elder", "name": "Kind-hearted Elder", "nameEn": "Kind-hearted Elder", "nameZh": "善良-hearted Elder", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A kind and gentle senior female voice in Standard Mandarin.", "descriptionEn": "A kind and gentle senior female voice in Standard Mandarin.", "descriptionZh": "一个kind and 温和的 老年女性的声音 in 标准普通话.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Kind-hearted_Elder", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--cute-spirit", "name": "Cute Spirit", "nameEn": "Cute Spirit", "nameZh": "Cute Spirit", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An adorable and cute female spirit voice, youthful and sweet, in Standard Mandarin.", "descriptionEn": "An adorable and cute female spirit voice, youthful and sweet, in Standard Mandarin.", "descriptionZh": "一个adorable and cute female spirit的声音, 年轻人ful and sweet, in 标准普通话.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Chinese (Mandarin)_Cute_Spirit", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--radio-host", "name": "Radio Host", "nameEn": "Radio Host", "nameZh": "Radio 主持人", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A poetic adult male radio host in Standard Mandarin, with a smooth and engaging delivery.", "descriptionEn": "A poetic adult male radio host in Standard Mandarin, with a smooth and engaging delivery.", "descriptionZh": "A poetic 成年男性 radio host in 标准普通话, with a smooth and 引人入胜的 delivery.", "styles": ["Professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Chinese (Mandarin)_Radio_Host", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--lyrical-voice", "name": "Lyrical Voice", "nameEn": "Lyrical Voice", "nameZh": "Lyrical Voice", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A mellow and lyrical adult male voice in Standard Mandarin, smooth and expressive.", "descriptionEn": "A mellow and lyrical adult male voice in Standard Mandarin, smooth and expressive.", "descriptionZh": "一个mellow and lyrical 成年男性的声音 in 标准普通话, smooth and 富有表现力的.", "styles": ["Expressive"], "stylesEn": ["Expressive"], "stylesZh": ["表现力"], "voiceName": "Chinese (Mandarin)_Lyrical_Voice", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--straightforward-boy", "name": "Straightforward Boy", "nameEn": "Straightforward Boy", "nameZh": "Straightforward 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A thoughtful and straightforward young adult male voice in Standard Mandarin.", "descriptionEn": "A thoughtful and straightforward young adult male voice in Standard Mandarin.", "descriptionZh": "一个thoughtful and straightforward young 成年男性的声音 in 标准普通话.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Chinese (Mandarin)_Straightforward_Boy", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--sincere-adult", "name": "Sincere Adult", "nameEn": "Sincere Adult", "nameZh": "Sincere Adult", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A sincere and encouraging adult male voice in Standard Mandarin.", "descriptionEn": "A sincere and encouraging adult male voice in Standard Mandarin.", "descriptionZh": "一个sincere and encouraging 成年男性的声音 in 标准普通话.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Chinese (Mandarin)_Sincere_Adult", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--gentle-senior", "name": "Gentle Senior", "nameEn": "Gentle Senior", "nameZh": "温和 Senior", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A gentle and cozy adult female senior voice in Standard Mandarin.", "descriptionEn": "A gentle and cozy adult female senior voice in Standard Mandarin.", "descriptionZh": "一个温和的 and cozy 成年女性 senior的声音 in 标准普通话.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Gentle_Senior", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--crisp-girl", "name": "Crisp Girl", "nameEn": "Crisp Girl", "nameZh": "Crisp 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A warm and crisp young adult female voice in Standard Mandarin.", "descriptionEn": "A warm and crisp young adult female voice in Standard Mandarin.", "descriptionZh": "一个温暖的 and crisp young 成年女性的声音 in 标准普通话.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Crisp_Girl", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--pure-hearted-boy", "name": "Pure-hearted Boy", "nameEn": "Pure-hearted Boy", "nameZh": "Pure-hearted 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A committed and pure-hearted young adult male voice in Standard Mandarin.", "descriptionEn": "A committed and pure-hearted young adult male voice in Standard Mandarin.", "descriptionZh": "一个committed and pure-hearted young 成年男性的声音 in 标准普通话.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Chinese (Mandarin)_Pure-hearted_Boy", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--soft-girl", "name": "Soft Girl", "nameEn": "Soft Girl", "nameZh": "Soft 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A welcoming and soft adult female voice with a Southern Chinese accent.", "descriptionEn": "A welcoming and soft adult female voice with a Southern Chinese accent.", "descriptionZh": "一个welcoming and soft 成年女性的声音 ，带有南方中文口音.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Soft_Girl", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--intellectualgirl", "name": "Intellectual Girl", "nameEn": "Intellectual Girl", "nameZh": "Intellectual 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An intellectual adult female voice in Standard Mandarin, clear and knowledgeable.", "descriptionEn": "An intellectual adult female voice in Standard Mandarin, clear and knowledgeable.", "descriptionZh": "一个intellectual 成年女性的声音 in 标准普通话, clear and knowledgeable.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Chinese (Mandarin)_IntellectualGirl", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--warm-heartedgirl", "name": "Warm-hearted Girl", "nameEn": "Warm-hearted Girl", "nameZh": "温暖-hearted 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A warm-hearted and caring adult female voice in Standard Mandarin.", "descriptionEn": "A warm-hearted and caring adult female voice in Standard Mandarin.", "descriptionZh": "一个温暖的-hearted and caring 成年女性的声音 in 标准普通话.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Warm_HeartedGirl", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--laid-backgirl", "name": "Laid-back Girl", "nameEn": "Laid-back Girl", "nameZh": "Laid-back 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A relaxed and laid-back adult female voice in Standard Mandarin.", "descriptionEn": "A relaxed and laid-back adult female voice in Standard Mandarin.", "descriptionZh": "一个relaxed and laid-back 成年女性的声音 in 标准普通话.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Chinese (Mandarin)_Laid_BackGirl", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--explorativegirl", "name": "Explorative Girl", "nameEn": "Explorative Girl", "nameZh": "Explorative 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An explorative and curious adult female voice in Standard Mandarin.", "descriptionEn": "An explorative and curious adult female voice in Standard Mandarin.", "descriptionZh": "一个explorative and curious 成年女性的声音 in 标准普通话.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Chinese (Mandarin)_ExplorativeGirl", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--warm-heartedaunt", "name": "Warm-hearted Aunt", "nameEn": "Warm-hearted Aunt", "nameZh": "温暖-hearted Aunt", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A kind and warm-hearted middle-aged auntie voice in Standard Mandarin.", "descriptionEn": "A kind and warm-hearted middle-aged auntie voice in Standard Mandarin.", "descriptionZh": "一个kind and 温暖的-hearted middle-aged auntie的声音 in 标准普通话.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Warm-HeartedAunt", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "chinese--mandarin--bashfulgirl", "name": "Bashful Girl", "nameEn": "Bashful Girl", "nameZh": "Bashful 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A bashful and shy female youth voice in Standard Mandarin.", "descriptionEn": "A bashful and shy female youth voice in Standard Mandarin.", "descriptionZh": "一个bashful and shy female 年轻人的声音 in 标准普通话.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Chinese (Mandarin)_BashfulGirl", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "你好，这是一个文本转语音演示的示例声音。", "sampleUrl": null}]}, {"slug": "japanese-<PERSON><PERSON><PERSON>", "name": "Intellectual Senior", "nameEn": "Intellectual Senior", "nameZh": "Intellectual Senior", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A mature and intellectual young adult male voice in Japanese, sounding older than his age.", "descriptionEn": "A mature and intellectual young adult male voice in Japanese, sounding older than his age.", "descriptionZh": "一个mature and intellectual young 成年男性的声音 in Japanese, sounding older than his age.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Japanese_IntellectualSenior", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "japanese-decisive<PERSON>rin<PERSON>", "name": "Decisive Princess", "nameEn": "Decisive Princess", "nameZh": "Decisive Princess", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A firm and decisive adult princess's voice in Japanese.", "descriptionEn": "A firm and decisive adult princess's voice in Japanese.", "descriptionZh": "一个firm and decisive adult princess's的声音 in Japanese.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Japanese_DecisivePrincess", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "japanese-loyalknight", "name": "<PERSON><PERSON>", "nameEn": "<PERSON><PERSON>", "nameZh": "<PERSON><PERSON>", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A youthful and loyal adult male knight's voice in Japanese.", "descriptionEn": "A youthful and loyal adult male knight's voice in Japanese.", "descriptionZh": "一个年轻人ful and loyal 成年男性 knight's的声音 in Japanese.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Japanese_LoyalKnight", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "japanese-dominantman", "name": "Dominant Man", "nameEn": "Dominant Man", "nameZh": "Dominant 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A mature and dominant middle-aged male voice in Japanese.", "descriptionEn": "A mature and dominant middle-aged male voice in Japanese.", "descriptionZh": "一个mature and dominant 中年男性的声音 in Japanese.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Japanese_DominantMan", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "japanese-<PERSON><PERSON>mander", "name": "Serious Commander", "nameEn": "Serious Commander", "nameZh": "Serious Commander", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A serious and reliable adult male commander's voice in Japanese.", "descriptionEn": "A serious and reliable adult male commander's voice in Japanese.", "descriptionZh": "一个serious and reliable 成年男性 commander's的声音 in Japanese.", "styles": ["Authoritative", "Serious"], "stylesEn": ["Authoritative", "Serious"], "stylesZh": ["权威", "严肃"], "voiceName": "Japanese_SeriousCommander", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "japanese-coldqueen", "name": "Cold Queen", "nameEn": "Cold Queen", "nameZh": "Cold Queen", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A distant and cold adult queen's voice in Japanese.", "descriptionEn": "A distant and cold adult queen's voice in Japanese.", "descriptionZh": "一个distant and cold adult queen's的声音 in Japanese.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Japanese_ColdQueen", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "japanese-dependablewoman", "name": "Dependable Woman", "nameEn": "Dependable Woman", "nameZh": "Dependable 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A steady and dependable adult female voice in Japanese.", "descriptionEn": "A steady and dependable adult female voice in Japanese.", "descriptionZh": "一个steady and dependable 成年女性的声音 in Japanese.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Japanese_DependableWoman", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "japanese-gentlebutler", "name": "Gen<PERSON> Butler", "nameEn": "Gen<PERSON> Butler", "nameZh": "温和管家", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A charming and gentle adult male butler's voice in Japanese.", "descriptionEn": "A charming and gentle adult male butler's voice in Japanese.", "descriptionZh": "一个charming and 温和的 成年男性 butler's的声音 in Japanese.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Japanese_GentleButler", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "japanese-kindlady", "name": "Kind Lady", "nameEn": "Kind Lady", "nameZh": "善良女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A charming and kind adult female voice in Japanese.", "descriptionEn": "A charming and kind adult female voice in Japanese.", "descriptionZh": "一个charming and kind 成年女性的声音 in Japanese.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Japanese_KindLady", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "japanese-calmlady", "name": "Calm Lady", "nameEn": "Calm Lady", "nameZh": "平静 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A calm and charming adult female voice in Japanese.", "descriptionEn": "A calm and charming adult female voice in Japanese.", "descriptionZh": "一个平静的 and charming 成年女性的声音 in Japanese.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Japanese_CalmLady", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "japanese-optimisticyouth", "name": "Optimistic Youth", "nameEn": "Optimistic Youth", "nameZh": "Optimistic Youth", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A cheerful and optimistic adult male youth's voice in Japanese.", "descriptionEn": "A cheerful and optimistic adult male youth's voice in Japanese.", "descriptionZh": "一个愉快的 and optimistic 成年男性 年轻人's的声音 in Japanese.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Japanese_OptimisticYouth", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "japanese-generousizakayaowner", "name": "Generous Izakaya Owner", "nameEn": "Generous Izakaya Owner", "nameZh": "Generous Izakaya Owner", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A playful and generous middle-aged male izakaya owner's voice in Standard Japanese.", "descriptionEn": "A playful and generous middle-aged male izakaya owner's voice in Standard Japanese.", "descriptionZh": "一个playful and generous 中年男性 izakaya owner's的声音 in 标准日语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Japanese_GenerousIzakayaOwner", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "japanese-sportystudent", "name": "Sporty Student", "nameEn": "Sporty Student", "nameZh": "Sporty Student", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An inviting and sporty adult male student's voice in Standard Japanese.", "descriptionEn": "An inviting and sporty adult male student's voice in Standard Japanese.", "descriptionZh": "一个inviting and sporty 成年男性 student's的声音 in 标准日语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Japanese_SportyStudent", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "japanese-innocentboy", "name": "Innocent Boy", "nameEn": "Innocent Boy", "nameZh": "<PERSON> 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An inviting and innocent adult male voice in Standard Japanese.", "descriptionEn": "An inviting and innocent adult male voice in Standard Japanese.", "descriptionZh": "一个inviting and innocent 成年男性的声音 in 标准日语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Japanese_Innocent<PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "japanese-gracefulmaiden", "name": "Graceful Maiden", "nameEn": "Graceful Maiden", "nameZh": "Graceful Maiden", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A sweet and graceful adult maiden's voice in Standard Japanese.", "descriptionEn": "A sweet and graceful adult maiden's voice in Standard Japanese.", "descriptionZh": "一个sweet and graceful adult maiden's的声音 in 标准日语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Japanese_GracefulMaiden", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "standard", "isDefault": true, "sampleText": "こんにちは、これはテキスト読み上げのデモンストレーション用のサンプル音声です。", "sampleUrl": null}]}, {"slug": "cantonese-professionalhost-f-", "name": "Professional Female Host", "nameEn": "Professional Female Host", "nameZh": "专业 Female 主持人", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A neutral and professional adult female host in Cantonese.", "descriptionEn": "A neutral and professional adult female host in Cantonese.", "descriptionZh": "A neutral and 专业的 成年女性 host in Cantonese.", "styles": ["Professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Cantonese_ProfessionalHost（F)", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "standard", "isDefault": true, "sampleText": "你好，呢個係文字轉語音示範嘅樣本聲音。", "sampleUrl": null}]}, {"slug": "cantonese-gentlelady", "name": "Gentle Lady", "nameEn": "Gentle Lady", "nameZh": "温和 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A calm and gentle adult female voice in Cantonese.", "descriptionEn": "A calm and gentle adult female voice in Cantonese.", "descriptionZh": "一个平静的 and 温和的 成年女性的声音 in Cantonese.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Cantonese_GentleLady", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "standard", "isDefault": true, "sampleText": "你好，呢個係文字轉語音示範嘅樣本聲音。", "sampleUrl": null}]}, {"slug": "cantonese-professionalhost-m-", "name": "Professional Male Host", "nameEn": "Professional Male Host", "nameZh": "专业 Male 主持人", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A neutral and professional adult male host in Cantonese.", "descriptionEn": "A neutral and professional adult male host in Cantonese.", "descriptionZh": "A neutral and 专业的 成年男性 host in Cantonese.", "styles": ["Professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Cantonese_ProfessionalHost（M)", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "standard", "isDefault": true, "sampleText": "你好，呢個係文字轉語音示範嘅樣本聲音。", "sampleUrl": null}]}, {"slug": "cantonese-playfulman", "name": "Playful Man", "nameEn": "Playful Man", "nameZh": "Playful 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A soulful and playful adult male voice in Cantonese.", "descriptionEn": "A soulful and playful adult male voice in Cantonese.", "descriptionZh": "一个soulful and playful 成年男性的声音 in Cantonese.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Cantonese_PlayfulMan", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "standard", "isDefault": true, "sampleText": "你好，呢個係文字轉語音示範嘅樣本聲音。", "sampleUrl": null}]}, {"slug": "cantonese-cutegirl", "name": "Cute Girl", "nameEn": "Cute Girl", "nameZh": "Cute 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A soothing and cute young adult female voice in Cantonese.", "descriptionEn": "A soothing and cute young adult female voice in Cantonese.", "descriptionZh": "一个舒缓的 and cute young 成年女性的声音 in Cantonese.", "styles": ["Gentle", "Playful"], "stylesEn": ["Gentle", "Playful"], "stylesZh": ["温和", "活泼"], "voiceName": "Cantonese_CuteGirl", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "standard", "isDefault": true, "sampleText": "你好，呢個係文字轉語音示範嘅樣本聲音。", "sampleUrl": null}]}, {"slug": "cantonese-kindwoman", "name": "Kind Woman", "nameEn": "Kind Woman", "nameZh": "善良 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A friendly and kind adult female voice in Cantonese.", "descriptionEn": "A friendly and kind adult female voice in Cantonese.", "descriptionZh": "一个友好的 and kind 成年女性的声音 in Cantonese.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Cantonese_KindWoman", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "standard", "isDefault": true, "sampleText": "你好，呢個係文字轉語音示範嘅樣本聲音。", "sampleUrl": null}]}, {"slug": "korean-airheadedgirl", "name": "Airheaded Girl", "nameEn": "Airheaded Girl", "nameZh": "Airheaded 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A cool and composed adult female voice, suitable for an \"airheaded\" character in Standard Korean.", "descriptionEn": "A cool and composed adult female voice, suitable for an \"airheaded\" character in Standard Korean.", "descriptionZh": "一个cool and composed 成年女性的声音, ，适合 an \"airheaded\" character in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Korean_AirheadedGirl", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-athleticgirl", "name": "Athletic Girl", "nameEn": "Athletic Girl", "nameZh": "Athletic 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A robust and athletic female youth's voice in Standard Korean.", "descriptionEn": "A robust and athletic female youth's voice in Standard Korean.", "descriptionZh": "一个robust and athletic female 年轻人's的声音 in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Korean_AthleticGirl", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-athleticstudent", "name": "Athletic Student", "nameEn": "Athletic Student", "nameZh": "Athletic Student", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An energetic young adult male athletic student's voice in Standard Korean.", "descriptionEn": "An energetic young adult male athletic student's voice in Standard Korean.", "descriptionZh": "一个充满活力的 young 成年男性 athletic student's的声音 in 标准韩语.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Korean_AthleticStudent", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-braveadventurer", "name": "Brave Adventurer", "nameEn": "Brave Adventurer", "nameZh": "勇敢 Adventurer", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A playful and adventurous adult female brave adventurer's voice in Standard Korean.", "descriptionEn": "A playful and adventurous adult female brave adventurer's voice in Standard Korean.", "descriptionZh": "一个playful and adventurous 成年女性 brave adventurer's的声音 in 标准韩语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Korean_BraveAdventurer", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-bravefemalewarrior", "name": "Brave Female Warrior", "nameEn": "Brave Female Warrior", "nameZh": "勇敢 Female Warrior", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A resolute young adult brave female warrior's voice in Standard Korean.", "descriptionEn": "A resolute young adult brave female warrior's voice in Standard Korean.", "descriptionZh": "一个resolute young adult brave female warrior's的声音 in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Korean_BraveFemaleWarrior", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-braveyo<PERSON>", "name": "Brave Youth", "nameEn": "Brave Youth", "nameZh": "勇敢 Youth", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A powerful young adult brave male youth's voice in Standard Korean.", "descriptionEn": "A powerful young adult brave male youth's voice in Standard Korean.", "descriptionZh": "一个powerful young adult brave male 年轻人's的声音 in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Korean_BraveYouth", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>m <PERSON>", "nameEn": "<PERSON>m <PERSON>", "nameZh": "平静 温和man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A composed and calm middle-aged gentleman's voice in Standard Korean.", "descriptionEn": "A composed and calm middle-aged gentleman's voice in Standard Korean.", "descriptionZh": "一个composed and 平静的 middle-aged 温和的man's的声音 in 标准韩语.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Korean_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-calmlady", "name": "Calm Lady", "nameEn": "Calm Lady", "nameZh": "平静 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A resilient and determined young adult female voice in Standard Korean.", "descriptionEn": "A resilient and determined young adult female voice in Standard Korean.", "descriptionZh": "一个resilient and determined young 成年女性的声音 in 标准韩语.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Korean_CalmLady", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-caringwoman", "name": "Caring Woman", "nameEn": "Caring Woman", "nameZh": "Caring 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A lively and vibrant young adult caring woman's voice in Standard Korean.", "descriptionEn": "A lively and vibrant young adult caring woman's voice in Standard Korean.", "descriptionZh": "一个活泼的 and vibrant young adult caring woman's的声音 in 标准韩语.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Korean_CaringWoman", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-<PERSON><PERSON><PERSON><PERSON>", "name": "Charming Elder Sister", "nameEn": "Charming Elder Sister", "nameZh": "Charming Elder Sister", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A playful and mischievous middle-aged \"charming elder sister\" voice in Standard Korean.", "descriptionEn": "A playful and mischievous middle-aged \"charming elder sister\" voice in Standard Korean.", "descriptionZh": "一个playful and mischievous middle-aged \"charming elder sister\"的声音 in 标准韩语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Korean_CharmingElderSister", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-charmingsister", "name": "Charming Sister", "nameEn": "Charming Sister", "nameZh": "Charming Sister", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A seductive middle-aged \"charming sister\" voice in Standard Korean.", "descriptionEn": "A seductive middle-aged \"charming sister\" voice in Standard Korean.", "descriptionZh": "一个seductive middle-aged \"charming sister\"的声音 in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Korean_CharmingSister", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-cheerfulboyfriend", "name": "Cheerful Boyfriend", "nameEn": "Cheerful Boyfriend", "nameZh": "Cheerful 男孩friend", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A sharp and intense middle-aged male voice in Standard Korean.", "descriptionEn": "A sharp and intense middle-aged male voice in Standard Korean.", "descriptionZh": "一个sharp and intense 中年男性的声音 in 标准韩语.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Korean_CheerfulBoyfriend", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-cheerfulcooljunior", "name": "Cheerful Cool Junior", "nameEn": "Cheerful Cool Junior", "nameZh": "Cheerful Cool Junior", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An energetic and spirited adult male \"cheerful cool junior\" voice in Standard Korean.", "descriptionEn": "An energetic and spirited adult male \"cheerful cool junior\" voice in Standard Korean.", "descriptionZh": "一个充满活力的 and spirited 成年男性 \"愉快的 cool junior\"的声音 in 标准韩语.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Korean_CheerfulCoolJunior", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-cheerful<PERSON><PERSON><PERSON>", "name": "Cheerful Little <PERSON>", "nameEn": "Cheerful Little <PERSON>", "nameZh": "Cheerful Little <PERSON>", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An energetic and lively adult female \"cheerful little sister\" voice in Standard Korean.", "descriptionEn": "An energetic and lively adult female \"cheerful little sister\" voice in Standard Korean.", "descriptionZh": "一个充满活力的 and 活泼的 成年女性 \"愉快的 little sister\"的声音 in 标准韩语.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Korean_CheerfulLittleSister", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-childhoodfriendgirl", "name": "Childhood Friend Girl", "nameEn": "Childhood Friend Girl", "nameZh": "Childhood Friend 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A polite and reserved young adult female \"childhood friend\" voice in Standard Korean.", "descriptionEn": "A polite and reserved young adult female \"childhood friend\" voice in Standard Korean.", "descriptionZh": "一个polite and reserved young 成年女性 \"childhood friend\"的声音 in 标准韩语.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Korean_ChildhoodFriendGirl", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-cockyguy", "name": "<PERSON><PERSON>", "nameEn": "<PERSON><PERSON>", "nameZh": "Cocky 小伙", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A playful and mischievous young adult cocky guy's voice in Standard Korean.", "descriptionEn": "A playful and mischievous young adult cocky guy's voice in Standard Korean.", "descriptionZh": "一个playful and mischievous young adult cocky guy's的声音 in 标准韩语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-coldgirl", "name": "Cold Girl", "nameEn": "Cold Girl", "nameZh": "Cold 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An aloof and cold adult female voice in Standard Korean.", "descriptionEn": "An aloof and cold adult female voice in Standard Korean.", "descriptionZh": "一个aloof and cold 成年女性的声音 in 标准韩语.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Korean_ColdGirl", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-coldyoungman", "name": "<PERSON> Young Man", "nameEn": "<PERSON> Young Man", "nameZh": "Cold Young 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A cold and composed adult male voice in Standard Korean.", "descriptionEn": "A cold and composed adult male voice in Standard Korean.", "descriptionZh": "一个cold and composed 成年男性的声音 in 标准韩语.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Korean_ColdYoungMan", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-<PERSON><PERSON><PERSON>", "name": "Confident Boss", "nameEn": "Confident Boss", "nameZh": "Confident Boss", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A deep and commanding middle-aged confident boss's voice in Standard Korean.", "descriptionEn": "A deep and commanding middle-aged confident boss's voice in Standard Korean.", "descriptionZh": "一个深沉的 and 威严的 middle-aged confident boss's的声音 in 标准韩语.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Korean_ConfidentBoss", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-consider<PERSON><PERSON><PERSON>", "name": "Considerate Senior", "nameEn": "Considerate Senior", "nameZh": "Considerate Senior", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A gentle and mature young adult male \"considerate senior\" voice in Standard Korean.", "descriptionEn": "A gentle and mature young adult male \"considerate senior\" voice in Standard Korean.", "descriptionZh": "一个温和的 and mature young 成年男性 \"considerate senior\"的声音 in 标准韩语.", "styles": ["Gentle", "Serious"], "stylesEn": ["Gentle", "Serious"], "stylesZh": ["温和", "严肃"], "voiceName": "Korean_ConsiderateSenior", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-decisivequeen", "name": "Decisive Queen", "nameEn": "Decisive Queen", "nameZh": "Decisive Queen", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A sweet yet resolute young adult decisive queen's voice in Standard Korean.", "descriptionEn": "A sweet yet resolute young adult decisive queen's voice in Standard Korean.", "descriptionZh": "一个sweet yet resolute young adult decisive queen's的声音 in 标准韩语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Korean_DecisiveQueen", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-dominantman", "name": "Dominant Man", "nameEn": "Dominant Man", "nameZh": "Dominant 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A mature and authoritative dominant adult male voice in Standard Korean.", "descriptionEn": "A mature and authoritative dominant adult male voice in Standard Korean.", "descriptionZh": "一个mature and 权威的 dominant 成年男性的声音 in 标准韩语.", "styles": ["Authoritative", "Serious"], "stylesEn": ["Authoritative", "Serious"], "stylesZh": ["权威", "严肃"], "voiceName": "Korean_DominantMan", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-<PERSON><PERSON><PERSON><PERSON>", "name": "Elegant Princess", "nameEn": "Elegant Princess", "nameZh": "Elegant Princess", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A graceful and refined adult princess voice in Standard Korean.", "descriptionEn": "A graceful and refined adult princess voice in Standard Korean.", "descriptionZh": "一个graceful and refined adult princess的声音 in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Korean_ElegantPrincess", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-enchantingsister", "name": "Enchanting Sister", "nameEn": "Enchanting Sister", "nameZh": "Enchanting Sister", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A desirable and charming young adult \"enchanting sister\" voice in Standard Korean.", "descriptionEn": "A desirable and charming young adult \"enchanting sister\" voice in Standard Korean.", "descriptionZh": "一个desirable and charming young adult \"enchanting sister\"的声音 in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Korean_EnchantingSister", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-enthusiasticteen", "name": "Enthusiastic Teen", "nameEn": "Enthusiastic Teen", "nameZh": "Enthusiastic Teen", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A passionate and lively young adult male teen's voice in Standard Korean.", "descriptionEn": "A passionate and lively young adult male teen's voice in Standard Korean.", "descriptionZh": "一个passionate and 活泼的 young 成年男性 teen's的声音 in 标准韩语.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Korean_EnthusiasticTeen", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-friendlybigsister", "name": "Friendly Big Sister", "nameEn": "Friendly Big Sister", "nameZh": "Friendly Big Sister", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A charismatic and alluring middle-aged \"friendly big sister\" voice in Standard Korean.", "descriptionEn": "A charismatic and alluring middle-aged \"friendly big sister\" voice in Standard Korean.", "descriptionZh": "一个charismatic and alluring middle-aged \"友好的 big sister\"的声音 in 标准韩语.", "styles": ["Expressive"], "stylesEn": ["Expressive"], "stylesZh": ["表现力"], "voiceName": "Korean_FriendlyBigSister", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-gentleboss", "name": "Gentle Boss", "nameEn": "Gentle Boss", "nameZh": "温和 Boss", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A regal and refined middle-aged male \"gentle boss\" voice in Standard Korean.", "descriptionEn": "A regal and refined middle-aged male \"gentle boss\" voice in Standard Korean.", "descriptionZh": "一个regal and refined 中年男性 \"温和的 boss\"的声音 in 标准韩语.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Korean_GentleBoss", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-gentlewoman", "name": "Gentle Woman", "nameEn": "Gentle Woman", "nameZh": "温和 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A strong-willed yet gentle young adult female voice in Standard Korean.", "descriptionEn": "A strong-willed yet gentle young adult female voice in Standard Korean.", "descriptionZh": "一个strong-willed yet 温和的 young 成年女性的声音 in 标准韩语.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Korean_GentleWoman", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-haughtylady", "name": "Haughty Lady", "nameEn": "Haughty Lady", "nameZh": "Haughty 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A cold and distant young adult haughty lady's voice in Standard Korean.", "descriptionEn": "A cold and distant young adult haughty lady's voice in Standard Korean.", "descriptionZh": "一个cold and distant young adult haughty lady's的声音 in 标准韩语.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Korean_HaughtyLady", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-innocentboy", "name": "Innocent Boy", "nameEn": "Innocent Boy", "nameZh": "<PERSON> 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A naive and pure young adult male \"innocent boy\" voice in Standard Korean.", "descriptionEn": "A naive and pure young adult male \"innocent boy\" voice in Standard Korean.", "descriptionZh": "一个naive and pure young 成年男性 \"innocent boy\"的声音 in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "<PERSON>_Innocent<PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-intellectualman", "name": "Intellectual Man", "nameEn": "Intellectual Man", "nameZh": "Intellectual 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A combative middle-aged intellectual male voice in Standard Korean.", "descriptionEn": "A combative middle-aged intellectual male voice in Standard Korean.", "descriptionZh": "一个combative middle-aged intellectual male的声音 in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Korean_IntellectualMan", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-<PERSON><PERSON><PERSON>", "name": "Intellectual Senior", "nameEn": "Intellectual Senior", "nameZh": "Intellectual Senior", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A magnetic and intellectual young adult male voice with a senior quality in Standard Korean.", "descriptionEn": "A magnetic and intellectual young adult male voice with a senior quality in Standard Korean.", "descriptionZh": "一个磁性的 and intellectual young 成年男性的声音 with a senior quality in 标准韩语.", "styles": ["Expressive"], "stylesEn": ["Expressive"], "stylesZh": ["表现力"], "voiceName": "Korean_IntellectualSenior", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-lonelywarrior", "name": "Lonely Warrior", "nameEn": "Lonely Warrior", "nameZh": "Lonely Warrior", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A youthful and daring adult male \"lonely warrior\" voice in Standard Korean.", "descriptionEn": "A youthful and daring adult male \"lonely warrior\" voice in Standard Korean.", "descriptionZh": "一个年轻人ful and daring 成年男性 \"lonely warrior\"的声音 in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Korean_LonelyWarrior", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-maturelady", "name": "Mature Lady", "nameEn": "Mature Lady", "nameZh": "Mature 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A refined and elegant mature middle-aged lady's voice in Standard Korean.", "descriptionEn": "A refined and elegant mature middle-aged lady's voice in Standard Korean.", "descriptionZh": "一个refined and 优雅的 mature middle-aged lady's的声音 in 标准韩语.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Korean_MatureLady", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-mysteriousgirl", "name": "Mysterious Girl", "nameEn": "Mysterious Girl", "nameZh": "Mysterious 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An energetic and lively female youth voice for a mysterious character in Standard Korean.", "descriptionEn": "An energetic and lively female youth voice for a mysterious character in Standard Korean.", "descriptionZh": "一个充满活力的 and 活泼的 female 年轻人的声音 for a mysterious character in 标准韩语.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Korean_MysteriousGirl", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-<PERSON><PERSON><PERSON>", "name": "Optimistic Youth", "nameEn": "Optimistic Youth", "nameZh": "Optimistic Youth", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A cheerful and optimistic young adult male youth's voice in Standard Korean.", "descriptionEn": "A cheerful and optimistic young adult male youth's voice in Standard Korean.", "descriptionZh": "一个愉快的 and optimistic young 成年男性 年轻人's的声音 in 标准韩语.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Korean_OptimisticYouth", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-playboy<PERSON><PERSON><PERSON>", "name": "Playboy Charmer", "nameEn": "Playboy Charmer", "nameZh": "Playboy Charmer", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A seductive young adult male \"playboy charmer\" voice in Standard Korean.", "descriptionEn": "A seductive young adult male \"playboy charmer\" voice in Standard Korean.", "descriptionZh": "一个seductive young 成年男性 \"playboy charmer\"的声音 in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Korean_Playboy<PERSON><PERSON>mer", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-possessiveman", "name": "Possessive Man", "nameEn": "Possessive Man", "nameZh": "Possessive 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A powerful and authoritative middle-aged possessive man's voice in Standard Korean.", "descriptionEn": "A powerful and authoritative middle-aged possessive man's voice in Standard Korean.", "descriptionZh": "一个powerful and 权威的 middle-aged possessive man's的声音 in 标准韩语.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Korean_PossessiveMan", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-quirkygirl", "name": "Quirky Girl", "nameEn": "Quirky Girl", "nameZh": "Quirky 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An adorable and quirky female youth voice in Standard Korean.", "descriptionEn": "An adorable and quirky female youth voice in Standard Korean.", "descriptionZh": "一个adorable and quirky female 年轻人的声音 in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Korean_QuirkyGirl", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-reliablesister", "name": "Reliable Sister", "nameEn": "Reliable Sister", "nameZh": "Reliable Sister", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A powerful and authoritative middle-aged \"reliable sister\" voice in Standard Korean.", "descriptionEn": "A powerful and authoritative middle-aged \"reliable sister\" voice in Standard Korean.", "descriptionZh": "一个powerful and 权威的 middle-aged \"reliable sister\"的声音 in 标准韩语.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Korean_ReliableSister", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-<PERSON><PERSON><PERSON>", "name": "Reliable Youth", "nameEn": "Reliable Youth", "nameZh": "Reliable Youth", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A gentle and considerate young adult reliable male youth's voice in Standard Korean.", "descriptionEn": "A gentle and considerate young adult reliable male youth's voice in Standard Korean.", "descriptionZh": "一个温和的 and considerate young adult reliable male 年轻人's的声音 in 标准韩语.", "styles": ["Gentle", "Authoritative"], "stylesEn": ["Gentle", "Authoritative"], "stylesZh": ["温和", "权威"], "voiceName": "Korean_ReliableYouth", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-sassygirl", "name": "Sassy Girl", "nameEn": "Sassy Girl", "nameZh": "Sassy 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A Sassy female youth voice in Standard Korean.", "descriptionEn": "A Sassy female youth voice in Standard Korean.", "descriptionZh": "一个Sassy female 年轻人的声音 in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Korean_SassyGirl", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-shygirl", "name": "<PERSON><PERSON> <PERSON>", "nameEn": "<PERSON><PERSON> <PERSON>", "nameZh": "<PERSON><PERSON> 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A timid and introverted young adult female voice in Standard Korean.", "descriptionEn": "A timid and introverted young adult female voice in Standard Korean.", "descriptionZh": "一个timid and introverted young 成年女性的声音 in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Korean_ShyGirl", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-soothinglady", "name": "Soothing Lady", "nameEn": "Soothing Lady", "nameZh": "Soothing 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An alluring and soothing middle-aged female voice in Standard Korean.", "descriptionEn": "An alluring and soothing middle-aged female voice in Standard Korean.", "descriptionZh": "一个alluring and 舒缓的 中年女性的声音 in 标准韩语.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Korean_SoothingLady", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-strict<PERSON>s", "name": "Strict Boss", "nameEn": "Strict Boss", "nameZh": "Strict Boss", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A stern middle-aged male boss's voice in Standard Korean.", "descriptionEn": "A stern middle-aged male boss's voice in Standard Korean.", "descriptionZh": "一个stern 中年男性 boss's的声音 in 标准韩语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Korean_StrictBoss", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-sweetgirl", "name": "Sweet Girl", "nameEn": "Sweet Girl", "nameZh": "甜美 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A soothing and gentle middle-aged female voice in Standard Korean, sweet and calming.", "descriptionEn": "A soothing and gentle middle-aged female voice in Standard Korean, sweet and calming.", "descriptionZh": "一个舒缓的 and 温和的 中年女性的声音 in 标准韩语, sweet and 平静的ing.", "styles": ["Gentle", "Playful"], "stylesEn": ["Gentle", "Playful"], "stylesZh": ["温和", "活泼"], "voiceName": "Korean_SweetGirl", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-thoughtfulwoman", "name": "Thoughtful Woman", "nameEn": "Thoughtful Woman", "nameZh": "Thoughtful 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A mature and contemplative young adult thoughtful woman's voice in Standard Korean.", "descriptionEn": "A mature and contemplative young adult thoughtful woman's voice in Standard Korean.", "descriptionZh": "一个mature and contemplative young adult thoughtful woman's的声音 in 标准韩语.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Korean_ThoughtfulWoman", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-wiseelf", "name": "Wise Elf", "nameEn": "Wise Elf", "nameZh": "智慧 Elf", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A sweet and ethereal young adult female \"wise elf\" voice in Standard Korean.", "descriptionEn": "A sweet and ethereal young adult female \"wise elf\" voice in Standard Korean.", "descriptionZh": "一个sweet and ethereal young 成年女性 \"wise elf\"的声音 in 标准韩语.", "styles": ["Playful", "Serious"], "stylesEn": ["Playful", "Serious"], "stylesZh": ["活泼", "严肃"], "voiceName": "Korean_WiseElf", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "korean-wiseteacher", "name": "Wise Teacher", "nameEn": "Wise Teacher", "nameZh": "智慧 Teacher", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A sagacious and wise middle-aged male teacher's voice in Standard Korean.", "descriptionEn": "A sagacious and wise middle-aged male teacher's voice in Standard Korean.", "descriptionZh": "一个sagacious and wise 中年男性 teacher's的声音 in 标准韩语.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "<PERSON>_<PERSON><PERSON>eacher", "isActive": true, "languageSupports": [{"languageCode": "ko-KR", "quality": "standard", "isDefault": true, "sampleText": "안녕하세요, 이것은 텍스트 음성 변환 데모를 위한 샘플 음성입니다.", "sampleUrl": null}]}, {"slug": "spanish-serenewoman", "name": "<PERSON><PERSON>", "nameEn": "<PERSON><PERSON>", "nameZh": "<PERSON><PERSON> 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A soothing and serene young adult female voice in Standard Spanish.", "descriptionEn": "A soothing and serene young adult female voice in Standard Spanish.", "descriptionZh": "一个舒缓的 and serene young 成年女性的声音 in 标准西班牙语.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Spanish_SereneWoman", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-<PERSON><PERSON><PERSON>ner", "name": "Mature Partner", "nameEn": "Mature Partner", "nameZh": "Mature Partner", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A warm and mature middle-aged male partner's voice in Standard Spanish.", "descriptionEn": "A warm and mature middle-aged male partner's voice in Standard Spanish.", "descriptionZh": "一个温暖的 and mature 中年男性 partner's的声音 in 标准西班牙语.", "styles": ["Gentle", "Serious"], "stylesEn": ["Gentle", "Serious"], "stylesZh": ["温和", "严肃"], "voiceName": "Spanish_<PERSON>ure<PERSON><PERSON>ner", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-captivat<PERSON><PERSON><PERSON><PERSON>", "name": "Captivating Storyteller", "nameEn": "Captivating Storyteller", "nameZh": "Captivating Storyteller", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A captivating middle-aged male narrator's voice in Standard Spanish, perfect for storytelling.", "descriptionEn": "A captivating middle-aged male narrator's voice in Standard Spanish, perfect for storytelling.", "descriptionZh": "一个迷人的 中年男性 narrator's的声音 in 标准西班牙语, ，非常适合 讲故事.", "styles": ["Expressive", "Narrative"], "stylesEn": ["Expressive", "Narrative"], "stylesZh": ["表现力", "叙述"], "voiceName": "Spanish_CaptivatingStoryteller", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-narrator", "name": "Narrator", "nameEn": "Narrator", "nameZh": "叙述者", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A middle-aged female narrator's voice in Standard Spanish, ideal for storytelling.", "descriptionEn": "A middle-aged female narrator's voice in Standard Spanish, ideal for storytelling.", "descriptionZh": "一个中年女性 narrator's的声音 in 标准西班牙语, ，理想用于 讲故事.", "styles": ["Narrative"], "stylesEn": ["Narrative"], "stylesZh": ["叙述"], "voiceName": "Spanish_Narrator", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-wisescholar", "name": "Wise Scholar", "nameEn": "Wise Scholar", "nameZh": "智慧 Scholar", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A conversational young adult male wise scholar's voice in Standard Spanish.", "descriptionEn": "A conversational young adult male wise scholar's voice in Standard Spanish.", "descriptionZh": "一个conversational young 成年男性 wise scholar's的声音 in 标准西班牙语.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Spanish_WiseScholar", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-kind-heartedgirl", "name": "Kind-hearted Girl", "nameEn": "Kind-hearted Girl", "nameZh": "善良-hearted 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A bright and kind-hearted young adult female voice in Standard Spanish.", "descriptionEn": "A bright and kind-hearted young adult female voice in Standard Spanish.", "descriptionZh": "一个明亮的 and kind-hearted young 成年女性的声音 in 标准西班牙语.", "styles": ["Gentle", "Energetic"], "stylesEn": ["Gentle", "Energetic"], "stylesZh": ["温和", "活力"], "voiceName": "Spanish_Kind-<PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-determinedmanager", "name": "Determined Manager", "nameEn": "Determined Manager", "nameZh": "Determined 男性ager", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A businesslike and determined middle-aged female manager's voice in Standard Spanish.", "descriptionEn": "A businesslike and determined middle-aged female manager's voice in Standard Spanish.", "descriptionZh": "一个businesslike and determined 中年女性 manager's的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_DeterminedManager", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-boss<PERSON><PERSON>", "name": "Bossy Leader", "nameEn": "Bossy Leader", "nameZh": "Bossy Leader", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A businesslike and bossy adult male leader's voice in Standard Spanish.", "descriptionEn": "A businesslike and bossy adult male leader's voice in Standard Spanish.", "descriptionZh": "一个businesslike and bossy 成年男性 leader's的声音 in 标准西班牙语.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Spanish_BossyLeader", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-<PERSON><PERSON><PERSON><PERSON>", "name": "Reserved <PERSON> Man", "nameEn": "Reserved <PERSON> Man", "nameZh": "Reserved Young 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A tranquil and reserved young adult male voice in Standard Spanish.", "descriptionEn": "A tranquil and reserved young adult male voice in Standard Spanish.", "descriptionZh": "一个tranquil and reserved young 成年男性的声音 in 标准西班牙语.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Spanish_ReservedYoungMan", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-confidentwoman", "name": "Confident Woman", "nameEn": "Confident Woman", "nameZh": "Confident 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A clear and firm young adult confident woman's voice in Standard Spanish.", "descriptionEn": "A clear and firm young adult confident woman's voice in Standard Spanish.", "descriptionZh": "一个clear and firm young adult confident woman's的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_ConfidentWoman", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-thoughtfulman", "name": "Thoughtful Man", "nameEn": "Thoughtful Man", "nameZh": "Thoughtful 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A sober and thoughtful young adult male voice in Standard Spanish.", "descriptionEn": "A sober and thoughtful young adult male voice in Standard Spanish.", "descriptionZh": "一个sober and thoughtful young 成年男性的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_ThoughtfulMan", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-strong-willedboy", "name": "Strong-willed Boy", "nameEn": "Strong-willed Boy", "nameZh": "Strong-willed 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A mature and strong-willed adult male voice in Standard Spanish.", "descriptionEn": "A mature and strong-willed adult male voice in Standard Spanish.", "descriptionZh": "一个mature and strong-willed 成年男性的声音 in 标准西班牙语.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Spanish_Strong-<PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-sophisticatedlady", "name": "Sophisticated Lady", "nameEn": "Sophisticated Lady", "nameZh": "Sophisticated 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A refined and sophisticated adult lady's voice in Standard Spanish.", "descriptionEn": "A refined and sophisticated adult lady's voice in Standard Spanish.", "descriptionZh": "一个refined and 精致的 adult lady's的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_SophisticatedLady", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-rationalman", "name": "Rational Man", "nameEn": "Rational Man", "nameZh": "Rational 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A thoughtful and rational adult male voice in Standard Spanish.", "descriptionEn": "A thoughtful and rational adult male voice in Standard Spanish.", "descriptionZh": "一个thoughtful and rational 成年男性的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_RationalMan", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-animecharacter", "name": "Anime Character", "nameEn": "Anime Character", "nameZh": "Anime Character", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An animated middle-aged female voice in Standard Spanish, suitable for anime characters.", "descriptionEn": "An animated middle-aged female voice in Standard Spanish, suitable for anime characters.", "descriptionZh": "一个animated 中年女性的声音 in 标准西班牙语, ，适合 anime characters.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_AnimeCharacter", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-deep-tonedman", "name": "Deep-toned Man", "nameEn": "Deep-toned Man", "nameZh": "Deep-toned 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A charismatic, deep-toned middle-aged male voice in Standard Spanish.", "descriptionEn": "A charismatic, deep-toned middle-aged male voice in Standard Spanish.", "descriptionZh": "A charismatic, 深沉的-toned 中年男性 voice in 标准西班牙语.", "styles": ["Expressive"], "stylesEn": ["Expressive"], "stylesZh": ["表现力"], "voiceName": "Spanish_Deep-tonedMan", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-fussy<PERSON><PERSON><PERSON>", "name": "Fussy hostess", "nameEn": "Fussy hostess", "nameZh": "Fussy hostess", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An intense and fussy middle-aged female hostess's voice in Standard Spanish.", "descriptionEn": "An intense and fussy middle-aged female hostess's voice in Standard Spanish.", "descriptionZh": "一个intense and fussy 中年女性 hostess's的声音 in 标准西班牙语.", "styles": ["Professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Spanish_Fussyhostess", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-sincereteen", "name": "<PERSON>re <PERSON>", "nameEn": "<PERSON>re <PERSON>", "nameZh": "<PERSON>re <PERSON>", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A heartfelt and sincere male teen's voice in Standard Spanish.", "descriptionEn": "A heartfelt and sincere male teen's voice in Standard Spanish.", "descriptionZh": "一个heartfelt and sincere male teen's的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_SincereTeen", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-franklady", "name": "<PERSON>", "nameEn": "<PERSON>", "nameZh": "<PERSON>", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An agitated and frank adult lady's voice in Standard Spanish.", "descriptionEn": "An agitated and frank adult lady's voice in Standard Spanish.", "descriptionZh": "一个agitated and frank adult lady's的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_FrankLady", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-comedian", "name": "Comedian", "nameEn": "Comedian", "nameZh": "Comedian", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A humorous young adult male comedian's voice in Standard Spanish.", "descriptionEn": "A humorous young adult male comedian's voice in Standard Spanish.", "descriptionZh": "一个humorous young 成年男性 comedian's的声音 in 标准西班牙语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Spanish_Comedian", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-debator", "name": "Debator", "nameEn": "Debator", "nameZh": "Debator", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A tough middle-aged male debater's voice in Standard Spanish.", "descriptionEn": "A tough middle-aged male debater's voice in Standard Spanish.", "descriptionZh": "一个tough 中年男性 debater's的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_Debator", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-toughboss", "name": "<PERSON>ugh <PERSON>", "nameEn": "<PERSON>ugh <PERSON>", "nameZh": "<PERSON>ugh <PERSON>", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A mature and tough middle-aged female boss's voice in Standard Spanish.", "descriptionEn": "A mature and tough middle-aged female boss's voice in Standard Spanish.", "descriptionZh": "一个mature and tough 中年女性 boss's的声音 in 标准西班牙语.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Spanish_<PERSON>ughBoss", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-wiselady", "name": "<PERSON> Lady", "nameEn": "<PERSON> Lady", "nameZh": "智慧 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A neutral and wise middle-aged lady's voice in Standard Spanish.", "descriptionEn": "A neutral and wise middle-aged lady's voice in Standard Spanish.", "descriptionZh": "一个neutral and wise middle-aged lady's的声音 in 标准西班牙语.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Spanish_Wiselady", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-steadymentor", "name": "<PERSON><PERSON><PERSON> Mentor", "nameEn": "<PERSON><PERSON><PERSON> Mentor", "nameZh": "<PERSON><PERSON><PERSON> Mentor", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An arrogant yet steady young adult male mentor's voice in Standard Spanish.", "descriptionEn": "An arrogant yet steady young adult male mentor's voice in Standard Spanish.", "descriptionZh": "一个arrogant yet steady young 成年男性 mentor's的声音 in 标准西班牙语.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Spanish_Steadymentor", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-jovialman", "name": "Jovial Man", "nameEn": "Jovial Man", "nameZh": "Jovial 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A gravelly and jovial senior male voice in Standard Spanish.", "descriptionEn": "A gravelly and jovial senior male voice in Standard Spanish.", "descriptionZh": "一个gravelly and jovial 老年男性的声音 in 标准西班牙语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Spanish<PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-santaclaus", "name": "Santa Claus", "nameEn": "Santa Claus", "nameZh": "Santa Claus", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A joyful senior male Santa Claus voice in Standard Spanish.", "descriptionEn": "A joyful senior male Santa Claus voice in Standard Spanish.", "descriptionZh": "一个joyful 老年男性 Santa Claus的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_Santa<PERSON>laus", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-rudolph", "name": "<PERSON>", "nameEn": "<PERSON>", "nameZh": "<PERSON>", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A naive young adult male voice in the style of <PERSON> in Standard Spanish.", "descriptionEn": "A naive young adult male voice in the style of <PERSON> in Standard Spanish.", "descriptionZh": "一个naive young 成年男性的声音 in the style of <PERSON> in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish<PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-intonategirl", "name": "Intonate Girl", "nameEn": "Intonate Girl", "nameZh": "Intonate 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A versatile young adult female voice in Standard Spanish.", "descriptionEn": "A versatile young adult female voice in Standard Spanish.", "descriptionZh": "一个versatile young 成年女性的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_Intonategirl", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-a<PERSON><PERSON>", "name": "<PERSON>", "nameEn": "<PERSON>", "nameZh": "<PERSON>", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A steady adult male voice in the style of <PERSON> in Standard Spanish.", "descriptionEn": "A steady adult male voice in the style of <PERSON> in Standard Spanish.", "descriptionZh": "一个steady 成年男性的声音 in the style of <PERSON> in 标准西班牙语.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Spanish<PERSON>Arnold", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-ghost", "name": "Ghost", "nameEn": "Ghost", "nameZh": "Ghost", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A raspy adult male ghost's voice in Standard Spanish.", "descriptionEn": "A raspy adult male ghost's voice in Standard Spanish.", "descriptionZh": "一个raspy 成年男性 ghost's的声音 in 标准西班牙语.", "styles": ["Professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Spanish_Ghost", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-<PERSON><PERSON>er", "name": "Humorous Elder", "nameEn": "Humorous Elder", "nameZh": "Humorous Elder", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An eccentric and humorous senior male elder's voice in Standard Spanish.", "descriptionEn": "An eccentric and humorous senior male elder's voice in Standard Spanish.", "descriptionZh": "一个eccentric and humorous 老年男性 elder's的声音 in 标准西班牙语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Spanish_HumorousElder", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-energeticboy", "name": "Energetic Boy", "nameEn": "Energetic Boy", "nameZh": "Energetic 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A cheerful and energetic young adult male voice in Standard Spanish.", "descriptionEn": "A cheerful and energetic young adult male voice in Standard Spanish.", "descriptionZh": "一个愉快的 and 充满活力的 young 成年男性的声音 in 标准西班牙语.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Spanish_EnergeticBoy", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-whimsicalgirl", "name": "Whimsical Girl", "nameEn": "Whimsical Girl", "nameZh": "Whimsical 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A witty and whimsical young adult female voice in Standard Spanish.", "descriptionEn": "A witty and whimsical young adult female voice in Standard Spanish.", "descriptionZh": "一个witty and whimsical young 成年女性的声音 in 标准西班牙语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Spanish_WhimsicalGirl", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-strict<PERSON>s", "name": "Strict Boss", "nameEn": "Strict Boss", "nameZh": "Strict Boss", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A commanding and strict young adult female boss's voice in Standard Spanish.", "descriptionEn": "A commanding and strict young adult female boss's voice in Standard Spanish.", "descriptionZh": "一个威严的 and strict young 成年女性 boss's的声音 in 标准西班牙语.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Spanish_StrictBoss", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-reliableman", "name": "Reliable Man", "nameEn": "Reliable Man", "nameZh": "Reliable 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A steady and reliable adult male voice in Standard Spanish.", "descriptionEn": "A steady and reliable adult male voice in Standard Spanish.", "descriptionZh": "一个steady and reliable 成年男性的声音 in 标准西班牙语.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Spanish_ReliableMan", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-sereneelder", "name": "<PERSON><PERSON>", "nameEn": "<PERSON><PERSON>", "nameZh": "<PERSON><PERSON>", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A reflective and serene senior male elder's voice in Standard Spanish.", "descriptionEn": "A reflective and serene senior male elder's voice in Standard Spanish.", "descriptionZh": "一个reflective and serene 老年男性 elder's的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_SereneElder", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-angryman", "name": "Angry Man", "nameEn": "Angry Man", "nameZh": "Angry 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An intense young adult male voice in Standard Spanish, conveying anger.", "descriptionEn": "An intense young adult male voice in Standard Spanish, conveying anger.", "descriptionZh": "一个intense young 成年男性的声音 in 标准西班牙语, ，传达 anger.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_AngryMan", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-assertivequeen", "name": "Assertive Queen", "nameEn": "Assertive Queen", "nameZh": "Assertive Queen", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A firm and assertive young adult queen's voice in Standard Spanish.", "descriptionEn": "A firm and assertive young adult queen's voice in Standard Spanish.", "descriptionZh": "一个firm and assertive young adult queen's的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_AssertiveQueen", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-caringgirlfriend", "name": "Caring Girlfriend", "nameEn": "Caring Girlfriend", "nameZh": "Caring 女孩friend", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A dreamy adult female caring girlfriend's voice in Standard Spanish.", "descriptionEn": "A dreamy adult female caring girlfriend's voice in Standard Spanish.", "descriptionZh": "一个dreamy 成年女性 caring girlfriend's的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_CaringGirlfriend", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-powerfulsoldier", "name": "Powerful Soldier", "nameEn": "Powerful Soldier", "nameZh": "Powerful Soldier", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A youthful and bold young adult male powerful soldier's voice in Standard Spanish.", "descriptionEn": "A youthful and bold young adult male powerful soldier's voice in Standard Spanish.", "descriptionZh": "一个年轻人ful and bold young 成年男性 powerful soldier's的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_PowerfulSoldier", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-passionatewarrior", "name": "Passionate Warrior", "nameEn": "Passionate Warrior", "nameZh": "Passionate Warrior", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An energetic and passionate young adult male warrior's voice in Standard Spanish.", "descriptionEn": "An energetic and passionate young adult male warrior's voice in Standard Spanish.", "descriptionZh": "一个充满活力的 and passionate young 成年男性 warrior's的声音 in 标准西班牙语.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Spanish_PassionateWarrior", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-chattygirl", "name": "Chatty Girl", "nameEn": "Chatty Girl", "nameZh": "Chatty 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A conversational and chatty young adult female voice in Standard Spanish.", "descriptionEn": "A conversational and chatty young adult female voice in Standard Spanish.", "descriptionZh": "一个conversational and chatty young 成年女性的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_Chatty<PERSON><PERSON>l", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-romantichusband", "name": "Romantic Husband", "nameEn": "Romantic Husband", "nameZh": "Romantic Husband", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An emotional middle-aged male romantic husband's voice in Standard Spanish.", "descriptionEn": "An emotional middle-aged male romantic husband's voice in Standard Spanish.", "descriptionZh": "一个emotional 中年男性 romantic husband's的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_RomanticHusband", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-compellinggirl", "name": "Compelling Girl", "nameEn": "Compelling Girl", "nameZh": "Compelling 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A persuasive and compelling young adult female voice in Standard Spanish.", "descriptionEn": "A persuasive and compelling young adult female voice in Standard Spanish.", "descriptionZh": "一个persuasive and 引人注目的 young 成年女性的声音 in 标准西班牙语.", "styles": ["Expressive"], "stylesEn": ["Expressive"], "stylesZh": ["表现力"], "voiceName": "Spanish_CompellingGirl", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-powerfulveteran", "name": "Powerful Veteran", "nameEn": "Powerful Veteran", "nameZh": "Powerful Veteran", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A strong and powerful middle-aged male veteran's voice in Standard Spanish.", "descriptionEn": "A strong and powerful middle-aged male veteran's voice in Standard Spanish.", "descriptionZh": "一个strong and powerful 中年男性 veteran's的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_PowerfulVeteran", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-sensiblemanager", "name": "Sensible Manager", "nameEn": "Sensible Manager", "nameZh": "Sensible 男性ager", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A charismatic and sensible adult male manager's voice in Standard Spanish.", "descriptionEn": "A charismatic and sensible adult male manager's voice in Standard Spanish.", "descriptionZh": "一个charismatic and sensible 成年男性 manager's的声音 in 标准西班牙语.", "styles": ["Expressive"], "stylesEn": ["Expressive"], "stylesZh": ["表现力"], "voiceName": "Spanish_SensibleManager", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "spanish-thoughtfullady", "name": "Thoughtful Lady", "nameEn": "Thoughtful Lady", "nameZh": "Thoughtful 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A worried and thoughtful adult lady's voice in Standard Spanish.", "descriptionEn": "A worried and thoughtful adult lady's voice in Standard Spanish.", "descriptionZh": "一个worried and thoughtful adult lady's的声音 in 标准西班牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Spanish_ThoughtfulLady", "isActive": true, "languageSupports": [{"languageCode": "es-ES", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, esta es una voz de muestra para la demostración de texto a voz.", "sampleUrl": null}]}, {"slug": "portuguese-sentimentallady", "name": "Sentimental Lady", "nameEn": "Sentimental Lady", "nameZh": "Sentimental 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An elegant and sentimental young adult female voice in Portuguese.", "descriptionEn": "An elegant and sentimental young adult female voice in Portuguese.", "descriptionZh": "一个优雅的 and sentimental young 成年女性的声音 in Portuguese.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_SentimentalLady", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-boss<PERSON><PERSON>", "name": "Bossy Leader", "nameEn": "Bossy Leader", "nameZh": "Bossy Leader", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A calm and formal adult male bossy leader's voice in Portuguese.", "descriptionEn": "A calm and formal adult male bossy leader's voice in Portuguese.", "descriptionZh": "一个平静的 and formal 成年男性 bossy leader's的声音 in Portuguese.", "styles": ["Gentle", "Authoritative", "Serious"], "stylesEn": ["Gentle", "Authoritative", "Serious"], "stylesZh": ["温和", "权威", "严肃"], "voiceName": "Portuguese_BossyLeader", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-wiselady", "name": "Wise lady", "nameEn": "Wise lady", "nameZh": "智慧 lady", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A smooth and wise middle-aged lady's voice in Portuguese.", "descriptionEn": "A smooth and wise middle-aged lady's voice in Portuguese.", "descriptionZh": "一个smooth and wise middle-aged lady's的声音 in Portuguese.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Portuguese_Wiselady", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-strong-willedboy", "name": "Strong-willed Boy", "nameEn": "Strong-willed Boy", "nameZh": "Strong-willed 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A mature and strong-willed young adult male voice in Portuguese.", "descriptionEn": "A mature and strong-willed young adult male voice in Portuguese.", "descriptionZh": "一个mature and strong-willed young 成年男性的声音 in Portuguese.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Portuguese_Strong-<PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-deep-voiced<PERSON><PERSON><PERSON>", "name": "Deep-voiced Gentleman", "nameEn": "Deep-voiced Gentleman", "nameZh": "Deep-voiced 温和man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A deep-voiced adult gentleman in Portuguese.", "descriptionEn": "A deep-voiced adult gentleman in Portuguese.", "descriptionZh": "A 深沉的-voiced adult 温和的man in Portuguese.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Portuguese_Deep-<PERSON><PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-upsetgirl", "name": "Upset Girl", "nameEn": "Upset Girl", "nameZh": "Upset 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A sad young adult female voice in Portuguese, conveying upset emotions.", "descriptionEn": "A sad young adult female voice in Portuguese, conveying upset emotions.", "descriptionZh": "一个sad young 成年女性的声音 in Portuguese, ，传达 upset emotions.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_UpsetGirl", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-passionatewarrior", "name": "Passionate Warrior", "nameEn": "Passionate Warrior", "nameZh": "Passionate Warrior", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An energetic and passionate young adult male warrior's voice in Portuguese.", "descriptionEn": "An energetic and passionate young adult male warrior's voice in Portuguese.", "descriptionZh": "一个充满活力的 and passionate young 成年男性 warrior's的声音 in Portuguese.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Portuguese_PassionateWarrior", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-animecharacter", "name": "Anime Character", "nameEn": "Anime Character", "nameZh": "Anime Character", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An animated middle-aged female voice in Portuguese, suitable for anime characters.", "descriptionEn": "An animated middle-aged female voice in Portuguese, suitable for anime characters.", "descriptionZh": "一个animated 中年女性的声音 in Portuguese, ，适合 anime characters.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_AnimeCharacter", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-confidentwoman", "name": "Confident Woman", "nameEn": "Confident Woman", "nameZh": "Confident 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A clear and firm young adult confident woman's voice in Portuguese.", "descriptionEn": "A clear and firm young adult confident woman's voice in Portuguese.", "descriptionZh": "一个clear and firm young adult confident woman's的声音 in Portuguese.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_ConfidentWoman", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-angryman", "name": "Angry Man", "nameEn": "Angry Man", "nameZh": "Angry 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A serious young adult male voice in Portuguese, conveying anger.", "descriptionEn": "A serious young adult male voice in Portuguese, conveying anger.", "descriptionZh": "一个serious young 成年男性的声音 in Portuguese, ，传达 anger.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Portuguese_AngryMan", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-captivatingstoryteller", "name": "Captivating Storyteller", "nameEn": "Captivating Storyteller", "nameZh": "Captivating Storyteller", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A captivating middle-aged male narrator's voice in Portuguese, ideal for storytelling.", "descriptionEn": "A captivating middle-aged male narrator's voice in Portuguese, ideal for storytelling.", "descriptionZh": "一个迷人的 中年男性 narrator's的声音 in Portuguese, ，理想用于 讲故事.", "styles": ["Expressive", "Narrative"], "stylesEn": ["Expressive", "Narrative"], "stylesZh": ["表现力", "叙述"], "voiceName": "Portuguese_CaptivatingStoryteller", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-godfather", "name": "Godfather", "nameEn": "Godfather", "nameZh": "Godfather", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A serious middle-aged male godfather voice in Standard Portuguese, conveying authority.", "descriptionEn": "A serious middle-aged male godfather voice in Standard Portuguese, conveying authority.", "descriptionZh": "一个serious 中年男性 godfather的声音 in 标准葡萄牙语, ，传达 authority.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Portuguese_Godfather", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-<PERSON><PERSON><PERSON><PERSON>", "name": "Reserved <PERSON> Man", "nameEn": "Reserved <PERSON> Man", "nameZh": "Reserved Young 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A cold and calm reserved young adult male voice in Standard Portuguese.", "descriptionEn": "A cold and calm reserved young adult male voice in Standard Portuguese.", "descriptionZh": "一个cold and 平静的 reserved young 成年男性的声音 in 标准葡萄牙语.", "styles": ["Gentle", "Serious"], "stylesEn": ["Gentle", "Serious"], "stylesZh": ["温和", "严肃"], "voiceName": "Portuguese_ReservedYoungMan", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-smartyounggirl", "name": "Smart Young Girl", "nameEn": "Smart Young Girl", "nameZh": "<PERSON> Young 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An intelligent young female girl's voice in Standard Portuguese, sharp and clear.", "descriptionEn": "An intelligent young female girl's voice in Standard Portuguese, sharp and clear.", "descriptionZh": "一个intelligent young female girl's的声音 in 标准葡萄牙语, 尖锐而清晰.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_SmartYoungGirl", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-kind-heartedgirl", "name": "Kind-hearted Girl", "nameEn": "Kind-hearted Girl", "nameZh": "善良-hearted 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A calm and kind-hearted young adult female voice in Standard Portuguese.", "descriptionEn": "A calm and kind-hearted young adult female voice in Standard Portuguese.", "descriptionZh": "一个平静的 and kind-hearted young 成年女性的声音 in 标准葡萄牙语.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Portuguese_Kind-<PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-pompouslady", "name": "Pompous lady", "nameEn": "Pompous lady", "nameZh": "Pompous lady", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A pompous young adult female cartoon-style voice in Standard Portuguese, full of personality.", "descriptionEn": "A pompous young adult female cartoon-style voice in Standard Portuguese, full of personality.", "descriptionZh": "一个pompous young 成年女性 cartoon-style的声音 in 标准葡萄牙语, 充满个性.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_Pompouslady", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-grinch", "name": "<PERSON><PERSON><PERSON>", "nameEn": "<PERSON><PERSON><PERSON>", "nameZh": "<PERSON><PERSON><PERSON>", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A cunning adult male Grinch-like voice in Standard Portuguese, mischievous and sly.", "descriptionEn": "A cunning adult male Grinch-like voice in Standard Portuguese, mischievous and sly.", "descriptionZh": "一个cunning 成年男性 Grinch-like的声音 in 标准葡萄牙语, mischievous and sly.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_Grinch", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-debator", "name": "Debator", "nameEn": "Debator", "nameZh": "Debator", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A tough middle-aged male debater's voice in Standard Portuguese, strong and assertive.", "descriptionEn": "A tough middle-aged male debater's voice in Standard Portuguese, strong and assertive.", "descriptionZh": "一个tough 中年男性 debater's的声音 in 标准葡萄牙语, strong and assertive.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_Debator", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-sweetgirl", "name": "Sweet Girl", "nameEn": "Sweet Girl", "nameZh": "甜美 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An adorable and sweet young adult female voice in Standard Portuguese.", "descriptionEn": "An adorable and sweet young adult female voice in Standard Portuguese.", "descriptionZh": "一个adorable and sweet young 成年女性的声音 in 标准葡萄牙语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Portuguese_SweetGirl", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-attractivegirl", "name": "Attractive Girl", "nameEn": "Attractive Girl", "nameZh": "Attractive 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An alluring and attractive adult female voice in Standard Portuguese.", "descriptionEn": "An alluring and attractive adult female voice in Standard Portuguese.", "descriptionZh": "一个alluring and attractive 成年女性的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_AttractiveGirl", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-thoughtfulman", "name": "Thoughtful Man", "nameEn": "Thoughtful Man", "nameZh": "Thoughtful 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A gentle and thoughtful young adult male voice in Standard Portuguese.", "descriptionEn": "A gentle and thoughtful young adult male voice in Standard Portuguese.", "descriptionZh": "一个温和的 and thoughtful young 成年男性的声音 in 标准葡萄牙语.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Portuguese_ThoughtfulMan", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-playfulgirl", "name": "Playful Girl", "nameEn": "Playful Girl", "nameZh": "Playful 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A cutesy and playful female youth voice in Standard Portuguese.", "descriptionEn": "A cutesy and playful female youth voice in Standard Portuguese.", "descriptionZh": "一个cutesy and playful female 年轻人的声音 in 标准葡萄牙语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Portuguese_PlayfulGirl", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-gorgeouslady", "name": "Gorgeous Lady", "nameEn": "Gorgeous Lady", "nameZh": "Gorgeous 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A playful and gorgeous adult female voice in Standard Portuguese, exuding confidence.", "descriptionEn": "A playful and gorgeous adult female voice in Standard Portuguese, exuding confidence.", "descriptionZh": "一个playful and gorgeous 成年女性的声音 in 标准葡萄牙语, ，散发 confidence.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Portuguese_GorgeousLady", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-lovelylady", "name": "Lovely Lady", "nameEn": "Lovely Lady", "nameZh": "Lovely 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A charismatic and lovely adult female voice in Standard Portuguese.", "descriptionEn": "A charismatic and lovely adult female voice in Standard Portuguese.", "descriptionZh": "一个charismatic and lovely 成年女性的声音 in 标准葡萄牙语.", "styles": ["Expressive"], "stylesEn": ["Expressive"], "stylesZh": ["表现力"], "voiceName": "Portuguese_LovelyLady", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-serenewoman", "name": "<PERSON><PERSON>", "nameEn": "<PERSON><PERSON>", "nameZh": "<PERSON><PERSON> 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A calm and serene young adult female voice in Standard Portuguese, peaceful and composed.", "descriptionEn": "A calm and serene young adult female voice in Standard Portuguese, peaceful and composed.", "descriptionZh": "一个平静的 and serene young 成年女性的声音 in 标准葡萄牙语, 平和而沉着.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Portuguese_SereneWoman", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-sadteen", "name": "Sad Teen", "nameEn": "Sad Teen", "nameZh": "Sad Teen", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A frustrated and sad male teen's voice in Standard Portuguese.", "descriptionEn": "A frustrated and sad male teen's voice in Standard Portuguese.", "descriptionZh": "一个frustrated and sad male teen's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_SadTeen", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-mature<PERSON><PERSON><PERSON>", "name": "Mature Partner", "nameEn": "Mature Partner", "nameZh": "Mature Partner", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A mature middle-aged male partner's voice in Standard Portuguese, dependable and warm.", "descriptionEn": "A mature middle-aged male partner's voice in Standard Portuguese, dependable and warm.", "descriptionZh": "一个mature 中年男性 partner's的声音 in 标准葡萄牙语, dependable and 温暖的.", "styles": ["Gentle", "Serious"], "stylesEn": ["Gentle", "Serious"], "stylesZh": ["温和", "严肃"], "voiceName": "Portuguese_MaturePartner", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-comedian", "name": "Comedian", "nameEn": "Comedian", "nameZh": "Comedian", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A humorous young adult male comedian's voice in Standard Portuguese.", "descriptionEn": "A humorous young adult male comedian's voice in Standard Portuguese.", "descriptionZh": "一个humorous young 成年男性 comedian's的声音 in 标准葡萄牙语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Portuguese_Comedian", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-naughtyschoolgirl", "name": "Naughty Schoolgirl", "nameEn": "Naughty Schoolgirl", "nameZh": "Naughty Schoolgirl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An inviting and naughty young adult female schoolgirl's voice in Standard Portuguese.", "descriptionEn": "An inviting and naughty young adult female schoolgirl's voice in Standard Portuguese.", "descriptionZh": "一个inviting and naughty young 成年女性 schoolgirl's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_NaughtySchoolgirl", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-narrator", "name": "Narrator", "nameEn": "Narrator", "nameZh": "叙述者", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A middle-aged female narrator's voice in Standard Portuguese, perfect for storytelling.", "descriptionEn": "A middle-aged female narrator's voice in Standard Portuguese, perfect for storytelling.", "descriptionZh": "一个中年女性 narrator's的声音 in 标准葡萄牙语, ，非常适合 讲故事.", "styles": ["Narrative"], "stylesEn": ["Narrative"], "stylesZh": ["叙述"], "voiceName": "Portuguese_Narrator", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-toughboss", "name": "<PERSON>ugh <PERSON>", "nameEn": "<PERSON>ugh <PERSON>", "nameZh": "<PERSON>ugh <PERSON>", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A mature and tough middle-aged female boss's voice in Standard Portuguese.", "descriptionEn": "A mature and tough middle-aged female boss's voice in Standard Portuguese.", "descriptionZh": "一个mature and tough 中年女性 boss's的声音 in 标准葡萄牙语.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Portuguese_ToughBoss", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-fussyhos<PERSON>s", "name": "Fussy hostess", "nameEn": "Fussy hostess", "nameZh": "Fussy hostess", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An intense and fussy middle-aged female hostess's voice in Standard Portuguese.", "descriptionEn": "An intense and fussy middle-aged female hostess's voice in Standard Portuguese.", "descriptionZh": "一个intense and fussy 中年女性 hostess's的声音 in 标准葡萄牙语.", "styles": ["Professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Portuguese_Fussyhostess", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-dramatist", "name": "Dramatist", "nameEn": "Dramatist", "nameZh": "Dramatist", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A quirky middle-aged male dramatist's voice in Standard Portuguese.", "descriptionEn": "A quirky middle-aged male dramatist's voice in Standard Portuguese.", "descriptionZh": "一个quirky 中年男性 dramatist's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_Dramatist", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-steadymentor", "name": "<PERSON><PERSON><PERSON> Mentor", "nameEn": "<PERSON><PERSON><PERSON> Mentor", "nameZh": "<PERSON><PERSON><PERSON> Mentor", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An arrogant yet steady young adult male mentor's voice in Standard Portuguese.", "descriptionEn": "An arrogant yet steady young adult male mentor's voice in Standard Portuguese.", "descriptionZh": "一个arrogant yet steady young 成年男性 mentor's的声音 in 标准葡萄牙语.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Portuguese_Steadymentor", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-jovialman", "name": "Jovial Man", "nameEn": "Jovial Man", "nameZh": "Jovial 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A jovial and laughing middle-aged male voice in Standard Portuguese.", "descriptionEn": "A jovial and laughing middle-aged male voice in Standard Portuguese.", "descriptionZh": "一个jovial and laughing 中年男性的声音 in 标准葡萄牙语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-charmingqueen", "name": "Charming Queen", "nameEn": "Charming Queen", "nameZh": "Charming Queen", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A bewitching and charming adult queen's voice in Standard Portuguese.", "descriptionEn": "A bewitching and charming adult queen's voice in Standard Portuguese.", "descriptionZh": "一个bewitching and charming adult queen's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_<PERSON><PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-santaclaus", "name": "Santa Claus", "nameEn": "Santa Claus", "nameZh": "Santa Claus", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A joyful middle-aged male Santa Claus voice in Standard Portuguese, full of holiday cheer.", "descriptionEn": "A joyful middle-aged male Santa Claus voice in Standard Portuguese, full of holiday cheer.", "descriptionZh": "一个joyful 中年男性 Santa Claus的声音 in 标准葡萄牙语, full of holiday cheer.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-rudolph", "name": "<PERSON>", "nameEn": "<PERSON>", "nameZh": "<PERSON>", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A naive young adult female voice in the style of <PERSON> in Standard Portuguese.", "descriptionEn": "A naive young adult female voice in the style of <PERSON> in Standard Portuguese.", "descriptionZh": "一个naive young 成年女性的声音 in the style of <PERSON> in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "<PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portugues<PERSON>-<PERSON><PERSON><PERSON>", "name": "<PERSON>", "nameEn": "<PERSON>", "nameZh": "<PERSON>", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A steady adult male voice in the style of <PERSON> in Standard Portuguese, strong and firm.", "descriptionEn": "A steady adult male voice in the style of <PERSON> in Standard Portuguese, strong and firm.", "descriptionZh": "一个steady 成年男性的声音 in the style of <PERSON> in 标准葡萄牙语, strong and firm.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Portuguese<PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-<PERSON><PERSON><PERSON>", "name": "Charming Santa", "nameEn": "Charming Santa", "nameZh": "Charming Santa", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An attractive and charming middle-aged male Santa voice in Standard Portuguese.", "descriptionEn": "An attractive and charming middle-aged male Santa voice in Standard Portuguese.", "descriptionZh": "一个attractive and charming 中年男性 Santa的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_CharmingSanta", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-charminglady", "name": "Charming Lady", "nameEn": "Charming Lady", "nameZh": "Charming <PERSON>", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "", "descriptionEn": "", "descriptionZh": null, "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_CharmingLady", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-ghost", "name": "Ghost", "nameEn": "Ghost", "nameZh": "Ghost", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A sensual adult male ghost's voice in Standard Portuguese, mysterious and alluring.", "descriptionEn": "A sensual adult male ghost's voice in Standard Portuguese, mysterious and alluring.", "descriptionZh": "一个sensual 成年男性 ghost's的声音 in 标准葡萄牙语, mysterious and alluring.", "styles": ["Professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Portuguese_Ghost", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-<PERSON><PERSON>er", "name": "Humorous Elder", "nameEn": "Humorous Elder", "nameZh": "Humorous Elder", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A wacky and humorous middle-aged male elder's voice in Standard Portuguese.", "descriptionEn": "A wacky and humorous middle-aged male elder's voice in Standard Portuguese.", "descriptionZh": "一个wacky and humorous 中年男性 elder's的声音 in 标准葡萄牙语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Portuguese_HumorousElder", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-calmleader", "name": "Calm Leader", "nameEn": "Calm Leader", "nameZh": "平静 Leader", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A composed and calm middle-aged male leader's voice in Standard Portuguese.", "descriptionEn": "A composed and calm middle-aged male leader's voice in Standard Portuguese.", "descriptionZh": "一个composed and 平静的 中年男性 leader's的声音 in 标准葡萄牙语.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Portuguese_CalmLeader", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-gentleteacher", "name": "Gentle Teacher", "nameEn": "Gentle Teacher", "nameZh": "温和 Teacher", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A mild and gentle adult male teacher's voice in Standard Portuguese.", "descriptionEn": "A mild and gentle adult male teacher's voice in Standard Portuguese.", "descriptionZh": "一个mild and 温和的 成年男性 teacher's的声音 in 标准葡萄牙语.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Portuguese_<PERSON><PERSON>Teacher", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-energeticboy", "name": "Energetic Boy", "nameEn": "Energetic Boy", "nameZh": "Energetic 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A cheerful and energetic young adult male voice in Standard Portuguese.", "descriptionEn": "A cheerful and energetic young adult male voice in Standard Portuguese.", "descriptionZh": "一个愉快的 and 充满活力的 young 成年男性的声音 in 标准葡萄牙语.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Portuguese_EnergeticBoy", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-reliableman", "name": "Reliable Man", "nameEn": "Reliable Man", "nameZh": "Reliable 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A steady and reliable adult male voice in Standard Portuguese.", "descriptionEn": "A steady and reliable adult male voice in Standard Portuguese.", "descriptionZh": "一个steady and reliable 成年男性的声音 in 标准葡萄牙语.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Portuguese_ReliableMan", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-sereneelder", "name": "<PERSON><PERSON>", "nameEn": "<PERSON><PERSON>", "nameZh": "<PERSON><PERSON>", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A reflective and serene middle-aged male elder's voice in Standard Portuguese.", "descriptionEn": "A reflective and serene middle-aged male elder's voice in Standard Portuguese.", "descriptionZh": "一个reflective and serene 中年男性 elder's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_SereneElder", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-grimreaper", "name": "<PERSON><PERSON> Reaper", "nameEn": "<PERSON><PERSON> Reaper", "nameZh": "<PERSON><PERSON> Reaper", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A sinister adult male <PERSON><PERSON> Reaper's voice in Standard Portuguese, dark and ominous.", "descriptionEn": "A sinister adult male <PERSON><PERSON> Reaper's voice in Standard Portuguese, dark and ominous.", "descriptionZh": "一个sinister 成年男性 Grim Reaper's的声音 in 标准葡萄牙语, dark and ominous.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_GrimReaper", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-assertivequeen", "name": "Assertive Queen", "nameEn": "Assertive Queen", "nameZh": "Assertive Queen", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A firm and assertive young adult queen's voice in Standard Portuguese.", "descriptionEn": "A firm and assertive young adult queen's voice in Standard Portuguese.", "descriptionZh": "一个firm and assertive young adult queen's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_AssertiveQueen", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-whimsicalgirl", "name": "Whimsical Girl", "nameEn": "Whimsical Girl", "nameZh": "Whimsical 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A lovely and whimsical young adult female voice in Standard Portuguese.", "descriptionEn": "A lovely and whimsical young adult female voice in Standard Portuguese.", "descriptionZh": "一个lovely and whimsical young 成年女性的声音 in 标准葡萄牙语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Portuguese_WhimsicalGirl", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-stressedlady", "name": "Stressed Lady", "nameEn": "Stressed Lady", "nameZh": "Stressed 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An unsure and stressed middle-aged lady's voice in Standard Portuguese.", "descriptionEn": "An unsure and stressed middle-aged lady's voice in Standard Portuguese.", "descriptionZh": "一个unsure and stressed middle-aged lady's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_StressedLady", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-friendlyneighbor", "name": "Friendly Neighbor", "nameEn": "Friendly Neighbor", "nameZh": "Friendly Neighbor", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An energetic and friendly young adult female neighbor's voice in Standard Portuguese.", "descriptionEn": "An energetic and friendly young adult female neighbor's voice in Standard Portuguese.", "descriptionZh": "一个充满活力的 and 友好的 young 成年女性 neighbor's的声音 in 标准葡萄牙语.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Portuguese_FriendlyNeighbor", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-caringgirlfriend", "name": "Caring Girlfriend", "nameEn": "Caring Girlfriend", "nameZh": "Caring 女孩friend", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A dreamy middle-aged female caring girlfriend's voice in Standard Portuguese.", "descriptionEn": "A dreamy middle-aged female caring girlfriend's voice in Standard Portuguese.", "descriptionZh": "一个dreamy 中年女性 caring girlfriend's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_CaringGirlfriend", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-powerfulsoldier", "name": "Powerful Soldier", "nameEn": "Powerful Soldier", "nameZh": "Powerful Soldier", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A youthful and bold young adult male powerful soldier's voice in Standard Portuguese.", "descriptionEn": "A youthful and bold young adult male powerful soldier's voice in Standard Portuguese.", "descriptionZh": "一个年轻人ful and bold young 成年男性 powerful soldier's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_PowerfulSoldier", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-fascinatingboy", "name": "Fascinating Boy", "nameEn": "Fascinating Boy", "nameZh": "Fascinating 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An approachable and fascinating young adult male voice in Standard Portuguese.", "descriptionEn": "An approachable and fascinating young adult male voice in Standard Portuguese.", "descriptionZh": "一个approachable and fascinating young 成年男性的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_FascinatingBoy", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-romantichusband", "name": "Romantic Husband", "nameEn": "Romantic Husband", "nameZh": "Romantic Husband", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An emotional middle-aged male romantic husband's voice in Standard Portuguese.", "descriptionEn": "An emotional middle-aged male romantic husband's voice in Standard Portuguese.", "descriptionZh": "一个emotional 中年男性 romantic husband's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_RomanticHusband", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-<PERSON>boss", "name": "Strict Boss", "nameEn": "Strict Boss", "nameZh": "Strict Boss", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A robotic and strict young adult female boss's voice in Standard Portuguese.", "descriptionEn": "A robotic and strict young adult female boss's voice in Standard Portuguese.", "descriptionZh": "一个robotic and strict young 成年女性 boss's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_StrictBoss", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-inspiringlady", "name": "Inspiring Lady", "nameEn": "Inspiring Lady", "nameZh": "Inspiring 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A commanding and inspiring young adult female voice in Standard Portuguese.", "descriptionEn": "A commanding and inspiring young adult female voice in Standard Portuguese.", "descriptionZh": "一个威严的 and inspiring young 成年女性的声音 in 标准葡萄牙语.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Portuguese_InspiringLady", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-<PERSON><PERSON><PERSON><PERSON>", "name": "Playful Spirit", "nameEn": "Playful Spirit", "nameZh": "Playful Spirit", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An animated and playful young adult female spirit's voice in Standard Portuguese.", "descriptionEn": "An animated and playful young adult female spirit's voice in Standard Portuguese.", "descriptionZh": "一个animated and playful young 成年女性 spirit's的声音 in 标准葡萄牙语.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Portuguese_PlayfulSpirit", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-elegantgirl", "name": "Elegant Girl", "nameEn": "Elegant Girl", "nameZh": "Elegant 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A dramatic and elegant young adult female voice in Standard Portuguese.", "descriptionEn": "A dramatic and elegant young adult female voice in Standard Portuguese.", "descriptionZh": "一个dramatic and 优雅的 young 成年女性的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_ElegantGirl", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-compellinggirl", "name": "Compelling Girl", "nameEn": "Compelling Girl", "nameZh": "Compelling 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A persuasive and compelling young adult female voice in Standard Portuguese.", "descriptionEn": "A persuasive and compelling young adult female voice in Standard Portuguese.", "descriptionZh": "一个persuasive and 引人注目的 young 成年女性的声音 in 标准葡萄牙语.", "styles": ["Expressive"], "stylesEn": ["Expressive"], "stylesZh": ["表现力"], "voiceName": "Portuguese_CompellingGirl", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-powerfulveteran", "name": "Powerful Veteran", "nameEn": "Powerful Veteran", "nameZh": "Powerful Veteran", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A strong and powerful middle-aged male veteran's voice in Standard Portuguese.", "descriptionEn": "A strong and powerful middle-aged male veteran's voice in Standard Portuguese.", "descriptionZh": "一个strong and powerful 中年男性 veteran's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_PowerfulVeteran", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-sensiblemanager", "name": "Sensible Manager", "nameEn": "Sensible Manager", "nameZh": "Sensible 男性ager", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A charismatic and sensible middle-aged male manager's voice in Standard Portuguese.", "descriptionEn": "A charismatic and sensible middle-aged male manager's voice in Standard Portuguese.", "descriptionZh": "一个charismatic and sensible 中年男性 manager's的声音 in 标准葡萄牙语.", "styles": ["Expressive"], "stylesEn": ["Expressive"], "stylesZh": ["表现力"], "voiceName": "Portuguese_SensibleManager", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-thoughtfullady", "name": "Thoughtful Lady", "nameEn": "Thoughtful Lady", "nameZh": "Thoughtful 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A worried and thoughtful middle-aged lady's voice in Standard Portuguese.", "descriptionEn": "A worried and thoughtful middle-aged lady's voice in Standard Portuguese.", "descriptionZh": "一个worried and thoughtful middle-aged lady's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_ThoughtfulLady", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-theatricalactor", "name": "Theatrical Actor", "nameEn": "Theatrical Actor", "nameZh": "Theatrical Actor", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An animated middle-aged male theatrical actor's voice in Standard Portuguese.", "descriptionEn": "An animated middle-aged male theatrical actor's voice in Standard Portuguese.", "descriptionZh": "一个animated 中年男性 theatrical actor's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_TheatricalActor", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-fragileboy", "name": "Fr<PERSON>le Boy", "nameEn": "Fr<PERSON>le Boy", "nameZh": "Fragile 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A gentle and fragile young adult male voice in Standard Portuguese.", "descriptionEn": "A gentle and fragile young adult male voice in Standard Portuguese.", "descriptionZh": "一个温和的 and fragile young 成年男性的声音 in 标准葡萄牙语.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Portuguese_FragileBoy", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-chattygirl", "name": "Chatty Girl", "nameEn": "Chatty Girl", "nameZh": "Chatty 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A conversational and chatty young adult female voice in Standard Portuguese.", "descriptionEn": "A conversational and chatty young adult female voice in Standard Portuguese.", "descriptionZh": "一个conversational and chatty young 成年女性的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_ChattyGirl", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-conscientiousinstructor", "name": "Conscientious In<PERSON><PERSON>ctor", "nameEn": "Conscientious In<PERSON><PERSON>ctor", "nameZh": "Conscientious In<PERSON><PERSON>ctor", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A youthful and conscientious young adult female instructor's voice in Standard Portuguese.", "descriptionEn": "A youthful and conscientious young adult female instructor's voice in Standard Portuguese.", "descriptionZh": "一个年轻人ful and conscientious young 成年女性 instructor's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_Conscientiousinstructor", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-rationalman", "name": "Rational Man", "nameEn": "Rational Man", "nameZh": "Rational 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A thoughtful and rational adult male voice in Standard Portuguese.", "descriptionEn": "A thoughtful and rational adult male voice in Standard Portuguese.", "descriptionZh": "一个thoughtful and rational 成年男性的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_RationalMan", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-wisescholar", "name": "Wise Scholar", "nameEn": "Wise Scholar", "nameZh": "智慧 Scholar", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A conversational young adult male wise scholar's voice in Standard Portuguese.", "descriptionEn": "A conversational young adult male wise scholar's voice in Standard Portuguese.", "descriptionZh": "一个conversational young 成年男性 wise scholar's的声音 in 标准葡萄牙语.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Portuguese_WiseScholar", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-franklady", "name": "<PERSON>", "nameEn": "<PERSON>", "nameZh": "<PERSON>", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An agitated and frank middle-aged lady's voice in Standard Portuguese.", "descriptionEn": "An agitated and frank middle-aged lady's voice in Standard Portuguese.", "descriptionZh": "一个agitated and frank middle-aged lady's的声音 in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_FrankLady", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "portuguese-determinedmanager", "name": "Determined Manager", "nameEn": "Determined Manager", "nameZh": "Determined 男性ager", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A middle-aged female manager's voice with attitude and determination in Standard Portuguese.", "descriptionEn": "A middle-aged female manager's voice with attitude and determination in Standard Portuguese.", "descriptionZh": "一个中年女性 manager's的声音 with attitude and determination in 标准葡萄牙语.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Portuguese_DeterminedManager", "isActive": true, "languageSupports": [{"languageCode": "pt-BR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, esta é uma voz de amostra para demonstração de texto para fala.", "sampleUrl": null}]}, {"slug": "french-male-speech-new", "name": "Level-Headed Man", "nameEn": "Level-Headed Man", "nameZh": "Level-Headed 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A level-headed and composed adult male voice in French.", "descriptionEn": "A level-headed and composed adult male voice in French.", "descriptionZh": "一个level-headed and composed 成年男性的声音 in French.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "French_Male_Speech_New", "isActive": true, "languageSupports": [{"languageCode": "fr-FR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, ceci est un échantillon vocal pour la démonstration de synthèse vocale.", "sampleUrl": null}]}, {"slug": "french-female-news-anchor", "name": "Patient Female Presenter", "nameEn": "Patient Female Presenter", "nameZh": "Patient Female Presenter", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A patient adult female presenter in French, calm and clear.", "descriptionEn": "A patient adult female presenter in French, calm and clear.", "descriptionZh": "A patient 成年女性 presenter in French, 平静的 and clear.", "styles": ["Professional", "Gentle"], "stylesEn": ["Professional", "Gentle"], "stylesZh": ["专业", "温和"], "voiceName": "French_Female_News Anchor", "isActive": true, "languageSupports": [{"languageCode": "fr-FR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, ceci est un échantillon vocal pour la démonstration de synthèse vocale.", "sampleUrl": null}]}, {"slug": "french-casualman", "name": "Casual Man", "nameEn": "Casual Man", "nameZh": "Casual 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A casual and relaxed middle-aged male voice in French.", "descriptionEn": "A casual and relaxed middle-aged male voice in French.", "descriptionZh": "一个casual and relaxed 中年男性的声音 in French.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "French_CasualMan", "isActive": true, "languageSupports": [{"languageCode": "fr-FR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, ceci est un échantillon vocal pour la démonstration de synthèse vocale.", "sampleUrl": null}]}, {"slug": "french-movieleadfemale", "name": "Movie Lead Female", "nameEn": "Movie Lead Female", "nameZh": "Movie Lead Female", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A cinematic young adult female lead voice in French, perfect for film.", "descriptionEn": "A cinematic young adult female lead voice in French, perfect for film.", "descriptionZh": "一个cinematic young 成年女性 lead的声音 in French, ，非常适合 film.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "French_MovieLeadFemale", "isActive": true, "languageSupports": [{"languageCode": "fr-FR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, ceci est un échantillon vocal pour la démonstration de synthèse vocale.", "sampleUrl": null}]}, {"slug": "french-femaleanchor", "name": "Female Anchor", "nameEn": "Female Anchor", "nameZh": "Female 主播", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A professional adult female anchor voice in French, authoritative and clear.", "descriptionEn": "A professional adult female anchor voice in French, authoritative and clear.", "descriptionZh": "一个专业的 成年女性 anchor的声音 in French, 权威的 and clear.", "styles": ["Professional", "Authoritative"], "stylesEn": ["Professional", "Authoritative"], "stylesZh": ["专业", "权威"], "voiceName": "French_FemaleAnchor", "isActive": true, "languageSupports": [{"languageCode": "fr-FR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, ceci est un échantillon vocal pour la démonstration de synthèse vocale.", "sampleUrl": null}]}, {"slug": "french-malenarrator", "name": "Male Narrator", "nameEn": "Male Narrator", "nameZh": "Male 叙述者", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A classic adult male narrator voice in French, ideal for storytelling.", "descriptionEn": "A classic adult male narrator voice in French, ideal for storytelling.", "descriptionZh": "一个经典 成年男性 narrator的声音 in French, ，理想用于 讲故事.", "styles": ["Narrative"], "stylesEn": ["Narrative"], "stylesZh": ["叙述"], "voiceName": "French_MaleNarrator", "isActive": true, "languageSupports": [{"languageCode": "fr-FR", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON><PERSON>, ceci est un échantillon vocal pour la démonstration de synthèse vocale.", "sampleUrl": null}]}, {"slug": "indonesian-sweetgirl", "name": "Sweet Girl", "nameEn": "Sweet Girl", "nameZh": "甜美 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A cute and sweet young adult female voice in Indonesian.", "descriptionEn": "A cute and sweet young adult female voice in Indonesian.", "descriptionZh": "一个cute and sweet young 成年女性的声音 in Indonesian.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "Indonesian_SweetGirl", "isActive": true, "languageSupports": [{"languageCode": "id-ID", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "indonesian-<PERSON><PERSON><PERSON><PERSON>", "name": "Reserved <PERSON> Man", "nameEn": "Reserved <PERSON> Man", "nameZh": "Reserved Young 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A cold and reserved young adult male voice in Indonesian.", "descriptionEn": "A cold and reserved young adult male voice in Indonesian.", "descriptionZh": "一个cold and reserved young 成年男性的声音 in Indonesian.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Indonesian_ReservedYoungMan", "isActive": true, "languageSupports": [{"languageCode": "id-ID", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "indonesian-charminggirl", "name": "Charming Girl", "nameEn": "Charming Girl", "nameZh": "Charming 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An alluring and charming adult female voice in Indonesian.", "descriptionEn": "An alluring and charming adult female voice in Indonesian.", "descriptionZh": "一个alluring and charming 成年女性的声音 in Indonesian.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Indonesian_CharmingGirl", "isActive": true, "languageSupports": [{"languageCode": "id-ID", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "indonesian-calmwoman", "name": "Calm Woman", "nameEn": "Calm Woman", "nameZh": "平静女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A serene and calm young adult female voice in Indonesian.", "descriptionEn": "A serene and calm young adult female voice in Indonesian.", "descriptionZh": "一个serene and 平静的 young 成年女性的声音 in Indonesian.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Indonesian_CalmWoman", "isActive": true, "languageSupports": [{"languageCode": "id-ID", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "indonesian-confidentwoman", "name": "Confident Woman", "nameEn": "Confident Woman", "nameZh": "Confident 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An assertive and confident young adult female voice in Indonesian.", "descriptionEn": "An assertive and confident young adult female voice in Indonesian.", "descriptionZh": "一个assertive and confident young 成年女性的声音 in Indonesian.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Indonesian_ConfidentWoman", "isActive": true, "languageSupports": [{"languageCode": "id-ID", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "indonesian-caringman", "name": "Caring Man", "nameEn": "Caring Man", "nameZh": "Caring 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A compassionate and caring young adult male voice in Indonesian.", "descriptionEn": "A compassionate and caring young adult male voice in Indonesian.", "descriptionZh": "一个compassionate and caring young 成年男性的声音 in Indonesian.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Indonesian_CaringMan", "isActive": true, "languageSupports": [{"languageCode": "id-ID", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "indonesian-boss<PERSON><PERSON>", "name": "Bossy Leader", "nameEn": "Bossy Leader", "nameZh": "Bossy Leader", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A calm, authoritative, and bossy adult male leader's voice in Indonesian.", "descriptionEn": "A calm, authoritative, and bossy adult male leader's voice in Indonesian.", "descriptionZh": "A 平静的, 权威的, and bossy 成年男性 leader's voice in Indonesian.", "styles": ["Gentle", "Authoritative"], "stylesEn": ["Gentle", "Authoritative"], "stylesZh": ["温和", "权威"], "voiceName": "Indonesian_BossyLeader", "isActive": true, "languageSupports": [{"languageCode": "id-ID", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "indonesian-determinedboy", "name": "Determined Boy", "nameEn": "Determined Boy", "nameZh": "Determined 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A mature and resolute young adult male voice in Indonesian, conveying determination.", "descriptionEn": "A mature and resolute young adult male voice in Indonesian, conveying determination.", "descriptionZh": "一个mature and resolute young 成年男性的声音 in Indonesian, ，传达 determination.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Indonesian_DeterminedBoy", "isActive": true, "languageSupports": [{"languageCode": "id-ID", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "indonesian-gentlegirl", "name": "Gentle Girl", "nameEn": "Gentle Girl", "nameZh": "温和 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A soft-spoken and gentle young adult female voice in Indonesian.", "descriptionEn": "A soft-spoken and gentle young adult female voice in Indonesian.", "descriptionZh": "一个soft-spoken and 温和的 young 成年女性的声音 in Indonesian.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Indonesian_GentleGirl", "isActive": true, "languageSupports": [{"languageCode": "id-ID", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "german-friendlyman", "name": "Friendly Man", "nameEn": "Friendly Man", "nameZh": "Friendly 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A sincere and friendly middle-aged male voice in German.", "descriptionEn": "A sincere and friendly middle-aged male voice in German.", "descriptionZh": "一个sincere and 友好的 中年男性的声音 in German.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "German_FriendlyMan", "isActive": true, "languageSupports": [{"languageCode": "de-DE", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, das ist eine Beispielstimme für die Text-zu-Sprache-Demonstration.", "sampleUrl": null}]}, {"slug": "german-sweetlady", "name": "Sweet Lady", "nameEn": "Sweet Lady", "nameZh": "甜美 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An animated and sweet adult female voice in German.", "descriptionEn": "An animated and sweet adult female voice in German.", "descriptionZh": "一个animated and sweet 成年女性的声音 in German.", "styles": ["Playful"], "stylesEn": ["Playful"], "stylesZh": ["活泼"], "voiceName": "German_SweetLady", "isActive": true, "languageSupports": [{"languageCode": "de-DE", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, das ist eine Beispielstimme für die Text-zu-Sprache-Demonstration.", "sampleUrl": null}]}, {"slug": "german-playfulman", "name": "Playful Man", "nameEn": "Playful Man", "nameZh": "Playful 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A lively and spirited adult male voice in German, full of energy.", "descriptionEn": "A lively and spirited adult male voice in German, full of energy.", "descriptionZh": "一个活泼的 and spirited 成年男性的声音 in German, full of energy.", "styles": ["Energetic", "Playful"], "stylesEn": ["Energetic", "Playful"], "stylesZh": ["活力", "活泼"], "voiceName": "German_PlayfulMan", "isActive": true, "languageSupports": [{"languageCode": "de-DE", "quality": "standard", "isDefault": true, "sampleText": "<PERSON><PERSON>, das ist eine Beispielstimme für die Text-zu-Sprache-Demonstration.", "sampleUrl": null}]}, {"slug": "russian-handsomechildhoodfriend", "name": "Handsome Childhood Friend", "nameEn": "Handsome Childhood Friend", "nameZh": "Handsome Childhood Friend", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An aggressive female youth's voice for a handsome childhood friend character in Standard Russian.", "descriptionEn": "An aggressive female youth's voice for a handsome childhood friend character in Standard Russian.", "descriptionZh": "一个aggressive female 年轻人's的声音 for a handsome childhood friend character in Standard Russian.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Russian_HandsomeChildhoodFriend", "isActive": true, "languageSupports": [{"languageCode": "ru-RU", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "russian-brightheroine", "name": "<PERSON>", "nameEn": "<PERSON>", "nameZh": "<PERSON>", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An arrogant and bright adult queen's voice in Standard Russian.", "descriptionEn": "An arrogant and bright adult queen's voice in Standard Russian.", "descriptionZh": "一个arrogant and 明亮的 adult queen's的声音 in Standard Russian.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Russian_Bright<PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "ru-RU", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "russian-ambitiouswoman", "name": "Ambitious Woman", "nameEn": "Ambitious Woman", "nameZh": "Ambitious 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A demanding and ambitious adult woman's voice in Standard Russian.", "descriptionEn": "A demanding and ambitious adult woman's voice in Standard Russian.", "descriptionZh": "一个demanding and ambitious adult woman's的声音 in Standard Russian.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Russian_AmbitiousWoman", "isActive": true, "languageSupports": [{"languageCode": "ru-RU", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "russian-reliableman", "name": "Reliable Man", "nameEn": "Reliable Man", "nameZh": "Reliable 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A steady and reliable middle-aged man's voice in Standard Russian.", "descriptionEn": "A steady and reliable middle-aged man's voice in Standard Russian.", "descriptionZh": "一个steady and reliable middle-aged man's的声音 in Standard Russian.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Russian_ReliableMan", "isActive": true, "languageSupports": [{"languageCode": "ru-RU", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "russian-crazyqueen", "name": "Crazy Girl", "nameEn": "Crazy Girl", "nameZh": "Crazy 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An energetic adult female \"crazy girl\" voice in Standard Russian, wild and unpredictable.", "descriptionEn": "An energetic adult female \"crazy girl\" voice in Standard Russian, wild and unpredictable.", "descriptionZh": "一个充满活力的 成年女性 \"crazy girl\"的声音 in Standard Russian, 狂野且不可预测.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "ru-RU", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "russian-pessimisticgirl", "name": "Pessimistic Girl", "nameEn": "Pessimistic Girl", "nameZh": "Pessimistic 女孩", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A compassionate adult female \"pessimistic girl\" voice in Standard Russian.", "descriptionEn": "A compassionate adult female \"pessimistic girl\" voice in Standard Russian.", "descriptionZh": "一个compassionate 成年女性 \"pessimistic girl\"的声音 in Standard Russian.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Russian_PessimisticGirl", "isActive": true, "languageSupports": [{"languageCode": "ru-RU", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "russian-attractiveguy", "name": "Attractive Guy", "nameEn": "Attractive Guy", "nameZh": "Attractive 小伙", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A deep-voiced and attractive adult guy's voice in Standard Russian.", "descriptionEn": "A deep-voiced and attractive adult guy's voice in Standard Russian.", "descriptionZh": "一个深沉的-voiced and attractive adult guy's的声音 in Standard Russian.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Russian_AttractiveGuy", "isActive": true, "languageSupports": [{"languageCode": "ru-RU", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "russian-bad-temperedboy", "name": "Bad-tempered Boy", "nameEn": "Bad-tempered Boy", "nameZh": "Bad-tempered 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A charming adult male \"bad-tempered boy\" voice in Standard Russian.", "descriptionEn": "A charming adult male \"bad-tempered boy\" voice in Standard Russian.", "descriptionZh": "一个charming 成年男性 \"bad-tempered boy\"的声音 in Standard Russian.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Russian_Bad-tempered<PERSON>oy", "isActive": true, "languageSupports": [{"languageCode": "ru-RU", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "italian-brave<PERSON><PERSON>ne", "name": "Brave Heroine", "nameEn": "Brave Heroine", "nameZh": "勇敢 Heroine", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A calm and brave middle-aged female heroine's voice in Italian.", "descriptionEn": "A calm and brave middle-aged female heroine's voice in Italian.", "descriptionZh": "一个平静的 and brave 中年女性 heroine's的声音 in Italian.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Italian_<PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "it-IT", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "italian-narrator", "name": "Narrator", "nameEn": "Narrator", "nameZh": "叙述者", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A classic middle-aged male narrator voice in Italian.", "descriptionEn": "A classic middle-aged male narrator voice in Italian.", "descriptionZh": "一个经典 中年男性 narrator的声音 in Italian.", "styles": ["Narrative"], "stylesEn": ["Narrative"], "stylesZh": ["叙述"], "voiceName": "Italian_Narrator", "isActive": true, "languageSupports": [{"languageCode": "it-IT", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "italian-wanderingsorcerer", "name": "Wandering Sorcerer", "nameEn": "Wandering Sorcerer", "nameZh": "Wandering Sorcerer", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A ruthless adult female wandering sorcerer's voice in Italian.", "descriptionEn": "A ruthless adult female wandering sorcerer's voice in Italian.", "descriptionZh": "一个ruthless 成年女性 wandering sorcerer's的声音 in Italian.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Italian_WanderingSorcerer", "isActive": true, "languageSupports": [{"languageCode": "it-IT", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "italian-diligentleader", "name": "Diligent Leader", "nameEn": "Diligent Leader", "nameZh": "Diligent Leader", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A calm and diligent adult female leader's voice in Italian.", "descriptionEn": "A calm and diligent adult female leader's voice in Italian.", "descriptionZh": "一个平静的 and diligent 成年女性 leader's的声音 in Italian.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Italian_DiligentLeader", "isActive": true, "languageSupports": [{"languageCode": "it-IT", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "dutch-kindhearted-girl", "name": "Kind-hearted girl", "nameEn": "Kind-hearted girl", "nameZh": "善良-hearted girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A warm and kind-hearted young adult female voice in Dutch.", "descriptionEn": "A warm and kind-hearted young adult female voice in Dutch.", "descriptionZh": "一个温暖的 and kind-hearted young 成年女性的声音 in Dutch.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Dutch_kindhearted_girl", "isActive": true, "languageSupports": [{"languageCode": "nl-NL", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "dutch-bossy-leader", "name": "Bossy leader", "nameEn": "Bossy leader", "nameZh": "Bossy leader", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A serious and bossy adult male leader's voice in Dutch.", "descriptionEn": "A serious and bossy adult male leader's voice in Dutch.", "descriptionZh": "一个serious and bossy 成年男性 leader's的声音 in Dutch.", "styles": ["Authoritative", "Serious"], "stylesEn": ["Authoritative", "Serious"], "stylesZh": ["权威", "严肃"], "voiceName": "Dutch_bossy_leader", "isActive": true, "languageSupports": [{"languageCode": "nl-NL", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "vietnamese-kindhearted-girl", "name": "Kind-hearted girl", "nameEn": "Kind-hearted girl", "nameZh": "善良-hearted girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A warm and kind-hearted young adult female voice in Vietnamese.", "descriptionEn": "A warm and kind-hearted young adult female voice in Vietnamese.", "descriptionZh": "一个温暖的 and kind-hearted young 成年女性的声音 in Vietnamese.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Vietnamese_kindhearted_girl", "isActive": true, "languageSupports": [{"languageCode": "vi-VN", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "arabic-calmwoman", "name": "Calm Woman", "nameEn": "Calm Woman", "nameZh": "平静女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A calm adult female Arabic voice, ideal for audiobooks and serene narration.", "descriptionEn": "A calm adult female Arabic voice, ideal for audiobooks and serene narration.", "descriptionZh": "一个平静的 成年女性 Arabic的声音, ，理想用于 有声书s and serene 叙述.", "styles": ["Gentle", "Narrative"], "stylesEn": ["Gentle", "Narrative"], "stylesZh": ["温和", "叙述"], "voiceName": "Arabic_CalmWoman", "isActive": true, "languageSupports": [{"languageCode": "ar-SA", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "arabic-friendlyguy", "name": "Friendly Guy", "nameEn": "Friendly Guy", "nameZh": "Friendly 小伙", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A natural and friendly adult male Arabic voice.", "descriptionEn": "A natural and friendly adult male Arabic voice.", "descriptionZh": "一个natural and 友好的 成年男性 Arabic的声音.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Arabic_FriendlyGuy", "isActive": true, "languageSupports": [{"languageCode": "ar-SA", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "turkish-calmwoman", "name": "Calm Woman", "nameEn": "Calm Woman", "nameZh": "平静女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A calm adult female Turkish voice, ideal for audiobooks.", "descriptionEn": "A calm adult female Turkish voice, ideal for audiobooks.", "descriptionZh": "一个平静的 成年女性 Turkish的声音, ，理想用于 有声书s.", "styles": ["Gentle", "Narrative"], "stylesEn": ["Gentle", "Narrative"], "stylesZh": ["温和", "叙述"], "voiceName": "Turkish_CalmWoman", "isActive": true, "languageSupports": [{"languageCode": "tr-TR", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "turkish-trustworthyman", "name": "Trustworthy man", "nameEn": "Trustworthy man", "nameZh": "Trustworthy man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A resonant and trustworthy adult male Turkish voice.", "descriptionEn": "A resonant and trustworthy adult male Turkish voice.", "descriptionZh": "一个洪亮的 and 可信赖的 成年男性 Turkish的声音.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Turkish_Trustworthyman", "isActive": true, "languageSupports": [{"languageCode": "tr-TR", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "ukrainian-calmwoman", "name": "Calm Woman", "nameEn": "Calm Woman", "nameZh": "平静女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A calm adult female Ukrainian voice, ideal for audiobooks.", "descriptionEn": "A calm adult female Ukrainian voice, ideal for audiobooks.", "descriptionZh": "一个平静的 成年女性 Ukrainian的声音, ，理想用于 有声书s.", "styles": ["Gentle", "Narrative"], "stylesEn": ["Gentle", "Narrative"], "stylesZh": ["温和", "叙述"], "voiceName": "Ukrainian_CalmWoman", "isActive": true, "languageSupports": [{"languageCode": "uk-UA", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "ukrainian-wisescholar", "name": "Wise Scholar", "nameEn": "Wise Scholar", "nameZh": "智慧 Scholar", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A conversational young adult male wise scholar's voice in Ukrainian.", "descriptionEn": "A conversational young adult male wise scholar's voice in Ukrainian.", "descriptionZh": "一个conversational young 成年男性 wise scholar's的声音 in Ukrainian.", "styles": ["Serious"], "stylesEn": ["Serious"], "stylesZh": ["严肃"], "voiceName": "Ukrainian_WiseScholar", "isActive": true, "languageSupports": [{"languageCode": "uk-UA", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "thai-male-1-sample8", "name": "<PERSON><PERSON>", "nameEn": "<PERSON><PERSON>", "nameZh": "Serene 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A magnetic and serene adult male voice in Thai.", "descriptionEn": "A magnetic and serene adult male voice in Thai.", "descriptionZh": "一个磁性的 and serene 成年男性的声音 in Thai.", "styles": ["Expressive"], "stylesEn": ["Expressive"], "stylesZh": ["表现力"], "voiceName": "Thai_male_1_sample8", "isActive": true, "languageSupports": [{"languageCode": "th-TH", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "thai-male-2-sample2", "name": "Friendly Man", "nameEn": "Friendly Man", "nameZh": "Friendly 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A lively and friendly adult male voice in Thai.", "descriptionEn": "A lively and friendly adult male voice in Thai.", "descriptionZh": "一个活泼的 and 友好的 成年男性的声音 in Thai.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Thai_male_2_sample2", "isActive": true, "languageSupports": [{"languageCode": "th-TH", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "thai-female-1-sample1", "name": "Confident Woman", "nameEn": "Confident Woman", "nameZh": "Confident 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A neutral and confident adult female voice in Thai.", "descriptionEn": "A neutral and confident adult female voice in Thai.", "descriptionZh": "一个neutral and confident 成年女性的声音 in Thai.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Thai_female_1_sample1", "isActive": true, "languageSupports": [{"languageCode": "th-TH", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "thai-female-2-sample2", "name": "Energetic Woman", "nameEn": "Energetic Woman", "nameZh": "Energetic 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An energetic adult female voice in Thai.", "descriptionEn": "An energetic adult female voice in Thai.", "descriptionZh": "一个充满活力的 成年女性的声音 in Thai.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Thai_female_2_sample2", "isActive": true, "languageSupports": [{"languageCode": "th-TH", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "polish-male-1-sample4", "name": "Male Narrator", "nameEn": "Male Narrator", "nameZh": "Male 叙述者", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A mature adult male narrator's voice in Polish.", "descriptionEn": "A mature adult male narrator's voice in Polish.", "descriptionZh": "一个mature 成年男性 narrator's的声音 in Polish.", "styles": ["Serious", "Narrative"], "stylesEn": ["Serious", "Narrative"], "stylesZh": ["严肃", "叙述"], "voiceName": "Polish_male_1_sample4", "isActive": true, "languageSupports": [{"languageCode": "pl-PL", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "polish-male-2-sample3", "name": "Male Anchor", "nameEn": "Male Anchor", "nameZh": "Male 主播", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An adult male broadcast anchor's voice in Polish.", "descriptionEn": "An adult male broadcast anchor's voice in Polish.", "descriptionZh": "一个成年男性 广播 anchor's的声音 in Polish.", "styles": ["Professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Polish_male_2_sample3", "isActive": true, "languageSupports": [{"languageCode": "pl-PL", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "polish-female-1-sample1", "name": "Calm Woman", "nameEn": "Calm Woman", "nameZh": "平静女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A calm adult female voice in Polish.", "descriptionEn": "A calm adult female voice in Polish.", "descriptionZh": "一个平静的 成年女性的声音 in Polish.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Polish_female_1_sample1", "isActive": true, "languageSupports": [{"languageCode": "pl-PL", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "polish-female-2-sample3", "name": "Casual Woman", "nameEn": "Casual Woman", "nameZh": "Casual 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A neutral and casual adult female voice in Polish.", "descriptionEn": "A neutral and casual adult female voice in Polish.", "descriptionZh": "一个neutral and casual 成年女性的声音 in Polish.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "Polish_female_2_sample3", "isActive": true, "languageSupports": [{"languageCode": "pl-PL", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "romanian-male-1-sample2", "name": "Reliable Man", "nameEn": "Reliable Man", "nameZh": "Reliable 男性", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A neutral and reliable adult male voice in Romanian.", "descriptionEn": "A neutral and reliable adult male voice in Romanian.", "descriptionZh": "一个neutral and reliable 成年男性的声音 in Romanian.", "styles": ["Authoritative"], "stylesEn": ["Authoritative"], "stylesZh": ["权威"], "voiceName": "Romanian_male_1_sample2", "isActive": true, "languageSupports": [{"languageCode": "ro-RO", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "romanian-male-2-sample1", "name": "Energetic Youth", "nameEn": "Energetic Youth", "nameZh": "Energetic Youth", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An energetic adult male youth's voice in Romanian.", "descriptionEn": "An energetic adult male youth's voice in Romanian.", "descriptionZh": "一个充满活力的 成年男性 年轻人's的声音 in Romanian.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Romanian_male_2_sample1", "isActive": true, "languageSupports": [{"languageCode": "ro-RO", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "romanian-female-1-sample4", "name": "Optimistic Youth", "nameEn": "Optimistic Youth", "nameZh": "Optimistic Youth", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A cheerful and optimistic adult female youth's voice in Romanian.", "descriptionEn": "A cheerful and optimistic adult female youth's voice in Romanian.", "descriptionZh": "一个愉快的 and optimistic 成年女性 年轻人's的声音 in Romanian.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Romanian_female_1_sample4", "isActive": true, "languageSupports": [{"languageCode": "ro-RO", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "romanian-female-2-sample1", "name": "Gentle Woman", "nameEn": "Gentle Woman", "nameZh": "温和 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A gentle adult female voice in Romanian.", "descriptionEn": "A gentle adult female voice in Romanian.", "descriptionZh": "一个温和的 成年女性的声音 in Romanian.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Romanian_female_2_sample1", "isActive": true, "languageSupports": [{"languageCode": "ro-RO", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "greek-male-1a-v1", "name": "Thoughtful Mentor", "nameEn": "Thoughtful Mentor", "nameZh": "Thoughtful Mentor", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "An intellectual and thoughtful adult male mentor's voice in Greek.", "descriptionEn": "An intellectual and thoughtful adult male mentor's voice in Greek.", "descriptionZh": "一个intellectual and thoughtful 成年男性 mentor's的声音 in Greek.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "greek_male_1a_v1", "isActive": true, "languageSupports": [{"languageCode": "el-GR", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "greek-female-1-sample1", "name": "Gentle Lady", "nameEn": "Gentle Lady", "nameZh": "温和 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A gentle adult lady's voice in Greek.", "descriptionEn": "A gentle adult lady's voice in Greek.", "descriptionZh": "一个温和的 adult lady's的声音 in Greek.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Greek_female_1_sample1", "isActive": true, "languageSupports": [{"languageCode": "el-GR", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "greek-female-2-sample3", "name": "Girl Next Door", "nameEn": "Girl Next Door", "nameZh": "女孩 Next Door", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A cheerful young adult \"girl next door\" voice in Greek.", "descriptionEn": "A cheerful young adult \"girl next door\" voice in Greek.", "descriptionZh": "一个愉快的 young adult \"girl next door\"的声音 in Greek.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Greek_female_2_sample3", "isActive": true, "languageSupports": [{"languageCode": "el-GR", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "czech-male-1-v1", "name": "Assured Presenter", "nameEn": "Assured Presenter", "nameZh": "Assured Presenter", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A serious and assured young adult male presenter's voice in Czech.", "descriptionEn": "A serious and assured young adult male presenter's voice in Czech.", "descriptionZh": "一个serious and assured young 成年男性 presenter's的声音 in Czech.", "styles": ["Professional", "Serious"], "stylesEn": ["Professional", "Serious"], "stylesZh": ["专业", "严肃"], "voiceName": "czech_male_1_v1", "isActive": true, "languageSupports": [{"languageCode": "cs-CZ", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "czech-female-5-v7", "name": "Steadfast Narrator", "nameEn": "Steadfast Narrator", "nameZh": "Steadfast 叙述者", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A steadfast adult female broadcast narrator's voice in Czech.", "descriptionEn": "A steadfast adult female broadcast narrator's voice in Czech.", "descriptionZh": "一个steadfast 成年女性 广播 narrator's的声音 in Czech.", "styles": ["Narrative"], "stylesEn": ["Narrative"], "stylesZh": ["叙述"], "voiceName": "czech_female_5_v7", "isActive": true, "languageSupports": [{"languageCode": "cs-CZ", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "czech-female-2-v2", "name": "Elegant Lady", "nameEn": "Elegant Lady", "nameZh": "Elegant 女士", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A graceful and elegant adult lady's voice in Czech.", "descriptionEn": "A graceful and elegant adult lady's voice in Czech.", "descriptionZh": "一个graceful and 优雅的 adult lady's的声音 in Czech.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "czech_female_2_v2", "isActive": true, "languageSupports": [{"languageCode": "cs-CZ", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "finnish-male-3-v1", "name": "Upbeat Man", "nameEn": "Upbeat Man", "nameZh": "Upbeat 男性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "An energetic and upbeat young adult female voice in Finnish.", "descriptionEn": "An energetic and upbeat young adult female voice in Finnish.", "descriptionZh": "一个充满活力的 and upbeat young 成年女性的声音 in Finnish.", "styles": ["Energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "finnish_male_3_v1", "isActive": true, "languageSupports": [{"languageCode": "fi-FI", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "finnish-male-1-v2", "name": "Friendly Boy", "nameEn": "Friendly Boy", "nameZh": "Friendly 男孩", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A deep and friendly young adult male voice in Finnish.", "descriptionEn": "A deep and friendly young adult male voice in Finnish.", "descriptionZh": "一个深沉的 and 友好的 young 成年男性的声音 in Finnish.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "finnish_male_1_v2", "isActive": true, "languageSupports": [{"languageCode": "fi-FI", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "finnish-female-4-v1", "name": "Assetive Woman", "nameEn": "Assetive Woman", "nameZh": "Assetive 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A determined and assertive adult female voice in Finnish.", "descriptionEn": "A determined and assertive adult female voice in Finnish.", "descriptionZh": "一个determined and assertive 成年女性的声音 in Finnish.", "styles": [], "stylesEn": [], "stylesZh": [], "voiceName": "finnish_female_4_v1", "isActive": true, "languageSupports": [{"languageCode": "fi-FI", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "hindi-male-1-v2", "name": "Trustworthy Advisor", "nameEn": "Trustworthy Advisor", "nameZh": "Trustworthy Advisor", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "A magnetic and trustworthy adult male advisor's voice in Hindi.", "descriptionEn": "A magnetic and trustworthy adult male advisor's voice in Hindi.", "descriptionZh": "一个磁性的 and 可信赖的 成年男性 advisor's的声音 in Hindi.", "styles": ["Authoritative", "Expressive"], "stylesEn": ["Authoritative", "Expressive"], "stylesZh": ["权威", "表现力"], "voiceName": "hindi_male_1_v2", "isActive": true, "languageSupports": [{"languageCode": "hi-IN", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "hindi-female-2-v1", "name": "Tranquil Woman", "nameEn": "Tranquil Woman", "nameZh": "Tranquil 女性", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A gentle and tranquil adult female voice in Hindi.", "descriptionEn": "A gentle and tranquil adult female voice in Hindi.", "descriptionZh": "一个温和的 and tranquil 成年女性的声音 in Hindi.", "styles": ["Gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "hindi_female_2_v1", "isActive": true, "languageSupports": [{"languageCode": "hi-IN", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}, {"slug": "hindi-female-1-v2", "name": "News Anchor", "nameEn": "News Anchor", "nameZh": "新闻主播", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "A calm adult female news anchor's voice in Hindi.", "descriptionEn": "A calm adult female news anchor's voice in Hindi.", "descriptionZh": "一个平静的 成年女性 news anchor's的声音 in Hindi.", "styles": ["Professional", "Gentle"], "stylesEn": ["Professional", "Gentle"], "stylesZh": ["专业", "温和"], "voiceName": "hindi_female_1_v2", "isActive": true, "languageSupports": [{"languageCode": "hi-IN", "quality": "standard", "isDefault": true, "sampleText": "Hello, this is a sample voice for text-to-speech demonstration.", "sampleUrl": null}]}]