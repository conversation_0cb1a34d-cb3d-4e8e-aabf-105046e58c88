// Minimax语音角色种子数据类型定义
export interface TtsRoleSeed {
  slug: string;
  name: string;
  nameEn: string;
  nameZh: string;
  genderEn: string | null;
  genderZh: string | null;
  avatarUrl: string | null;
  description: string | null;
  descriptionEn: string | null;
  descriptionZh: string | null;
  styles: string[];
  stylesEn: string[];
  stylesZh: string[];
  voiceName: string;
  isActive: boolean;
  languageSupports: {
    languageCode: string;
    quality: string;
    isDefault: boolean;
    sampleText: string | null;
    sampleUrl: string | null;
  }[];
}

export const minimaxVoicesSeed: TtsRoleSeed[] = require('./minimax-voices-seed.json');
