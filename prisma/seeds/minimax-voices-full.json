[{"slug": "minimax-english-expressive-narrator", "name": "Expressive Narrator", "nameEn": "Expressive Narrator", "nameZh": "英Expressive  Narrator", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Expressive Narrator voice", "descriptionEn": "Expressive Narrator voice with expressive style", "descriptionZh": "英语中性语音，富有表现力风格", "styles": ["expressive"], "stylesEn": ["Expressive"], "stylesZh": ["富有表现力"], "voiceName": "English_expressive_narrator", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "这是Expressive Narrator的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 6, "isActive": true}]}, {"slug": "minimax-english-radiant-girl", "name": "Radiant Girl", "nameEn": "Radiant Girl", "nameZh": "英女Radiant  Girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Radiant Girl voice", "descriptionEn": "Radiant Girl voice with energetic style", "descriptionZh": "英语女性语音，活力风格", "styles": ["energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "English_radiant_girl", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Radiant Girl的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-magnetic-voiced-man", "name": "Magnetic-voiced Male", "nameEn": "Magnetic-voiced Male", "nameZh": "英男Magnetic-voiced  Male", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Magnetic-voiced Male voice", "descriptionEn": "Magnetic-voiced Male voice with expressive style", "descriptionZh": "英语男性语音，富有表现力风格", "styles": ["expressive"], "stylesEn": ["Expressive"], "stylesZh": ["富有表现力"], "voiceName": "English_magnetic_voiced_man", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Magnetic-voiced Male的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-compelling-lady", "name": "Compelling Lady", "nameEn": "Compelling Lady", "nameZh": "英女Compelling  Lady", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Compelling Lady voice", "descriptionEn": "Compelling <PERSON> voice with expressive style", "descriptionZh": "英语女性语音，富有表现力风格", "styles": ["expressive"], "stylesEn": ["Expressive"], "stylesZh": ["富有表现力"], "voiceName": "English_Compelling_Lady", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Compelling Lady的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-trustworth-man", "name": "Trustworthy Man", "nameEn": "Trustworthy Man", "nameZh": "英男Trustworthy  Man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Trustworthy Man voice", "descriptionEn": "Trustworthy Man voice with normal style", "descriptionZh": "英语男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Trustworth_Man", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Trustworthy Man的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-calmwoman", "name": "Calm Woman", "nameEn": "Calm Woman", "nameZh": "英女Calm  Woman", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Calm Woman voice", "descriptionEn": "Calm Woman voice with gentle style", "descriptionZh": "英语女性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "English_CalmWoman", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Calm Woman的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-gentle-voicedman", "name": "<PERSON>tle-voiced Man", "nameEn": "<PERSON>tle-voiced Man", "nameZh": "英男<PERSON>tle-voiced  Man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "<PERSON><PERSON>-voiced <PERSON> voice", "descriptionEn": "Gentle-voiced Man voice with gentle style", "descriptionZh": "英语男性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "English_Gentle-<PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Gentle-voiced Man的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-gracefullady", "name": "Graceful Lady", "nameEn": "Graceful Lady", "nameZh": "英女Graceful  Lady", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Graceful Lady voice", "descriptionEn": "Graceful Lady voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_GracefulLady", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Graceful Lady的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-deep-voiced<PERSON><PERSON><PERSON>", "name": "Deep-voiced Gentleman", "nameEn": "Deep-voiced Gentleman", "nameZh": "英男Deep-voiced  Gentleman", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Deep-voiced Gentleman voice", "descriptionEn": "Deep-voiced Gentleman voice with gentle style", "descriptionZh": "英语男性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "English_Deep-<PERSON><PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Deep-voiced Gentleman的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-wiselady", "name": "<PERSON> Lady", "nameEn": "<PERSON> Lady", "nameZh": "英女Wise  Lady", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Wise Lady voice", "descriptionEn": "Wise Lady voice with wise style", "descriptionZh": "英语女性语音，智慧风格", "styles": ["wise"], "stylesEn": ["<PERSON>"], "stylesZh": ["智慧"], "voiceName": "English_Wiselady", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Wise Lady的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-captivatingstoryteller", "name": "Captivating Storyteller", "nameEn": "Captivating Storyteller", "nameZh": "英Captivating  Storyteller", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Captivating Storyteller voice", "descriptionEn": "Captivating Storyteller voice with expressive style", "descriptionZh": "英语中性语音，富有表现力风格", "styles": ["expressive"], "stylesEn": ["Expressive"], "stylesZh": ["富有表现力"], "voiceName": "English_CaptivatingStoryteller", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "这是Captivating Storyteller的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 6, "isActive": true}]}, {"slug": "minimax-english-<PERSON><PERSON><PERSON><PERSON>", "name": "Decent Young Man", "nameEn": "Decent Young Man", "nameZh": "英男Decent  Young  Man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Decent Young Man voice", "descriptionEn": "Decent Young Man voice with normal style", "descriptionZh": "英语男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_DecentYoungMan", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Decent Young Man的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-sentimentallady", "name": "Sentimental Lady", "nameEn": "Sentimental Lady", "nameZh": "英女Sentimental  Lady", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Sentimental Lady voice", "descriptionEn": "Sentimental Lady voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_SentimentalLady", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Sentimental Lady的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-<PERSON><PERSON><PERSON>", "name": "Imposing Queen", "nameEn": "Imposing Queen", "nameZh": "英女Imposing  Queen", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Imposing <PERSON> voice", "descriptionEn": "Imposing <PERSON> voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_ImposingManner", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Imposing Queen的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-sadteen", "name": "Teen Boy", "nameEn": "Teen Boy", "nameZh": "英男Teen  Boy", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Teen Boy voice", "descriptionEn": "Teen Boy voice with normal style", "descriptionZh": "英语男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_SadTeen", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Teen Boy的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-passionatewarrior", "name": "Passionate Warrior", "nameEn": "Passionate Warrior", "nameZh": "英男Passionate  Warrior", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Passionate Warrior voice", "descriptionEn": "Passionate Warrior voice with expressive style", "descriptionZh": "英语男性语音，富有表现力风格", "styles": ["expressive"], "stylesEn": ["Expressive"], "stylesZh": ["富有表现力"], "voiceName": "English_PassionateWarrior", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Passionate Warrior的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-wisescholar", "name": "Wise Scholar", "nameEn": "Wise Scholar", "nameZh": "英Wise  Scholar", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Wise Scholar voice", "descriptionEn": "Wise Scholar voice with wise style", "descriptionZh": "英语中性语音，智慧风格", "styles": ["wise"], "stylesEn": ["<PERSON>"], "stylesZh": ["智慧"], "voiceName": "English_WiseScholar", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Wise Scholar的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-soft-spokengirl", "name": "Soft-Spoken Girl", "nameEn": "Soft-Spoken Girl", "nameZh": "英女Soft- Spoken  Girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Soft-Spoken Girl voice", "descriptionEn": "Soft-Spoken Girl voice with gentle style", "descriptionZh": "英语女性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "English_Soft-spokenGirl", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Soft-Spoken Girl的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-serenewoman", "name": "<PERSON><PERSON>", "nameEn": "<PERSON><PERSON>", "nameZh": "英女Serene  Woman", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "<PERSON><PERSON> voice", "descriptionEn": "<PERSON><PERSON> Woman voice with gentle style", "descriptionZh": "英语女性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "English_SereneWoman", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Serene Woman的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-confidentwoman", "name": "Confident Woman", "nameEn": "Confident Woman", "nameZh": "英女Confident  Woman", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Confident Woman voice", "descriptionEn": "Confident Woman voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_ConfidentWoman", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Confident Woman的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-patientman", "name": "Patient Man", "nameEn": "Patient Man", "nameZh": "英男Patient  Man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Patient Man voice", "descriptionEn": "Patient Man voice with normal style", "descriptionZh": "英语男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_PatientMan", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Patient Man的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-comedian", "name": "Comedian", "nameEn": "Comedian", "nameZh": "英Comedian", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Comedian voice", "descriptionEn": "Comedian voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Comedian", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Comedian的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-boss<PERSON><PERSON>", "name": "Bossy Leader", "nameEn": "Bossy Leader", "nameZh": "英Bossy  Leader", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Bossy Leader voice", "descriptionEn": "Bossy Leader voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_BossyLeader", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Bossy Leader的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-strong-willedboy", "name": "Strong-Willed Boy", "nameEn": "Strong-Willed Boy", "nameZh": "英男Strong- Willed  Boy", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Strong-Willed <PERSON> voice", "descriptionEn": "Strong-Willed Boy voice with normal style", "descriptionZh": "英语男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Strong-<PERSON><PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Strong-Willed Boy的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-stressedlady", "name": "Stressed Lady", "nameEn": "Stressed Lady", "nameZh": "英女Stressed  Lady", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Stressed Lady voice", "descriptionEn": "Stressed Lady voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_StressedLady", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Stressed Lady的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-assertivequeen", "name": "Assertive Queen", "nameEn": "Assertive Queen", "nameZh": "英女Assertive  Queen", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Assertive Queen voice", "descriptionEn": "Assertive Queen voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_AssertiveQueen", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Assertive Queen的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-animecharacter", "name": "Female Narrator", "nameEn": "Female Narrator", "nameZh": "英女Female  Narrator", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Female Narrator voice", "descriptionEn": "Female Narrator voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_AnimeCharacter", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Female Narrator的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-jovialman", "name": "Jovial Man", "nameEn": "Jovial Man", "nameZh": "英男Jovial  Man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Jo<PERSON> Man voice", "descriptionEn": "Jovial Man voice with normal style", "descriptionZh": "英语男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_<PERSON><PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Jovial Man的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-whimsicalgirl", "name": "Whimsical Girl", "nameEn": "Whimsical Girl", "nameZh": "英女Whimsical  Girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Whimsical Girl voice", "descriptionEn": "Whimsical Girl voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_WhimsicalGirl", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Whimsical Girl的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-kind-heartedgirl", "name": "Kind-Hearted Girl", "nameEn": "Kind-Hearted Girl", "nameZh": "英女Kind- Hearted  Girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Kind-Hearted Girl voice", "descriptionEn": "Kind-Hearted Girl voice with gentle style", "descriptionZh": "英语女性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "English_Kind-<PERSON><PERSON><PERSON>l", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Kind-Hearted Girl的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-mature-woman", "name": "Mature Woman", "nameEn": "Mature Woman", "nameZh": "英女Mature  Woman", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Mature Woman voice", "descriptionEn": "Mature Woman voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Mature_Woman", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Mature Woman的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-cheerful-boy", "name": "Cheerful Boy", "nameEn": "Cheerful Boy", "nameZh": "英男Cheerful  Boy", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Cheerful Boy voice", "descriptionEn": "Cheerful Boy voice with energetic style", "descriptionZh": "英语男性语音，活力风格", "styles": ["energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "English_Cheerful_Boy", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Cheerful Boy的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-elegant-lady", "name": "Elegant Lady", "nameEn": "Elegant Lady", "nameZh": "英女Elegant  Lady", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Elegant Lady voice", "descriptionEn": "Elegant Lady voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Elegant_Lady", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Elegant Lady的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-friendly-man", "name": "Friendly Man", "nameEn": "Friendly Man", "nameZh": "英男Friendly  Man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Friendly Man voice", "descriptionEn": "Friendly Man voice with normal style", "descriptionZh": "英语男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Friendly_Man", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Friendly Man的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-sweet-girl", "name": "Sweet Girl", "nameEn": "Sweet Girl", "nameZh": "英女Sweet  Girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Sweet Girl voice", "descriptionEn": "Sweet Girl voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Sweet_Girl", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Sweet Girl的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-professional-host", "name": "Professional Host", "nameEn": "Professional Host", "nameZh": "英Professional  Host", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Professional Host voice", "descriptionEn": "Professional Host voice with professional style", "descriptionZh": "英语中性语音，专业风格", "styles": ["professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "English_Professional_Host", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "standard", "isDefault": true, "sampleText": "这是Professional Host的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 7, "isActive": true}]}, {"slug": "minimax-english-energetic-youth", "name": "Energetic Youth", "nameEn": "Energetic Youth", "nameZh": "英Energetic  Youth", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Energetic Youth voice", "descriptionEn": "Energetic Youth voice with energetic style", "descriptionZh": "英语中性语音，活力风格", "styles": ["energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "English_Energetic_Youth", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Energetic Youth的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-sophisticated-woman", "name": "Sophisticated Woman", "nameEn": "Sophisticated Woman", "nameZh": "英女Sophisticated  Woman", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Sophisticated Woman voice", "descriptionEn": "Sophisticated Woman voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Sophisticated_Woman", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Sophisticated Woman的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-reliable-man", "name": "Reliable Man", "nameEn": "Reliable Man", "nameZh": "英男Reliable  Man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Reliable Man voice", "descriptionEn": "Reliable Man voice with professional style", "descriptionZh": "英语男性语音，专业风格", "styles": ["professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "English_Reliable_Man", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Reliable Man的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-charming-lady", "name": "Charming Lady", "nameEn": "Charming Lady", "nameZh": "英女Charming  Lady", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Charming Lady voice", "descriptionEn": "Charming Lady voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Cha<PERSON>_Lady", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Charming Lady的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-thoughtful-scholar", "name": "Thoughtful Scholar", "nameEn": "Thoughtful Scholar", "nameZh": "英Thoughtful  Scholar", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Thoughtful Scholar voice", "descriptionEn": "Thoughtful Scholar voice with wise style", "descriptionZh": "英语中性语音，智慧风格", "styles": ["wise"], "stylesEn": ["<PERSON>"], "stylesZh": ["智慧"], "voiceName": "English_Thoughtful_Scholar", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Thoughtful Scholar的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-lively-girl", "name": "Lively Girl", "nameEn": "Lively Girl", "nameZh": "英女Lively  Girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Lively Girl voice", "descriptionEn": "Lively Girl voice with energetic style", "descriptionZh": "英语女性语音，活力风格", "styles": ["energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "English_Lively_Girl", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Lively Girl的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-distinguished-gentleman", "name": "Distinguished Gentleman", "nameEn": "Distinguished Gentleman", "nameZh": "英男Distinguished  Gentleman", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Distinguished Gentleman voice", "descriptionEn": "Distinguished Gentleman voice with gentle style", "descriptionZh": "英语男性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "English_Distinguished_Gentleman", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Distinguished Gentleman的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-warm-mother", "name": "Warm Mother", "nameEn": "Warm Mother", "nameZh": "英女Warm  Mother", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Warm Mother voice", "descriptionEn": "Warm Mother voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Warm_Mother", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Warm Mother的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-dynamic-speaker", "name": "Dynamic Speaker", "nameEn": "Dynamic Speaker", "nameZh": "英Dynamic  Speaker", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Dynamic Speaker voice", "descriptionEn": "Dynamic Speaker voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Dynamic_Speaker", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Dynamic Speaker的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--reliable-executive", "name": "Reliable Executive", "nameEn": "Reliable Executive", "nameZh": "Reliable  Executive", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Reliable Executive voice", "descriptionEn": "Reliable Executive voice with professional style", "descriptionZh": "中文中性语音，专业风格", "styles": ["professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Chinese (Mandarin)_Reliable_Executive", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Reliable Executive的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--news-anchor", "name": "News Anchor", "nameEn": "News Anchor", "nameZh": "News  Anchor", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "News Anchor voice", "descriptionEn": "News Anchor voice with professional style", "descriptionZh": "中文中性语音，专业风格", "styles": ["professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Chinese (Mandarin)_News_Anchor", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是News Anchor的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--unrestrained-young-man", "name": "Unrestrained <PERSON> Man", "nameEn": "Unrestrained <PERSON> Man", "nameZh": "男Unrestrained  Young  Man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Unrestrained Young Man voice", "descriptionEn": "Unrestrained Young Man voice with normal style", "descriptionZh": "中文男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Unrestrained_Young_Man", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Unrestrained Young Man的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--mature-woman", "name": "Mature Woman", "nameEn": "Mature Woman", "nameZh": "女Mature  Woman", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Mature Woman voice", "descriptionEn": "Mature Woman voice with normal style", "descriptionZh": "中文女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Mature_Woman", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Mature Woman的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-arrogant-miss", "name": "Arrogant Miss", "nameEn": "Arrogant Miss", "nameZh": "英女Arrogant  Miss", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Arrogant Miss voice", "descriptionEn": "Arrogant Miss voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Arrogant_Miss", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Arrogant Miss的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-robot-armor", "name": "Robot Armor", "nameEn": "Robot Armor", "nameZh": "英Robot  Armor", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Robot Armor voice", "descriptionEn": "Robot Armor voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Robot_Armor", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Robot Armor的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--kind-hearted-antie", "name": "Kind-hearted <PERSON><PERSON>", "nameEn": "Kind-hearted <PERSON><PERSON>", "nameZh": "女Kind-hearted  Antie", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Kind-hearted <PERSON><PERSON> voice", "descriptionEn": "Kind-hearted <PERSON><PERSON> voice with gentle style", "descriptionZh": "中文女性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Kind-hearted_<PERSON>e", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Kind-hearted Antie的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--hk-flight-attendant", "name": "HK Flight Attendant", "nameEn": "HK Flight Attendant", "nameZh": "H K  Flight  Attendant", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "HK Flight Attendant voice", "descriptionEn": "HK Flight Attendant voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_HK_Flight_Attendant", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是HK Flight Attendant的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--humorous-elder", "name": "Humorous Elder", "nameEn": "Humorous Elder", "nameZh": "男Humorous  Elder", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Humorous Elder voice", "descriptionEn": "Humorous Elder voice with normal style", "descriptionZh": "中文男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Humorous_Elder", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Humorous Elder的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--gentleman", "name": "Gentleman", "nameEn": "Gentleman", "nameZh": "男Gentleman", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Gentleman voice", "descriptionEn": "Gentleman voice with gentle style", "descriptionZh": "中文男性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Gentleman", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Gentleman的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--warm-bestie", "name": "Warm Bestie", "nameEn": "Warm Bestie", "nameZh": "Warm  Bestie", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Warm Bestie voice", "descriptionEn": "Warm Bestie voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Warm_<PERSON>ie", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Warm Bestie的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--stubborn-friend", "name": "<PERSON><PERSON><PERSON> Friend", "nameEn": "<PERSON><PERSON><PERSON> Friend", "nameZh": "<PERSON><PERSON><PERSON>  Friend", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Stubborn Friend voice", "descriptionEn": "<PERSON><PERSON><PERSON> Friend voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_<PERSON><PERSON><PERSON>_Friend", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Stubborn Friend的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--sweet-lady", "name": "Sweet Lady", "nameEn": "Sweet Lady", "nameZh": "女Sweet  Lady", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Sweet Lady voice", "descriptionEn": "<PERSON> Lady voice with normal style", "descriptionZh": "中文女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Sweet_Lady", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Sweet Lady的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--southern-young-man", "name": "Southern Young Man", "nameEn": "Southern Young Man", "nameZh": "男Southern  Young  Man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Southern Young Man voice", "descriptionEn": "Southern Young Man voice with normal style", "descriptionZh": "中文男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Southern_Young_Man", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Southern Young Man的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--wise-women", "name": "Wise Women", "nameEn": "Wise Women", "nameZh": "Wise  Women", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Wise Women voice", "descriptionEn": "Wise Women voice with wise style", "descriptionZh": "中文中性语音，智慧风格", "styles": ["wise"], "stylesEn": ["<PERSON>"], "stylesZh": ["智慧"], "voiceName": "Chinese (Mandarin)_Wise_Women", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Wise Women的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--gentle-youth", "name": "Gentle Youth", "nameEn": "Gentle Youth", "nameZh": "Gentle  Youth", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Gentle Youth voice", "descriptionEn": "Gentle Youth voice with gentle style", "descriptionZh": "中文中性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Gentle_Youth", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Gentle Youth的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--warm-girl", "name": "Warm Girl", "nameEn": "Warm Girl", "nameZh": "女Warm  Girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Warm Girl voice", "descriptionEn": "Warm Girl voice with normal style", "descriptionZh": "中文女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Warm_Girl", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Warm Girl的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--male-announcer", "name": "Male Announcer", "nameEn": "Male Announcer", "nameZh": "男Male  Announcer", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Male Announcer voice", "descriptionEn": "Male Announcer voice with professional style", "descriptionZh": "中文男性语音，专业风格", "styles": ["professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Chinese (Mandarin)_Male_Announcer", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Male Announcer的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--kind-hearted-elder", "name": "Kind-hearted Elder", "nameEn": "Kind-hearted Elder", "nameZh": "男Kind-hearted  Elder", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Kind-hearted Elder voice", "descriptionEn": "Kind-hearted Elder voice with gentle style", "descriptionZh": "中文男性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Kind-hearted_Elder", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Kind-hearted Elder的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--cute-spirit", "name": "Cute Spirit", "nameEn": "Cute Spirit", "nameZh": "Cute  Spirit", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Cute Spirit voice", "descriptionEn": "Cute Spirit voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Cute_Spirit", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Cute Spirit的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--radio-host", "name": "Radio Host", "nameEn": "Radio Host", "nameZh": "Radio  Host", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Radio Host voice", "descriptionEn": "Radio Host voice with professional style", "descriptionZh": "中文中性语音，专业风格", "styles": ["professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Chinese (Mandarin)_Radio_Host", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Radio Host的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--lyrical-voice", "name": "Lyrical Voice", "nameEn": "Lyrical Voice", "nameZh": "Lyrical  Voice", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Lyrical Voice voice", "descriptionEn": "Lyrical Voice voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Lyrical_Voice", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Lyrical Voice的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--straightforward-boy", "name": "Straightforward Boy", "nameEn": "Straightforward Boy", "nameZh": "男Straightforward  Boy", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Straightforward Boy voice", "descriptionEn": "Straightforward Boy voice with normal style", "descriptionZh": "中文男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Straightforward_Boy", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Straightforward Boy的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--sincere-adult", "name": "Sincere Adult", "nameEn": "Sincere Adult", "nameZh": "Sincere  Adult", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Sincere Adult voice", "descriptionEn": "Sincere Adult voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Sincere_Adult", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Sincere Adult的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--gentle-senior", "name": "Gentle Senior", "nameEn": "Gentle Senior", "nameZh": "Gentle  Senior", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Gentle Senior voice", "descriptionEn": "Gentle Senior voice with gentle style", "descriptionZh": "中文中性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Gentle_Senior", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Gentle Senior的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--crisp-girl", "name": "Crisp Girl", "nameEn": "Crisp Girl", "nameZh": "女Crisp  Girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "<PERSON><PERSON><PERSON> Girl voice", "descriptionEn": "Crisp Girl voice with normal style", "descriptionZh": "中文女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Crisp_Girl", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Crisp Girl的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--pure-hearted-boy", "name": "Pure-hearted Boy", "nameEn": "Pure-hearted Boy", "nameZh": "男Pure-hearted  Boy", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Pure-hearted Boy voice", "descriptionEn": "Pure-hearted Boy voice with normal style", "descriptionZh": "中文男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Pure-hearted_Boy", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Pure-hearted Boy的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--soft-girl", "name": "Soft Girl", "nameEn": "Soft Girl", "nameZh": "女Soft  Girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Soft Girl voice", "descriptionEn": "Soft Girl voice with gentle style", "descriptionZh": "中文女性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Soft_Girl", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Soft Girl的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--intellectualgirl", "name": "Intellectual Girl", "nameEn": "Intellectual Girl", "nameZh": "女Intellectual  Girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Intellectual Girl voice", "descriptionEn": "Intellectual Girl voice with wise style", "descriptionZh": "中文女性语音，智慧风格", "styles": ["wise"], "stylesEn": ["<PERSON>"], "stylesZh": ["智慧"], "voiceName": "Chinese (Mandarin)_IntellectualGirl", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Intellectual Girl的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--warm-heartedgirl", "name": "Warm-hearted Girl", "nameEn": "Warm-hearted Girl", "nameZh": "女Warm-hearted  Girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Warm-hearted Girl voice", "descriptionEn": "Warm-hearted Girl voice with normal style", "descriptionZh": "中文女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Warm_HeartedGirl", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Warm-hearted Girl的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-intellectualsenior", "name": "Intellectual Senior", "nameEn": "Intellectual Senior", "nameZh": "日Intellectual  Senior", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Intellectual Senior voice", "descriptionEn": "Intellectual Senior voice with wise style", "descriptionZh": "日语中性语音，智慧风格", "styles": ["wise"], "stylesEn": ["<PERSON>"], "stylesZh": ["智慧"], "voiceName": "Japanese_IntellectualSenior", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Intellectual Senior的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-decisiveprincess", "name": "Decisive Princess", "nameEn": "Decisive Princess", "nameZh": "日Decisive  Princess", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Decisive Princess voice", "descriptionEn": "Decisive Princess voice with normal style", "descriptionZh": "日语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_DecisivePrincess", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Decisive Princess的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-loyalknight", "name": "<PERSON><PERSON>", "nameEn": "<PERSON><PERSON>", "nameZh": "日男Loyal  Knight", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "<PERSON><PERSON> voice", "descriptionEn": "Loyal <PERSON> voice with normal style", "descriptionZh": "日语男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_LoyalKnight", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Loyal Knight的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-dominantman", "name": "Dominant Man", "nameEn": "Dominant Man", "nameZh": "日男Dominant  Man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Dominant Man voice", "descriptionEn": "Dominant Man voice with normal style", "descriptionZh": "日语男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_DominantMan", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Dominant Man的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-seriouscommander", "name": "Serious Commander", "nameEn": "Serious Commander", "nameZh": "日男Serious  Commander", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Serious Commander voice", "descriptionEn": "Serious Commander voice with normal style", "descriptionZh": "日语男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_SeriousCommander", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Serious Commander的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-coldqueen", "name": "Cold Queen", "nameEn": "Cold Queen", "nameZh": "日女Cold  Queen", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Cold Queen voice", "descriptionEn": "Cold Queen voice with normal style", "descriptionZh": "日语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_ColdQueen", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Cold Queen的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-dependablewoman", "name": "Dependable Woman", "nameEn": "Dependable Woman", "nameZh": "日女Dependable  Woman", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Dependable Woman voice", "descriptionEn": "Dependable Woman voice with normal style", "descriptionZh": "日语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_DependableWoman", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Dependable Woman的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-gentlebutler", "name": "Gen<PERSON> Butler", "nameEn": "Gen<PERSON> Butler", "nameZh": "日Gentle  Butler", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "<PERSON><PERSON> Butler voice", "descriptionEn": "<PERSON><PERSON> voice with gentle style", "descriptionZh": "日语中性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Japanese_GentleButler", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Gentle Butler的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-kindlady", "name": "Kind Lady", "nameEn": "Kind Lady", "nameZh": "日女Kind  Lady", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Kind Lady voice", "descriptionEn": "Kind Lady voice with gentle style", "descriptionZh": "日语女性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Japanese_KindLady", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Kind Lady的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-calmlady", "name": "Calm Lady", "nameEn": "Calm Lady", "nameZh": "日女Calm  Lady", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Calm Lady voice", "descriptionEn": "Calm Lady voice with gentle style", "descriptionZh": "日语女性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Japanese_CalmLady", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Calm Lady的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-optimisticyouth", "name": "Optimistic Youth", "nameEn": "Optimistic Youth", "nameZh": "日Optimistic  Youth", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Optimistic Youth voice", "descriptionEn": "Optimistic Youth voice with normal style", "descriptionZh": "日语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_OptimisticYouth", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Optimistic Youth的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-generousizakayaowner", "name": "Generous Izakaya Owner", "nameEn": "Generous Izakaya Owner", "nameZh": "日Generous  Izakaya  Owner", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Generous Izakaya Owner voice", "descriptionEn": "Generous Izakaya Owner voice with normal style", "descriptionZh": "日语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_GenerousIzakayaOwner", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Generous Izakaya Owner的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-sportystudent", "name": "Sporty Student", "nameEn": "Sporty Student", "nameZh": "日Sporty  Student", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Sporty Student voice", "descriptionEn": "Sporty Student voice with normal style", "descriptionZh": "日语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_SportyStudent", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Sporty Student的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-innocentboy", "name": "Innocent Boy", "nameEn": "Innocent Boy", "nameZh": "日男Innocent  Boy", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Innocent Boy voice", "descriptionEn": "Innocent Boy voice with normal style", "descriptionZh": "日语男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_Innocent<PERSON><PERSON>", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Innocent Boy的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-gracefulmaiden", "name": "Graceful Maiden", "nameEn": "Graceful Maiden", "nameZh": "日女Graceful  Maiden", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Graceful <PERSON> voice", "descriptionEn": "Graceful <PERSON> voice with normal style", "descriptionZh": "日语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_GracefulMaiden", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Graceful Maiden的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-professionalhost-f-", "name": "Professional Female Host", "nameEn": "Professional Female Host", "nameZh": "港女Professional  Female  Host", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Professional Female Host voice", "descriptionEn": "Professional Female Host voice with professional style", "descriptionZh": "粤语女性语音，专业风格", "styles": ["professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Cantonese_ProfessionalHost（F)", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "standard", "isDefault": true, "sampleText": "这是Professional Female Host的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 7, "isActive": true}]}, {"slug": "minimax-cantonese-gentlelady", "name": "Gentle Lady", "nameEn": "Gentle Lady", "nameZh": "港女Gentle  Lady", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Gentle Lady voice", "descriptionEn": "Gentle Lady voice with gentle style", "descriptionZh": "粤语女性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Cantonese_GentleLady", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Gentle Lady的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-professionalhost-m-", "name": "Professional Male Host", "nameEn": "Professional Male Host", "nameZh": "港男Professional  Male  Host", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Professional Male Host voice", "descriptionEn": "Professional Male Host voice with professional style", "descriptionZh": "粤语男性语音，专业风格", "styles": ["professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Cantonese_ProfessionalHost（M)", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "standard", "isDefault": true, "sampleText": "这是Professional Male Host的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 7, "isActive": true}]}, {"slug": "minimax-cantonese-playfulman", "name": "Playful Man", "nameEn": "Playful Man", "nameZh": "港男Playful  Man", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Playful Man voice", "descriptionEn": "Playful Man voice with normal style", "descriptionZh": "粤语男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Cantonese_PlayfulMan", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Playful Man的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-cutegirl", "name": "Cute Girl", "nameEn": "Cute Girl", "nameZh": "港女Cute  Girl", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Cute Girl voice", "descriptionEn": "Cute Girl voice with normal style", "descriptionZh": "粤语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Cantonese_CuteGirl", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Cute Girl的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-kindwoman", "name": "Kind Woman", "nameEn": "Kind Woman", "nameZh": "港女Kind  Woman", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Kind Woman voice", "descriptionEn": "Kind Woman voice with gentle style", "descriptionZh": "粤语女性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Cantonese_KindWoman", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Kind Woman的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-authoritative-speaker", "name": "Authoritative Speaker", "nameEn": "Authoritative Speaker", "nameZh": "英Authoritative  Speaker", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Authoritative Speaker voice", "descriptionEn": "Authoritative Speaker voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Authoritative_Speaker", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Authoritative Speaker的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-bright-teenager", "name": "<PERSON>", "nameEn": "<PERSON>", "nameZh": "英Bright  Teenager", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Bright Teenager voice", "descriptionEn": "Bright Teenager voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Bright_Teenager", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Bright Teenager的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-caring-mother", "name": "Caring Mother", "nameEn": "Caring Mother", "nameZh": "英女Caring  Mother", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Caring Mother voice", "descriptionEn": "Caring Mother voice with normal style", "descriptionZh": "英语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Caring_Mother", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Caring Mother的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-decisive-leader", "name": "Decisive Leader", "nameEn": "Decisive Leader", "nameZh": "英Decisive  Leader", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Decisive Leader voice", "descriptionEn": "Decisive Leader voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Decisive_Leader", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Decisive Leader的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-enthusiastic-teacher", "name": "Enthusiastic Teacher", "nameEn": "Enthusiastic Teacher", "nameZh": "英Enthusiastic  Teacher", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Enthusiastic Teacher voice", "descriptionEn": "Enthusiastic Teacher voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Enthusiastic_Teacher", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Enthusiastic Teacher的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-formal-presenter", "name": "Formal Presenter", "nameEn": "Formal Presenter", "nameZh": "英Formal  Presenter", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Formal Presenter voice", "descriptionEn": "Formal Presenter voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Formal_Presenter", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Formal Presenter的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-gentle-father", "name": "Gentle Father", "nameEn": "Gentle Father", "nameZh": "英男Gentle  Father", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Gentle Father voice", "descriptionEn": "Gentle Father voice with gentle style", "descriptionZh": "英语男性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "English_Gentle_Father", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Gentle Father的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-happy-child", "name": "Happy Child", "nameEn": "Happy Child", "nameZh": "英Happy  Child", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Happy Child voice", "descriptionEn": "Happy Child voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Happy_Child", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Happy Child的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-inspiring-coach", "name": "Inspiring Coach", "nameEn": "Inspiring Coach", "nameZh": "英Inspiring  Coach", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Inspiring Coach voice", "descriptionEn": "Inspiring Coach voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Inspiring_Coach", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Inspiring Coach的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-joyful-friend", "name": "Joyful Friend", "nameEn": "Joyful Friend", "nameZh": "英Joyful  Friend", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Joyful Friend voice", "descriptionEn": "Joyful Friend voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Joyful_Friend", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Joyful Friend的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-kind-grandmother", "name": "Kind Grandmother", "nameEn": "Kind Grandmother", "nameZh": "英女Kind  Grandmother", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Kind Grandmother voice", "descriptionEn": "Kind Grandmother voice with gentle style", "descriptionZh": "英语女性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "English_Kind_Grandmother", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Kind Grandmother的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-lively-student", "name": "Lively Student", "nameEn": "Lively Student", "nameZh": "英Lively  Student", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Lively Student voice", "descriptionEn": "Lively Student voice with energetic style", "descriptionZh": "英语中性语音，活力风格", "styles": ["energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "English_Lively_Student", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Lively Student的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-motivational-speaker", "name": "Motivational Speaker", "nameEn": "Motivational Speaker", "nameZh": "英Motivational  Speaker", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Motivational Speaker voice", "descriptionEn": "Motivational Speaker voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Motivational_Speaker", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Motivational Speaker的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-natural-conversationalist", "name": "Natural Conversationalist", "nameEn": "Natural Conversationalist", "nameZh": "英Natural  Conversationalist", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Natural Conversationalist voice", "descriptionEn": "Natural Conversationalist voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Natural_Conversationalist", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Natural Conversationalist的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-optimistic-guide", "name": "Optimistic Guide", "nameEn": "Optimistic Guide", "nameZh": "英Optimistic  Guide", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Optimistic Guide voice", "descriptionEn": "Optimistic Guide voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Optimistic_Guide", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Optimistic Guide的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-passionate-artist", "name": "Passionate Artist", "nameEn": "Passionate Artist", "nameZh": "英Passionate  Artist", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Passionate Artist voice", "descriptionEn": "Passionate Artist voice with expressive style", "descriptionZh": "英语中性语音，富有表现力风格", "styles": ["expressive"], "stylesEn": ["Expressive"], "stylesZh": ["富有表现力"], "voiceName": "English_Passionate_Artist", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Passionate Artist的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-quiet-librarian", "name": "Quiet Librarian", "nameEn": "Quiet Librarian", "nameZh": "英Quiet  Librarian", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Quiet Librarian voice", "descriptionEn": "Quiet Librarian voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Quiet_Librarian", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Quiet Librarian的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-relaxed-narrator", "name": "Relaxed Narrator", "nameEn": "Relaxed Narrator", "nameZh": "英Relaxed  Narrator", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Relaxed Narrator voice", "descriptionEn": "Relaxed Narra<PERSON> voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Relaxed_Narrator", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Relaxed Narrator的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-serious-judge", "name": "Serious Judge", "nameEn": "Serious Judge", "nameZh": "英Serious  Judge", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Serious Judge voice", "descriptionEn": "Serious Judge voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Serious_Judge", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Serious Judge的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-thoughtful-philosopher", "name": "Thoughtful Philosopher", "nameEn": "Thoughtful Philosopher", "nameZh": "英Thoughtful  Philosopher", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Thoughtful Philosopher voice", "descriptionEn": "Thoughtful Philosopher voice with wise style", "descriptionZh": "英语中性语音，智慧风格", "styles": ["wise"], "stylesEn": ["<PERSON>"], "stylesZh": ["智慧"], "voiceName": "English_Thoughtful_Philosopher", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Thoughtful Philosopher的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-understanding-counselor", "name": "Understanding Counselor", "nameEn": "Understanding Counselor", "nameZh": "英Understanding  Counselor", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Understanding Counselor voice", "descriptionEn": "Understanding Counselor voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Understanding_Counselor", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Understanding Counselor的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-vibrant-performer", "name": "Vibrant <PERSON>", "nameEn": "Vibrant <PERSON>", "nameZh": "英Vibrant  Performer", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Vibrant Performer voice", "descriptionEn": "Vibrant Performer voice with energetic style", "descriptionZh": "英语中性语音，活力风格", "styles": ["energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "English_Vibrant_Performer", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Vibrant Performer的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-wise-mentor", "name": "Wise Mentor", "nameEn": "Wise Mentor", "nameZh": "英Wise  Mentor", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Wise Mentor voice", "descriptionEn": "Wise Mentor voice with wise style", "descriptionZh": "英语中性语音，智慧风格", "styles": ["wise"], "stylesEn": ["<PERSON>"], "stylesZh": ["智慧"], "voiceName": "English_<PERSON>_Mentor", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Wise Mentor的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-youthful-explorer", "name": "Youthful Explorer", "nameEn": "Youthful Explorer", "nameZh": "英Youthful  Explorer", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Youthful Explorer voice", "descriptionEn": "Youthful Explorer voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Youthful_Explorer", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Youthful Explorer的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-english-zealous-advocate", "name": "Zealous Advocate", "nameEn": "Zealous Advocate", "nameZh": "英Zealous  Advocate", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Zealous Advocate voice", "descriptionEn": "Zealous Advocate voice with normal style", "descriptionZh": "英语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "English_Zealous_Advocate", "isActive": true, "languageSupports": [{"languageCode": "en-US", "quality": "basic", "isDefault": true, "sampleText": "这是Zealous Advocate的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--professional-teacher", "name": "Professional Teacher", "nameEn": "Professional Teacher", "nameZh": "Professional  Teacher", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Professional Teacher voice", "descriptionEn": "Professional Teacher voice with professional style", "descriptionZh": "中文中性语音，专业风格", "styles": ["professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Chinese (Mandarin)_Professional_Teacher", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "这是Professional Teacher的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 7, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--friendly-neighbor", "name": "Friendly Neighbor", "nameEn": "Friendly Neighbor", "nameZh": "Friendly  Neighbor", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "<PERSON>eighbor voice", "descriptionEn": "Friendly <PERSON>eighbor voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Friendly_Neighbor", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Friendly Neighbor的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--wise-grandfather", "name": "<PERSON> Grandfather", "nameEn": "<PERSON> Grandfather", "nameZh": "男Wise  Grandfather", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Wise Grandfather voice", "descriptionEn": "Wise Grandfather voice with wise style", "descriptionZh": "中文男性语音，智慧风格", "styles": ["wise"], "stylesEn": ["<PERSON>"], "stylesZh": ["智慧"], "voiceName": "Chinese (Mandarin)_Wise_Grandfather", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Wise Grandfather的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--cheerful-student", "name": "Cheerful Student", "nameEn": "Cheerful Student", "nameZh": "Cheerful  Student", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Cheerful Student voice", "descriptionEn": "Cheerful Student voice with energetic style", "descriptionZh": "中文中性语音，活力风格", "styles": ["energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Chinese (Mandarin)_Cheerful_Student", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Cheerful Student的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--calm-therapist", "name": "Calm Therapist", "nameEn": "Calm Therapist", "nameZh": "Calm  Therapist", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Calm Therapist voice", "descriptionEn": "Calm Therapist voice with gentle style", "descriptionZh": "中文中性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Calm_Therapist", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Calm Therapist的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--energetic-host", "name": "Energetic Host", "nameEn": "Energetic Host", "nameZh": "Energetic  Host", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Energetic Host voice", "descriptionEn": "Energetic Host voice with professional, energetic style", "descriptionZh": "中文中性语音，专业、活力风格", "styles": ["professional", "energetic"], "stylesEn": ["Professional", "Energetic"], "stylesZh": ["专业", "活力"], "voiceName": "Chinese (Mandarin)_Energetic_Host", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "standard", "isDefault": true, "sampleText": "这是Energetic Host的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 6, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--gentle-nurse", "name": "Gentle Nurse", "nameEn": "Gentle Nurse", "nameZh": "Gentle  Nurse", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Gentle Nurse voice", "descriptionEn": "Gentle Nurse voice with gentle style", "descriptionZh": "中文中性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Gentle_Nurse", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Gentle Nurse的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--confident-ceo", "name": "Confident CEO", "nameEn": "Confident CEO", "nameZh": "Confident  C E O", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Confident CEO voice", "descriptionEn": "Confident CEO voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Confident_CEO", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Confident CEO的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--warm-counselor", "name": "Warm Counselor", "nameEn": "Warm Counselor", "nameZh": "Warm  Counselor", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Warm Counselor voice", "descriptionEn": "Warm Counselor voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Warm_Counselor", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Warm Counselor的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--playful-child", "name": "Playful Child", "nameEn": "Playful Child", "nameZh": "Playful  Child", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Playful Child voice", "descriptionEn": "Playful Child voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Playful_Child", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Playful Child的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--serious-doctor", "name": "Serious Doctor", "nameEn": "Serious Doctor", "nameZh": "Serious  Doctor", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Serious Doctor voice", "descriptionEn": "Serious Doctor voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Serious_Doctor", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Serious Doctor的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--kind-volunteer", "name": "Kind Volunteer", "nameEn": "Kind Volunteer", "nameZh": "Kind  Volunteer", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Kind Volunteer voice", "descriptionEn": "Kind Volunteer voice with gentle style", "descriptionZh": "中文中性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Chinese (Mandarin)_Kind_Volunteer", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Kind Volunteer的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--lively-performer", "name": "Lively Performer", "nameEn": "Lively Performer", "nameZh": "Lively  Performer", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Lively Performer voice", "descriptionEn": "Lively Performer voice with energetic style", "descriptionZh": "中文中性语音，活力风格", "styles": ["energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Chinese (Mandarin)_Lively_Performer", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Lively Performer的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--thoughtful-writer", "name": "Thoughtful Writer", "nameEn": "Thoughtful Writer", "nameZh": "Thoughtful  Writer", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Thoughtful Writer voice", "descriptionEn": "Thoughtful Writer voice with wise style", "descriptionZh": "中文中性语音，智慧风格", "styles": ["wise"], "stylesEn": ["<PERSON>"], "stylesZh": ["智慧"], "voiceName": "Chinese (Mandarin)_Thoughtful_Writer", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Thoughtful Writer的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--caring-parent", "name": "Caring Parent", "nameEn": "Caring Parent", "nameZh": "Caring  Parent", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Caring Parent voice", "descriptionEn": "Caring Parent voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Caring_Parent", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Caring Parent的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--dynamic-trainer", "name": "Dynamic Trainer", "nameEn": "Dynamic Trainer", "nameZh": "Dynamic  Trainer", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Dynamic Trainer voice", "descriptionEn": "Dynamic Trainer voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Dynamic_Trainer", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Dynamic Trainer的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--peaceful-monk", "name": "Peaceful Monk", "nameEn": "Peaceful Monk", "nameZh": "Peaceful  Monk", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Peaceful <PERSON> voice", "descriptionEn": "Peaceful <PERSON> voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Peaceful_Monk", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Peaceful Monk的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--enthusiastic-guide", "name": "Enthusiastic Guide", "nameEn": "Enthusiastic Guide", "nameZh": "Enthusiastic  Guide", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Enthusiastic Guide voice", "descriptionEn": "Enthusiastic Guide voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Enthusiastic_Guide", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Enthusiastic Guide的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--reliable-assistant", "name": "Reliable Assistant", "nameEn": "Reliable Assistant", "nameZh": "Reliable  Assistant", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Reliable Assistant voice", "descriptionEn": "Reliable Assistant voice with professional style", "descriptionZh": "中文中性语音，专业风格", "styles": ["professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Chinese (Mandarin)_Reliable_Assistant", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Reliable Assistant的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-chinese--mandarin--creative-designer", "name": "Creative Designer", "nameEn": "Creative Designer", "nameZh": "Creative  Designer", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Creative Designer voice", "descriptionEn": "Creative Designer voice with normal style", "descriptionZh": "中文中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Chinese (Mandarin)_Creative_Designer", "isActive": true, "languageSupports": [{"languageCode": "zh-CN", "quality": "basic", "isDefault": true, "sampleText": "这是Creative Designer的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-polite-student", "name": "Polite Student", "nameEn": "Polite Student", "nameZh": "日Polite  Student", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Polite Student voice", "descriptionEn": "Polite Student voice with normal style", "descriptionZh": "日语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_Polite_Student", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Polite Student的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-respectful-elder", "name": "Respectful Elder", "nameEn": "Respectful Elder", "nameZh": "日男Respectful  Elder", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Respectful Elder voice", "descriptionEn": "Respectful Elder voice with normal style", "descriptionZh": "日语男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_Respectful_Elder", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Respectful Elder的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-cheerful-friend", "name": "Cheerful Friend", "nameEn": "Cheerful Friend", "nameZh": "日Cheerful  Friend", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Cheerful Friend voice", "descriptionEn": "Cheerful Friend voice with energetic style", "descriptionZh": "日语中性语音，活力风格", "styles": ["energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Japanese_Cheerful_Friend", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Cheerful Friend的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-serious-teacher", "name": "Serious Teacher", "nameEn": "Serious Teacher", "nameZh": "日Serious  Teacher", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Serious Teacher voice", "descriptionEn": "Serious Teacher voice with normal style", "descriptionZh": "日语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_Serious_Teacher", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Serious Teacher的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-gentle-mother", "name": "Gentle Mother", "nameEn": "Gentle Mother", "nameZh": "日女Gentle  Mother", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "Gentle Mother voice", "descriptionEn": "Gentle Mother voice with gentle style", "descriptionZh": "日语女性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Japanese_Gentle_Mother", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Gentle Mother的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-strong-warrior", "name": "Strong Warrior", "nameEn": "Strong Warrior", "nameZh": "日男Strong  Warrior", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Strong Warrior voice", "descriptionEn": "Strong Warrior voice with normal style", "descriptionZh": "日语男性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_Strong_Warrior", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Strong Warrior的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-wise-sage", "name": "Wise Sage", "nameEn": "Wise Sage", "nameZh": "日Wise  Sage", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "<PERSON> voice", "descriptionEn": "<PERSON> Sage voice with wise style", "descriptionZh": "日语中性语音，智慧风格", "styles": ["wise"], "stylesEn": ["<PERSON>"], "stylesZh": ["智慧"], "voiceName": "Japanese_Wise_Sage", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Wise Sage的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-playful-child", "name": "Playful Child", "nameEn": "Playful Child", "nameZh": "日Playful  Child", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Playful Child voice", "descriptionEn": "Playful Child voice with normal style", "descriptionZh": "日语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_Playful_Child", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Playful Child的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-elegant-geisha", "name": "Elegant Geisha", "nameEn": "Elegant Geisha", "nameZh": "日Elegant  Geisha", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Elegant G<PERSON>sha voice", "descriptionEn": "Elegant Geisha voice with normal style", "descriptionZh": "日语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_Elegant_Geisha", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Elegant Geisha的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-brave-samurai", "name": "Brave Samurai", "nameEn": "Brave Samurai", "nameZh": "日Brave  Samurai", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Brave Samurai voice", "descriptionEn": "Brave Samurai voice with normal style", "descriptionZh": "日语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_Brave_Samurai", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Brave Samurai的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-kind-healer", "name": "<PERSON> Healer", "nameEn": "<PERSON> Healer", "nameZh": "日Kind  Healer", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Kind Healer voice", "descriptionEn": "Kind Healer voice with gentle style", "descriptionZh": "日语中性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Japanese_Kind_Healer", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Kind Healer的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-mysterious-ninja", "name": "Mysterious Ninja", "nameEn": "Mysterious Ninja", "nameZh": "日Mysterious  Ninja", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Mysterious Ninja voice", "descriptionEn": "Mysterious Ninja voice with normal style", "descriptionZh": "日语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_Mysterious_Ninja", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Mysterious Ninja的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-honorable-master", "name": "Honorable Master", "nameEn": "Honorable Master", "nameZh": "日Honorable  Master", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Honorable Master voice", "descriptionEn": "Honorable Master voice with normal style", "descriptionZh": "日语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_Honorable_Master", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Honorable Master的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-sweet-maiden", "name": "<PERSON> Maiden", "nameEn": "<PERSON> Maiden", "nameZh": "日女Sweet  Maiden", "genderEn": "Female", "genderZh": "女性", "avatarUrl": null, "description": "<PERSON> voice", "descriptionEn": "<PERSON> Maiden voice with normal style", "descriptionZh": "日语女性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_Sweet_Maiden", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Sweet Maiden的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-noble-prince", "name": "Noble Prince", "nameEn": "Noble Prince", "nameZh": "日Noble  Prince", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Noble Prince voice", "descriptionEn": "Noble Prince voice with normal style", "descriptionZh": "日语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_Noble_Prince", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Noble Prince的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-japanese-graceful-dancer", "name": "Graceful Dancer", "nameEn": "Graceful Dancer", "nameZh": "日Graceful  Dancer", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Graceful Dancer voice", "descriptionEn": "Graceful Dancer voice with normal style", "descriptionZh": "日语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Japanese_Grace<PERSON>_Dancer", "isActive": true, "languageSupports": [{"languageCode": "ja-<PERSON>", "quality": "basic", "isDefault": true, "sampleText": "这是Graceful Dancer的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-business-executive", "name": "Business Executive", "nameEn": "Business Executive", "nameZh": "港Business  Executive", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Business Executive voice", "descriptionEn": "Business Executive voice with professional style", "descriptionZh": "粤语中性语音，专业风格", "styles": ["professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Cantonese_Business_Executive", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Business Executive的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-friendly-shopkeeper", "name": "Friendly Shopkeeper", "nameEn": "Friendly Shopkeeper", "nameZh": "港Friendly  Shopkeeper", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Friendly Shopkeeper voice", "descriptionEn": "Friendly Shopkeeper voice with normal style", "descriptionZh": "粤语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Cantonese_Friendly_Shopkeeper", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Friendly Shopkeeper的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-wise-elder", "name": "<PERSON> Elder", "nameEn": "<PERSON> Elder", "nameZh": "港男Wise  Elder", "genderEn": "Male", "genderZh": "男性", "avatarUrl": null, "description": "Wise Elder voice", "descriptionEn": "Wise Elder voice with wise style", "descriptionZh": "粤语男性语音，智慧风格", "styles": ["wise"], "stylesEn": ["<PERSON>"], "stylesZh": ["智慧"], "voiceName": "<PERSON>_Wise_Elder", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Wise Elder的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-cheerful-youth", "name": "Cheerful Youth", "nameEn": "Cheerful Youth", "nameZh": "港Cheerful  Youth", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Cheerful Youth voice", "descriptionEn": "Cheerful Youth voice with energetic style", "descriptionZh": "粤语中性语音，活力风格", "styles": ["energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Cantonese_Cheerful_Youth", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Cheerful Youth的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-professional-guide", "name": "Professional Guide", "nameEn": "Professional Guide", "nameZh": "港Professional  Guide", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Professional Guide voice", "descriptionEn": "Professional Guide voice with professional style", "descriptionZh": "粤语中性语音，专业风格", "styles": ["professional"], "stylesEn": ["Professional"], "stylesZh": ["专业"], "voiceName": "Cantonese_Professional_Guide", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "standard", "isDefault": true, "sampleText": "这是Professional Guide的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 7, "isActive": true}]}, {"slug": "minimax-cantonese-warm-teacher", "name": "Warm Teacher", "nameEn": "Warm Teacher", "nameZh": "港Warm  Teacher", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Warm Teacher voice", "descriptionEn": "Warm Teacher voice with normal style", "descriptionZh": "粤语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Cantonese_Warm_Teacher", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Warm Teacher的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-confident-leader", "name": "Confident Leader", "nameEn": "Confident Leader", "nameZh": "港Confident  Leader", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Confident Leader voice", "descriptionEn": "Confident Leader voice with normal style", "descriptionZh": "粤语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Cantonese_Confident_Leader", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Confident Leader的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-gentle-caregiver", "name": "Gentle Caregiver", "nameEn": "Gentle Caregiver", "nameZh": "港Gentle  Caregiver", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Gentle Caregiver voice", "descriptionEn": "Gentle Caregiver voice with gentle style", "descriptionZh": "粤语中性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Cantonese_Gentle_Caregiver", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Gentle Caregiver的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-lively-entertainer", "name": "Lively Entertainer", "nameEn": "Lively Entertainer", "nameZh": "港Lively  Entertainer", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Lively Entertainer voice", "descriptionEn": "Lively Entertainer voice with energetic style", "descriptionZh": "粤语中性语音，活力风格", "styles": ["energetic"], "stylesEn": ["Energetic"], "stylesZh": ["活力"], "voiceName": "Cantonese_Lively_Entertainer", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Lively Entertainer的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-calm-advisor", "name": "Calm Advisor", "nameEn": "Calm Advisor", "nameZh": "港Calm  Advisor", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Calm Advisor voice", "descriptionEn": "Calm Advisor voice with gentle style", "descriptionZh": "粤语中性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Cantonese_Calm_Advisor", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Calm Advisor的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-kind-helper", "name": "Kind Helper", "nameEn": "Kind Helper", "nameZh": "港Kind  Helper", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Kind Helper voice", "descriptionEn": "Kind Helper voice with gentle style", "descriptionZh": "粤语中性语音，温和风格", "styles": ["gentle"], "stylesEn": ["Gentle"], "stylesZh": ["温和"], "voiceName": "Cantonese_Kind_Helper", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Kind Helper的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}, {"slug": "minimax-cantonese-dynamic-presenter", "name": "Dynamic Presenter", "nameEn": "Dynamic Presenter", "nameZh": "港Dynamic  Presenter", "genderEn": "Neutral", "genderZh": "中性", "avatarUrl": null, "description": "Dynamic Presenter voice", "descriptionEn": "Dynamic Presenter voice with normal style", "descriptionZh": "粤语中性语音，标准风格", "styles": ["normal"], "stylesEn": ["Normal"], "stylesZh": ["标准"], "voiceName": "Cantonese_Dynamic_Presenter", "isActive": true, "languageSupports": [{"languageCode": "zh-HK", "quality": "basic", "isDefault": true, "sampleText": "这是Dynamic Presenter的示例文本", "sampleUrl": null}], "modelMappings": [{"modelName": "speech-2.5-hd-preview", "isDefault": true, "priority": 5, "isActive": true}]}]