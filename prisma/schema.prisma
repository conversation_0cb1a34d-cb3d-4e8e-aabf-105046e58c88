generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Post {
  id          Int      @id @default(autoincrement())
  name        String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdById String
  createdBy   User     @relation(fields: [createdById], references: [id])

  @@index([name])
}

model Account {
  id                       String  @id @default(cuid())
  userId                   String
  type                     String
  provider                 String
  providerAccountId        String
  refresh_token            String?
  access_token             String?
  expires_at               Int?
  token_type               String?
  scope                    String?
  id_token                 String?
  session_state            String?
  refresh_token_expires_in Int?
  user                     User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String              @id @default(cuid())
  name          String?
  email         String?             @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole            @default(USER)
  createdAt     DateTime            @default(now())
  updatedAt     DateTime            @updatedAt
  accounts      Account[]
  transactions  CreditTransaction[]
  modelUsages   ModelUsage[]
  orders        Order[]
  posts         Post[]
  sessions      Session[]
  tokenUsages   TokenUsage[]
  credit        UserCredit?
  projects      Project[]
  projectHistory ProjectHistory[]
  voiceRoleFavorites UserVoiceRoleFavorite[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model TtsRole {
  id               String                @id @default(cuid())
  slug             String                @unique
  name             String
  nameEn           String?
  nameZh           String?
  genderZh         String?
  genderEn         String?
  avatarUrl        String?
  description      String?
  descriptionEn    String?
  descriptionZh    String?
  styles           String[]
  stylesEn         String[]
  stylesZh         String[]
  voiceName        String
  isActive         Boolean               @default(true)
  createdAt        DateTime              @default(now())
  updatedAt        DateTime              @updatedAt
  languageSupports RoleLanguageSupport[]
  modelMappings    RoleModelMapping[]
  userFavorites    UserVoiceRoleFavorite[]

  @@index([isActive])
  @@index([voiceName])
  @@index([isActive, createdAt]) // 复合索引优化活跃角色按时间排序
}

model Language {
  code         String                @id
  name         String
  nativeName   String
  region       String
  isActive     Boolean               @default(true)
  createdAt    DateTime              @default(now())
  updatedAt    DateTime              @updatedAt
  roleSupports RoleLanguageSupport[]

  @@index([region])
  @@index([isActive])
}

model RoleLanguageSupport {
  id           String   @id @default(cuid())
  roleId       String
  languageCode String
  quality      String   @default("standard")
  isDefault    Boolean  @default(false)
  sampleText   String?
  sampleUrl    String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  language     Language @relation(fields: [languageCode], references: [code])
  role         TtsRole  @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([roleId, languageCode])
  @@index([languageCode])
  @@index([quality])
  @@index([isDefault])
  @@index([roleId, languageCode]) // 复合索引优化角色语言查询
  @@index([languageCode, quality]) // 复合索引优化语言质量查询
}

/// --- 语音角色与模型关联表 ---
model RoleModelMapping {
  id        String   @id @default(cuid())
  roleId    String
  modelId   String
  isDefault Boolean  @default(false) // 标记该模型是否为角色的默认模型
  priority  Int      @default(0)     // 优先级，用于排序
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  role      TtsRole  @relation(fields: [roleId], references: [id], onDelete: Cascade)
  model     Model    @relation(fields: [modelId], references: [id], onDelete: Cascade)

  @@unique([roleId, modelId])
  @@index([roleId])
  @@index([modelId])
  @@index([isDefault])
  @@index([priority])
  @@index([roleId, priority]) // 复合索引优化角色优先级查询
  @@index([modelId, isActive]) // 复合索引优化模型活跃状态查询
}

/// --- 模型提供商管理 (简化版) ---
model ModelProvider {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  apiKey      String?
  config      Json?    // 提供商特定配置 (如Minimax的groupId)
  models      Model[]

  @@index([slug])
  @@index([isActive])
}

/// --- 模型注册表 (简化版) ---
model Model {
  id                      String          @id @default(cuid())
  providerId              String
  name                    String
  displayName             String?
  modelType               ModelType
  isActive                Boolean         @default(true)
  createdAt               DateTime        @default(now())
  updatedAt               DateTime        @updatedAt
  description             String?
  costAudioPricePerSecond Float           @default(0)
  costCharacterPrice      Float           @default(0)
  costCharacterUnit       Int             @default(1000)
  costCurrency            String          @default("USD")
  costImagePrice          Float           @default(0)
  costInputTokenPrice     Float           @default(0)
  costOutputTokenPrice    Float           @default(0)
  costPricingType         PricingType     @default(TOKEN)
  costRequestPrice        Float           @default(0)
  costVideoPricePerSecond Float           @default(0)
  costInputTokenUnit      Int             @default(1000)
  costOutputTokenUnit     Int             @default(1000)
  customPricings          CustomPricing[]
  provider                ModelProvider   @relation(fields: [providerId], references: [id], onDelete: Cascade)
  usages                  ModelUsage[]
  roleMappings            RoleModelMapping[]

  @@unique([providerId, name])
  @@index([modelType])
  @@index([isActive])
}

/// --- 自定义定价（我们的销售价）---
model CustomPricing {
  id                  String       @id @default(cuid())
  modelId             String
  name                String
  description         String?
  pricingType         PricingType  @default(TOKEN)
  inputTokenPrice     Float        @default(0)
  outputTokenPrice    Float        @default(0)
  requestPrice        Float        @default(0)
  characterPrice      Float        @default(0)
  characterUnit       Int          @default(1000)
  imagePrice          Float        @default(0)
  videoPricePerSecond Float        @default(0)
  audioPricePerSecond Float        @default(0)
  imagePriceConfig    Json?
  videoPriceConfig    Json?
  audioPriceConfig    Json?
  multiplier          Float        @default(1.0)
  currency            String       @default("USD")
  userTier            String?
  validFrom           DateTime?
  validUntil          DateTime?
  isActive            Boolean      @default(true)
  isDefault           Boolean      @default(false)
  createdAt           DateTime     @default(now())
  updatedAt           DateTime     @updatedAt
  model               Model        @relation(fields: [modelId], references: [id], onDelete: Cascade)
  usages              ModelUsage[]

  @@unique([modelId, name])
  @@index([isActive, isDefault])
}

model UserCredit {
  id           String              @id @default(cuid())
  userId       String              @unique
  balance      Int                 @default(0)
  totalEarned  Int                 @default(0)
  totalSpent   Int                 @default(0)
  createdAt    DateTime            @default(now())
  updatedAt    DateTime            @updatedAt
  transactions CreditTransaction[]
  user         User                @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model CreditTransaction {
  id          String          @id @default(cuid())
  userId      String
  creditId    String
  type        TransactionType
  amount      Int
  description String?
  orderId     String?
  createdAt   DateTime        @default(now())
  userCredit  UserCredit      @relation(fields: [creditId], references: [id], onDelete: Cascade)
  order       Order?          @relation(fields: [orderId], references: [id])
  user        User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([type])
}

model Order {
  id            String              @id @default(cuid())
  userId        String
  packageId     String?
  amount        Float
  currency      String              @default("USD")
  status        OrderStatus         @default(PENDING)
  paymentMethod String?
  paymentId     String?
  credits       Int?
  createdAt     DateTime            @default(now())
  updatedAt     DateTime            @updatedAt
  transactions  CreditTransaction[]
  package       CreditPackage?      @relation(fields: [packageId], references: [id])
  user          User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
}

/// --- 模型使用统计 ---
model ModelUsage {
  id              String         @id @default(cuid())
  userId          String
  modelId         String
  customPricingId String?
  inputTokens     Int            @default(0)
  outputTokens    Int            @default(0)
  totalTokens     Int            @default(0)
  requestCount    Int            @default(1)
  costUsd         Float          @default(0)
  creditsDeducted Int            @default(0)
  endpoint        String?
  sessionId       String?
  metadata        Json?
  createdAt       DateTime       @default(now())
  customPricing   CustomPricing? @relation(fields: [customPricingId], references: [id])
  model           Model          @relation(fields: [modelId], references: [id])
  user            User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([modelId])
  @@index([createdAt])
}

model CreditPackage {
  id           String            @id @default(cuid())
  name         String
  type         CreditPackageType
  credits      Int
  price        Float
  currency     String            @default("USD")
  bonusCredits Int               @default(0)
  description  String?
  isActive     Boolean           @default(true)
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  orders       Order[]
}

/// --- 兼容旧的 TokenUsage（逐步迁移到 ModelUsage）---
model TokenUsage {
  id              String    @id @default(cuid())
  userId          String
  modelType       ModelType
  modelName       String
  inputTokens     Int       @default(0)
  outputTokens    Int       @default(0)
  totalTokens     Int       @default(0)
  requestCount    Int       @default(1)
  costUsd         Float     @default(0)
  creditsDeducted Int       @default(0)
  endpoint        String?
  sessionId       String?
  metadata        Json?
  createdAt       DateTime  @default(now())
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([modelType])
  @@index([createdAt])
}

model SystemSettings {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  updatedAt   DateTime @updatedAt
}

model Project {
  id             String          @id @default(cuid())
  name           String
  description    String?
  userId         String
  status         ProjectStatus   @default(DRAFT)
  settings       Json            @default("{}")
  content        Json            @default("{}")
  metadata       Json?
  lastAccessedAt DateTime?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  history        ProjectHistory[]

  @@index([userId])
  @@index([status])
  @@index([createdAt])
  @@index([lastAccessedAt])
}

model ProjectHistory {
  id          String   @id @default(cuid())
  projectId   String
  userId      String
  action      String
  changes     Json?
  audioUrl    String?
  generatedAt DateTime @default(now())
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([projectId])
  @@index([userId])
  @@index([generatedAt])
}

enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}

enum CreditPackageType {
  STARTER
  STANDARD
  PREMIUM
  ENTERPRISE
}

enum OrderStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum TransactionType {
  PURCHASE
  CONSUME
  REFUND
  BONUS
}

enum ModelType {
  TEXT_GENERATION
  SPEECH_GENERATION
  IMAGE_GENERATION
  VIDEO_GENERATION
  MUSIC_GENERATION
}

/// --- 定价类型枚举 ---
enum PricingType {
  TOKEN
  CHARACTER
  IMAGE
  VIDEO
  REQUEST
  AUDIO
}

enum ProjectStatus {
  DRAFT
  ACTIVE
  ARCHIVED
}

model UserVoiceRoleFavorite {
  id        String   @id @default(cuid())
  userId    String
  roleId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  role TtsRole @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@index([userId])
  @@index([roleId])
}
