-- 增强 ModelProvider 表，添加更完整的配置字段
ALTER TABLE "ModelProvider" ADD COLUMN IF NOT EXISTS "configuration" JSONB;
ALTER TABLE "ModelProvider" ADD COLUMN IF NOT EXISTS "baseUrl" TEXT;
ALTER TABLE "ModelProvider" ADD COLUMN IF NOT EXISTS "defaultHeaders" JSONB;
ALTER TABLE "ModelProvider" ADD COLUMN IF NOT EXISTS "supportedFeatures" TEXT[];
ALTER TABLE "ModelProvider" ADD COLUMN IF NOT EXISTS "region" TEXT;
ALTER TABLE "ModelProvider" ADD COLUMN IF NOT EXISTS "status" TEXT DEFAULT 'active';
ALTER TABLE "ModelProvider" ADD COLUMN IF NOT EXISTS "healthCheckUrl" TEXT;
ALTER TABLE "ModelProvider" ADD COLUMN IF NOT EXISTS "documentationUrl" TEXT;

-- 增强 Model 表，添加版本和能力管理
ALTER TABLE "Model" ADD COLUMN IF NOT EXISTS "version" TEXT;
ALTER TABLE "Model" ADD COLUMN IF NOT EXISTS "capabilities" JSONB;
ALTER TABLE "Model" ADD COLUMN IF NOT EXISTS "limitations" JSONB;
ALTER TABLE "Model" ADD COLUMN IF NOT EXISTS "inputFormats" TEXT[];
ALTER TABLE "Model" ADD COLUMN IF NOT EXISTS "outputFormats" TEXT[];
ALTER TABLE "Model" ADD COLUMN IF NOT EXISTS "languages" TEXT[];
ALTER TABLE "Model" ADD COLUMN IF NOT EXISTS "releaseDate" TIMESTAMP;
ALTER TABLE "Model" ADD COLUMN IF NOT EXISTS "deprecationDate" TIMESTAMP;

-- 为新字段创建索引
CREATE INDEX IF NOT EXISTS "ModelProvider_status_idx" ON "ModelProvider"("status");
CREATE INDEX IF NOT EXISTS "ModelProvider_region_idx" ON "ModelProvider"("region");
CREATE INDEX IF NOT EXISTS "Model_version_idx" ON "Model"("version");
CREATE INDEX IF NOT EXISTS "Model_releaseDate_idx" ON "Model"("releaseDate");