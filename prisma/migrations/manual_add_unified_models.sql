-- CreateEnum
CREATE TYPE "ModelCategory" AS ENUM ('CHAT', 'TTS', 'STT', 'IMAGE_GEN', 'IMAGE_EDIT', 'EMBEDDING', 'MODERATION', 'CODE', 'MULTIMODAL');

-- CreateEnum
CREATE TYPE "InterfaceType" AS ENUM ('REST_API', 'WEBSOCKET', 'GRPC', 'GRAPH<PERSON>', 'SDK');

-- CreateTable
CREATE TABLE "ModelProvider" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "baseUrl" TEXT,
    "apiKeyEnvKey" TEXT,
    "authType" TEXT NOT NULL DEFAULT 'bearer',
    "rateLimit" JSONB,
    "description" TEXT,
    "logoUrl" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ModelProvider_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Model" (
    "id" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "officialName" TEXT NOT NULL,
    "displayName" TEXT,
    "category" "ModelCategory" NOT NULL,
    "modelType" "ModelType" NOT NULL,
    "contextWindow" INTEGER,
    "maxOutput" INTEGER,
    "supportedFormats" TEXT[],
    "officialPricing" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isDeprecated" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Model_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CustomPricing" (
    "id" TEXT NOT NULL,
    "modelId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "pricingType" "PricingType" NOT NULL DEFAULT 'TOKEN',
    "inputTokenPrice" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "outputTokenPrice" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "requestPrice" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "characterPrice" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "imagePrice" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "videoPricePerSecond" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "audioPricePerSecond" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "imagePriceConfig" JSONB,
    "videoPriceConfig" JSONB,
    "audioPriceConfig" JSONB,
    "multiplier" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "userTier" TEXT,
    "validFrom" TIMESTAMP(3),
    "validUntil" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CustomPricing_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ModelInterface" (
    "id" TEXT NOT NULL,
    "modelId" TEXT NOT NULL,
    "interfaceType" "InterfaceType" NOT NULL,
    "endpoint" TEXT NOT NULL,
    "method" TEXT NOT NULL DEFAULT 'POST',
    "requestFormat" JSONB,
    "headers" JSONB,
    "responseFormat" JSONB,
    "defaultParams" JSONB,
    "requiredParams" TEXT[],
    "rateLimit" JSONB,
    "retryConfig" JSONB,
    "timeout" INTEGER NOT NULL DEFAULT 30000,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "priority" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ModelInterface_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ModelUsage" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "modelId" TEXT NOT NULL,
    "customPricingId" TEXT,
    "interfaceId" TEXT,
    "inputTokens" INTEGER NOT NULL DEFAULT 0,
    "outputTokens" INTEGER NOT NULL DEFAULT 0,
    "totalTokens" INTEGER NOT NULL DEFAULT 0,
    "requestCount" INTEGER NOT NULL DEFAULT 1,
    "costUsd" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "creditsDeducted" INTEGER NOT NULL DEFAULT 0,
    "endpoint" TEXT,
    "sessionId" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ModelUsage_pkey" PRIMARY KEY ("id")
);

-- Add ModelConfiguration.modelId (optional foreign key)
ALTER TABLE "ModelConfiguration" ADD COLUMN "modelId" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "ModelProvider_slug_key" ON "ModelProvider"("slug");

-- CreateIndex
CREATE INDEX "ModelProvider_slug_idx" ON "ModelProvider"("slug");

-- CreateIndex
CREATE INDEX "ModelProvider_isActive_idx" ON "ModelProvider"("isActive");

-- CreateIndex
CREATE UNIQUE INDEX "Model_providerId_officialName_key" ON "Model"("providerId", "officialName");

-- CreateIndex
CREATE INDEX "Model_category_idx" ON "Model"("category");

-- CreateIndex
CREATE INDEX "Model_isActive_idx" ON "Model"("isActive");

-- CreateIndex
CREATE UNIQUE INDEX "CustomPricing_modelId_name_key" ON "CustomPricing"("modelId", "name");

-- CreateIndex
CREATE INDEX "CustomPricing_isActive_isDefault_idx" ON "CustomPricing"("isActive", "isDefault");

-- CreateIndex
CREATE UNIQUE INDEX "ModelInterface_modelId_interfaceType_priority_key" ON "ModelInterface"("modelId", "interfaceType", "priority");

-- CreateIndex
CREATE INDEX "ModelInterface_isActive_priority_idx" ON "ModelInterface"("isActive", "priority");

-- CreateIndex
CREATE INDEX "ModelUsage_userId_idx" ON "ModelUsage"("userId");

-- CreateIndex
CREATE INDEX "ModelUsage_modelId_idx" ON "ModelUsage"("modelId");

-- CreateIndex
CREATE INDEX "ModelUsage_createdAt_idx" ON "ModelUsage"("createdAt");

-- AddForeignKey
ALTER TABLE "Model" ADD CONSTRAINT "Model_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "ModelProvider"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CustomPricing" ADD CONSTRAINT "CustomPricing_modelId_fkey" FOREIGN KEY ("modelId") REFERENCES "Model"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ModelInterface" ADD CONSTRAINT "ModelInterface_modelId_fkey" FOREIGN KEY ("modelId") REFERENCES "Model"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ModelUsage" ADD CONSTRAINT "ModelUsage_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ModelUsage" ADD CONSTRAINT "ModelUsage_modelId_fkey" FOREIGN KEY ("modelId") REFERENCES "Model"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ModelUsage" ADD CONSTRAINT "ModelUsage_customPricingId_fkey" FOREIGN KEY ("customPricingId") REFERENCES "CustomPricing"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ModelUsage" ADD CONSTRAINT "ModelUsage_interfaceId_fkey" FOREIGN KEY ("interfaceId") REFERENCES "ModelInterface"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ModelConfiguration" ADD CONSTRAINT "ModelConfiguration_modelId_fkey" FOREIGN KEY ("modelId") REFERENCES "Model"("id") ON DELETE SET NULL ON UPDATE CASCADE;
