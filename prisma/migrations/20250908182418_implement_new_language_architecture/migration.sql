/*
  Warnings:

  - You are about to drop the column `languageSamples` on the `TtsRole` table. All the data in the column will be lost.
  - You are about to drop the column `languageTexts` on the `TtsRole` table. All the data in the column will be lost.
  - You are about to drop the column `languages` on the `TtsRole` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "public"."TtsRole" DROP COLUMN "languageSamples",
DROP COLUMN "languageTexts",
DROP COLUMN "languages";

-- CreateTable
CREATE TABLE "public"."Language" (
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "nativeName" TEXT NOT NULL,
    "region" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Language_pkey" PRIMARY KEY ("code")
);

-- CreateTable
CREATE TABLE "public"."RoleLanguageSupport" (
    "id" TEXT NOT NULL,
    "roleId" TEXT NOT NULL,
    "languageCode" TEXT NOT NULL,
    "quality" TEXT NOT NULL DEFAULT 'standard',
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "sampleText" TEXT,
    "sampleUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "RoleLanguageSupport_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Language_region_idx" ON "public"."Language"("region");

-- CreateIndex
CREATE INDEX "Language_isActive_idx" ON "public"."Language"("isActive");

-- CreateIndex
CREATE INDEX "RoleLanguageSupport_languageCode_idx" ON "public"."RoleLanguageSupport"("languageCode");

-- CreateIndex
CREATE INDEX "RoleLanguageSupport_quality_idx" ON "public"."RoleLanguageSupport"("quality");

-- CreateIndex
CREATE INDEX "RoleLanguageSupport_isDefault_idx" ON "public"."RoleLanguageSupport"("isDefault");

-- CreateIndex
CREATE UNIQUE INDEX "RoleLanguageSupport_roleId_languageCode_key" ON "public"."RoleLanguageSupport"("roleId", "languageCode");

-- AddForeignKey
ALTER TABLE "public"."RoleLanguageSupport" ADD CONSTRAINT "RoleLanguageSupport_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "public"."TtsRole"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."RoleLanguageSupport" ADD CONSTRAINT "RoleLanguageSupport_languageCode_fkey" FOREIGN KEY ("languageCode") REFERENCES "public"."Language"("code") ON DELETE RESTRICT ON UPDATE CASCADE;
