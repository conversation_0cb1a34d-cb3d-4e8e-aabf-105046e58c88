-- CreateTable
CREATE TABLE "public"."SampleText" (
    "id" TEXT NOT NULL,
    "languageCode" TEXT NOT NULL,
    "languageName" TEXT NOT NULL,
    "region" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SampleText_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "SampleText_languageCode_idx" ON "public"."SampleText"("languageCode");

-- CreateIndex
CREATE INDEX "SampleText_region_idx" ON "public"."SampleText"("region");

-- CreateIndex
CREATE INDEX "SampleText_isActive_idx" ON "public"."SampleText"("isActive");
