-- 简化模型管理系统，删除过度复杂的架构
-- 只保留核心必要的字段和功能

-- 1. 简化 ModelProvider 表
ALTER TABLE "ModelProvider" DROP COLUMN IF EXISTS "baseUrl";
ALTER TABLE "ModelProvider" DROP COLUMN IF EXISTS "authType";
ALTER TABLE "ModelProvider" DROP COLUMN IF EXISTS "rateLimit";
ALTER TABLE "ModelProvider" DROP COLUMN IF EXISTS "logoUrl";
ALTER TABLE "ModelProvider" DROP COLUMN IF EXISTS "configuration";
ALTER TABLE "ModelProvider" DROP COLUMN IF EXISTS "defaultHeaders";
ALTER TABLE "ModelProvider" DROP COLUMN IF EXISTS "supportedFeatures";
ALTER TABLE "ModelProvider" DROP COLUMN IF EXISTS "region";
ALTER TABLE "ModelProvider" DROP COLUMN IF EXISTS "status";
ALTER TABLE "ModelProvider" DROP COLUMN IF EXISTS "healthCheckUrl";
ALTER TABLE "ModelProvider" DROP COLUMN IF EXISTS "documentationUrl";

-- 删除 ModelProvider 对应的索引
DROP INDEX IF EXISTS "ModelProvider_status_idx";
DROP INDEX IF EXISTS "ModelProvider_region_idx";

-- 添加简化后需要的字段
ALTER TABLE "ModelProvider" ADD COLUMN IF NOT EXISTS "apiKey" TEXT;

-- 2. 简化 Model 表
ALTER TABLE "Model" DROP COLUMN IF EXISTS "officialName";
ALTER TABLE "Model" DROP COLUMN IF EXISTS "category";
ALTER TABLE "Model" DROP COLUMN IF EXISTS "contextWindow";
ALTER TABLE "Model" DROP COLUMN IF EXISTS "maxOutput";
ALTER TABLE "Model" DROP COLUMN IF EXISTS "supportedFormats";
ALTER TABLE "Model" DROP COLUMN IF EXISTS "officialPricing";
ALTER TABLE "Model" DROP COLUMN IF EXISTS "isDeprecated";
ALTER TABLE "Model" DROP COLUMN IF EXISTS "version";
ALTER TABLE "Model" DROP COLUMN IF EXISTS "capabilities";
ALTER TABLE "Model" DROP COLUMN IF EXISTS "limitations";
ALTER TABLE "Model" DROP COLUMN IF EXISTS "inputFormats";
ALTER TABLE "Model" DROP COLUMN IF EXISTS "outputFormats";
ALTER TABLE "Model" DROP COLUMN IF EXISTS "languages";
ALTER TABLE "Model" DROP COLUMN IF EXISTS "releaseDate";
ALTER TABLE "Model" DROP COLUMN IF EXISTS "deprecationDate";

-- 删除 Model 对应的索引
DROP INDEX IF EXISTS "Model_category_idx";
DROP INDEX IF EXISTS "Model_version_idx";
DROP INDEX IF EXISTS "Model_releaseDate_idx";
DROP INDEX IF EXISTS "Model_deprecationDate_idx";

-- 添加简化后需要的字段
ALTER TABLE "Model" ADD COLUMN IF NOT EXISTS "description" TEXT;

-- 更新唯一约束
DROP INDEX IF EXISTS "Model_providerId_officialName_key";
CREATE UNIQUE INDEX IF NOT EXISTS "Model_providerId_name_key" ON "Model"("providerId", "name");

-- 添加新的索引
CREATE INDEX IF NOT EXISTS "Model_modelType_idx" ON "Model"("modelType");

-- 3. 删除过度复杂的表
DROP TABLE IF EXISTS "ModelInterface";
DROP TABLE IF EXISTS "ModelConfiguration";
DROP TABLE IF EXISTS "ModelPricing";

-- 4. 清理 ModelUsage 表中的无用字段
ALTER TABLE "ModelUsage" DROP COLUMN IF EXISTS "interfaceId";