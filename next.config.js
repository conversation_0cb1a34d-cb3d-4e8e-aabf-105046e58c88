/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
import "./src/env.js";

/** @type {import("next").NextConfig} */
const config = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // 国际化配置
  i18n: {
    locales: [
      'zh-CN',  // 中文（默认）
      'en-US',  // 英语
      'ja-JP',  // 日语
      'ko-KR',  // 韩语
      'es-ES',  // 西班牙语
      'fr-FR',  // 法语
      'de-DE',  // 德语
      'ar-SA',  // 阿拉伯语
      'hi-IN',  // 印地语
    ],
    defaultLocale: 'zh-CN',
    localeDetection: true,
  },
};

export default config;
