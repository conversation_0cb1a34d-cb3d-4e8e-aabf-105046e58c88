"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { UserRole } from "@prisma/client";

interface User {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  role: UserRole;
  emailVerified?: Date | null;
  createdAt: Date;
  credit?: {
    balance: number;
    totalEarned: number;
    totalSpent: number;
  } | null;
}

export default function AdminUsersPage() {
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");

  // tRPC 查询和变更
  const utils = api.useUtils();
  const { data: usersData, isLoading } = api.user.getUsers.useQuery({
    search: searchTerm || undefined,
    role: roleFilter === "all" ? undefined : (roleFilter as UserRole),
  });

  const updateRoleMutation = api.user.updateUser.useMutation({
    onSuccess: () => {
      toast.success('用户角色更新成功');
      utils.user.getUsers.invalidate();
    },
    onError: () => {
      toast.error('更新用户角色失败');
    },
  });

  const adjustCreditMutation = api.user.adjustCredit.useMutation({
    onSuccess: () => {
      toast.success('积分调整成功');
      utils.user.getUsers.invalidate();
    },
    onError: () => {
      toast.error('积分调整失败');
    },
  });

  const users = usersData?.users || [];

  const handleRoleUpdate = (userId: string, newRole: UserRole) => {
    if (confirm(`确定要将用户角色更改为 ${getRoleLabel(newRole)} 吗？`)) {
      updateRoleMutation.mutate({ id: userId, role: newRole });
    }
  };

  const handleCreditAdjustment = (userId: string) => {
    const amount = prompt("请输入要调整的积分数量（正数为增加，负数为减少）:");
    if (amount && !isNaN(Number(amount))) {
      adjustCreditMutation.mutate({ userId, amount: Number(amount) });
    }
  };

  const getRoleLabel = (role: UserRole) => {
    const labels = {
      USER: "普通用户",
      ADMIN: "管理员",
      SUPER_ADMIN: "超级管理员",
    };
    return labels[role] || "未知角色";
  };

  const getRoleColor = (role: UserRole) => {
    const colors = {
      USER: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
      ADMIN: "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400",
      SUPER_ADMIN: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
    };
    return colors[role] || "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
  };

  const filteredUsers = users.filter((user) => {
    const matchesRole = roleFilter === "all" || user.role === roleFilter;
    const matchesSearch = 
      searchTerm === "" ||
      user.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.name?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesRole && matchesSearch;
  });

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">用户管理</h1>
          <p className="text-muted-foreground mt-2">管理系统中的所有用户</p>
        </div>
        <Link href="/admin">
          <Button variant="outline">返回后台</Button>
        </Link>
      </div>

      {/* 筛选和搜索 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>筛选用户</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="搜索用户ID、邮箱或姓名..."
                value={searchTerm}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="w-48">
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择角色" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有角色</SelectItem>
                  <SelectItem value="USER">普通用户</SelectItem>
                  <SelectItem value="ADMIN">管理员</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 用户统计 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{users.length}</div>
            <p className="text-sm text-muted-foreground">总用户数</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {users.filter(u => u.role === "ADMIN").length}
            </div>
            <p className="text-sm text-muted-foreground">管理员</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {users.filter(u => u.emailVerified).length}
            </div>
            <p className="text-sm text-muted-foreground">已验证邮箱</p>
          </CardContent>
        </Card>
      </div>

      {/* 用户列表 */}
      {filteredUsers.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">
              {users.length === 0 ? "暂无用户" : "没有找到匹配的用户"}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredUsers.map((user) => (
            <Card key={user.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3 flex-1">
                    {user.image && (
                      <img
                        src={user.image}
                        alt={user.name || "用户头像"}
                        className="w-10 h-10 rounded-full"
                      />
                    )}
                    <div>
                      <CardTitle className="text-lg">
                        {user.name || "未设置姓名"}
                      </CardTitle>
                      <CardDescription className="mt-1">
                        {user.email || "未设置邮箱"}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Badge className={getRoleColor(user.role)}>
                      {getRoleLabel(user.role)}
                    </Badge>
                    {user.emailVerified && (
                      <Badge variant="outline">已验证</Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-muted-foreground">用户ID:</span>
                    <p className="mt-1 font-mono text-xs">{user.id}</p>
                  </div>
                  <div>
                    <span className="font-medium text-muted-foreground">积分余额:</span>
                    <p className="mt-1 font-bold">
                      {user.credit?.balance?.toLocaleString() || 0}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      总获得: {user.credit?.totalEarned?.toLocaleString() || 0} |
                      总消费: {user.credit?.totalSpent?.toLocaleString() || 0}
                    </p>
                  </div>
                  <div>
                    <span className="font-medium text-muted-foreground">订单数:</span>
                    <p className="mt-1">-</p>
                    <p className="text-xs text-muted-foreground">
                      交易记录: -
                    </p>
                  </div>
                  <div>
                    <span className="font-medium text-muted-foreground">注册时间:</span>
                    <p className="mt-1">{formatDate(user.createdAt)}</p>
                    {user.emailVerified && (
                      <p className="text-xs text-muted-foreground">
                        验证: {formatDate(user.emailVerified)}
                      </p>
                    )}
                  </div>
                </div>
                
                {/* 操作按钮 */}
                <div className="flex gap-2 mt-4 pt-4 border-t">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleCreditAdjustment(user.id)}
                  >
                    调整积分
                  </Button>
                  {user.role === "USER" ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRoleUpdate(user.id, "ADMIN")}
                    >
                      设为管理员
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRoleUpdate(user.id, "USER")}
                    >
                      设为普通用户
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    查看详情
                  </Button>
                  <Button variant="outline" size="sm">
                    发送邮件
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}