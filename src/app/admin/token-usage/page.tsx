"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { BarChart3, ArrowRight } from "lucide-react";

export default function TokenUsagePage() {
  const router = useRouter();

  useEffect(() => {
    // 3秒后自动跳转
    const timer = setTimeout(() => {
      router.replace("/admin/model-management?tab=usage");
    }, 3000);

    return () => clearTimeout(timer);
  }, [router]);

  const handleRedirect = () => {
    router.replace("/admin/model-management?tab=usage");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
            <BarChart3 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <CardTitle className="text-xl">功能已整合</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground">
            Token使用统计功能已整合到 <strong>模型管理</strong> 页面中
          </p>
          <p className="text-sm text-muted-foreground">
            页面将在 3 秒后自动跳转...
          </p>
          <Button onClick={handleRedirect} className="w-full">
            立即跳转
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}