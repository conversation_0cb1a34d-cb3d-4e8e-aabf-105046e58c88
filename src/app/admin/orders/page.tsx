"use client";

import { useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { api } from "~/trpc/react";
import { toast } from "sonner";

type OrderStatus = "PENDING" | "PAID" | "FAILED" | "REFUNDED";

// 使用从API返回的实际类型，不需要手动定义接口

export default function AdminOrdersPage() {
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");

  // tRPC 查询和变更
  const utils = api.useUtils();
  const { data: ordersData, isLoading } = api.order.getOrders.useQuery({
    search: searchTerm || undefined,
    status: statusFilter === "all" ? undefined : statusFilter as "PENDING" | "PAID" | "FAILED" | "REFUNDED",
  });

  const updateStatusMutation = api.order.updateOrderStatus.useMutation({
    onSuccess: () => {
      toast.success('订单状态更新成功');
      utils.order.getOrders.invalidate();
    },
    onError: () => {
      toast.error('更新订单状态失败');
    },
  });

  const orders = ordersData?.orders || [];

  const handleStatusUpdate = async (orderId: string, newStatus: "PENDING" | "PAID" | "FAILED" | "REFUNDED") => {
    if (confirm(`确定要将订单状态更改为 ${getStatusLabel(newStatus)} 吗？`)) {
      updateStatusMutation.mutate({ id: orderId, status: newStatus });
    }
  };

  const getStatusLabel = (status: OrderStatus) => {
    const labels = {
      PENDING: "待处理",
    PAID: "已支付",
    FAILED: "失败",
    REFUNDED: "已退款",
    };
    return labels[status];
  };

  const getStatusColor = (status: OrderStatus) => {
    const colors = {
      PENDING: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
      PAID: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
      FAILED: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
      REFUNDED: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",
    };
    return colors[status];
  };

  const filteredOrders = orders.filter((order: typeof orders[0]) => {
    const matchesStatus = statusFilter === "all" || order.status === statusFilter;
    const matchesSearch = 
      searchTerm === "" ||
      order.id.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">订单管理</h1>
          <p className="text-muted-foreground mt-2">管理系统中的所有订单</p>
        </div>
        <Link href="/admin">
          <Button variant="outline">返回后台</Button>
        </Link>
      </div>

      {/* 筛选和搜索 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>筛选订单</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="搜索订单ID、用户邮箱或姓名..."
                value={searchTerm}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="w-48">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有状态</SelectItem>
                  <SelectItem value="PENDING">待处理</SelectItem>
                  <SelectItem value="PAID">已支付</SelectItem>
                  <SelectItem value="FAILED">失败</SelectItem>
                  <SelectItem value="REFUNDED">已退款</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 订单列表 */}
      {filteredOrders.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">
              {orders.length === 0 ? "暂无订单" : "没有找到匹配的订单"}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredOrders.map((order: typeof filteredOrders[0]) => (
            <Card key={order.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">订单 #{order.id.slice(-8)}</CardTitle>
                    <CardDescription className="mt-1">
                      用户ID: {order.userId}
                    </CardDescription>
                  </div>
                  <Badge className={getStatusColor(order.status)}>
                    {getStatusLabel(order.status)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-muted-foreground">积分包:</span>
                    <p className="mt-1">积分包ID: {order.packageId || "无"}</p>
                    <p className="text-sm text-muted-foreground">
                      积分数量: {order.credits || 0}
                    </p>
                  </div>
                  <div>
                    <span className="font-medium text-muted-foreground">金额:</span>
                    <p className="mt-1 font-bold">
                      ${order.amount} {order.currency}
                    </p>
                  </div>
                  <div>
                    <span className="font-medium text-muted-foreground">支付方式:</span>
                    <p className="mt-1">{order.paymentMethod || "未知"}</p>
                    {order.paymentId && (
                      <p className="text-xs text-muted-foreground">
                        ID: {order.paymentId.slice(-8)}
                      </p>
                    )}
                  </div>
                  <div>
                    <span className="font-medium text-muted-foreground">创建时间:</span>
                    <p className="mt-1">{formatDate(order.createdAt)}</p>
                    {order.updatedAt !== order.createdAt && (
                      <p className="text-xs text-muted-foreground">
                        更新: {formatDate(order.updatedAt)}
                      </p>
                    )}
                  </div>
                </div>
                
                {/* 操作按钮 */}
                <div className="flex gap-2 mt-4 pt-4 border-t">
                  {order.status === "PENDING" && (
                    <>
                      <Button
                        size="sm"
                        onClick={() => handleStatusUpdate(order.id, "PAID")}
                      >
                        标记为已支付
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleStatusUpdate(order.id, "FAILED")}
                      >
                        标记失败
                      </Button>
                    </>
                  )}
                  {order.status === "PAID" && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleStatusUpdate(order.id, "REFUNDED")}
                    >
                      退款
                    </Button>
                  )}
                  {(order.status === "FAILED" || order.status === "REFUNDED") && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleStatusUpdate(order.id, "PENDING")}
                    >
                      重新处理
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    查看详情
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}