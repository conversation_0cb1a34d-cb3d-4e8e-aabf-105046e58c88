"use client";

import { useState } from "react";
import { Button } from "~/components/ui/button";
import { PlusIcon } from "@heroicons/react/24/outline";
import EnhancedRoleList from "~/components/admin/roles/EnhancedRoleList";
import RoleEditDialog from "~/components/admin/roles/RoleEditDialog";

interface TtsRole {
  id: string;
  slug: string;
  name: string;
  nameEn: string | null;
  nameZh: string | null;
  description: string | null;
  descriptionEn: string | null;
  descriptionZh: string | null;
  styles: string[];
  stylesEn: string[] | null;
  stylesZh: string[] | null;
  voiceName: string;
  isActive: boolean;
  avatarUrl: string | null;
  genderZh: string | null;
  genderEn: string | null;
  createdAt: Date;
  updatedAt: Date;
  languageSupports?: {
    id: string;
    languageCode: string;
    quality: string;
    isDefault: boolean;
    sampleText: string | null;
    sampleUrl: string | null;
    language: {
      code: string;
      name: string;
      nativeName: string;
      region: string;
    };
  }[];
}

export default function AdminRolesPage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<TtsRole | null>(null);

  const handleEditRole = (role: TtsRole) => {
    setEditingRole(role);
    setIsEditDialogOpen(true);
  };

  const handleCloseCreateDialog = () => {
    setIsCreateDialogOpen(false);
  };

  const handleCloseEditDialog = () => {
    setIsEditDialogOpen(false);
    setEditingRole(null);
  };

  const handleCreateClick = () => {
    setEditingRole(null);
    setIsCreateDialogOpen(true);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">语音角色管理</h1>
            <p className="text-muted-foreground mt-1">
              管理文本转语音角色及其语言支持
            </p>
          </div>
          <Button onClick={handleCreateClick}>
            <PlusIcon className="h-4 w-4 mr-2" />
            新增角色
          </Button>
        </div>
      </div>

      {/* 角色列表组件 */}
      <EnhancedRoleList onEditRole={handleEditRole} />

      {/* 创建角色对话框 */}
      <RoleEditDialog
        isOpen={isCreateDialogOpen}
        onClose={handleCloseCreateDialog}
        mode="create"
      />

      {/* 编辑角色对话框 */}
      <RoleEditDialog
        isOpen={isEditDialogOpen}
        onClose={handleCloseEditDialog}
        editingRole={editingRole}
        mode="edit"
      />
    </div>
  );
}
