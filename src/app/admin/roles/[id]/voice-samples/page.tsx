"use client";

import { useState, use, useEffect } from "react";
import Link from "next/link";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Label } from "~/components/ui/label";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { Volume2, VolumeX, Loader2, Play, Edit, Save, X } from "lucide-react";
import { toast } from "sonner";
import { api } from "~/trpc/react";

export default function RoleLanguageSupportsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id: roleId } = use(params);
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);
  const [generatingAudio, setGeneratingAudio] = useState<string | null>(null);
  const [editingText, setEditingText] = useState<string | null>(null);
  const [editTextContent, setEditTextContent] = useState<string>("");
  const [selectedModel, setSelectedModel] = useState<"gemini-2.5-flash-preview-tts" | "gemini-2.5-pro-preview-tts">("gemini-2.5-flash-preview-tts");

  // tRPC 查询
  const { data: role, isLoading: roleLoading } = api.tts.getById.useQuery({ id: roleId });
  const { data: voiceSamples } = api.voiceSample.getByRoleId.useQuery({ roleId });
  const { data: supportedModels } = api.voiceSample.getSupportedModels.useQuery();
  
  // tRPC mutations
  const utils = api.useUtils();
  const generateSampleMutation = api.voiceSample.generateSample.useMutation({
    onSuccess: () => {
      toast.success('语音样本生成成功');
      setGeneratingAudio(null);
      utils.voiceSample.getByRoleId.invalidate({ roleId });
    },
    onError: (error) => {
      toast.error(error.message || '生成失败');
      setGeneratingAudio(null);
    },
  });

  const updateTextMutation = api.voiceSample.updateSampleText.useMutation({
    onSuccess: () => {
      toast.success('文本已保存');
      setEditingText(null);
      utils.voiceSample.getByRoleId.invalidate({ roleId });
    },
    onError: (error) => {
      toast.error(error.message || '保存失败');
    },
  });

  // 组件清理：停止音频播放
  useEffect(() => {
    return () => {
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0;
      }
    };
  }, [currentAudio]);

  // 停止当前播放的音频
  const stopCurrentAudio = () => {
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
      setCurrentAudio(null);
    }
    setPlayingAudio(null);
  };

  // 播放音频
  const handlePlayAudio = async (sampleUrl: string, sampleId: string) => {
    try {
      // 如果点击的是当前正在播放的音频，则停止播放
      if (playingAudio === sampleId) {
        stopCurrentAudio();
        return;
      }

      // 先停止之前播放的音频
      stopCurrentAudio();

      // 创建新的音频实例
      const audio = new Audio(sampleUrl);
      setCurrentAudio(audio);
      setPlayingAudio(sampleId);
      
      audio.onended = () => {
        setPlayingAudio(null);
        setCurrentAudio(null);
      };
      
      audio.onerror = () => {
        setPlayingAudio(null);
        setCurrentAudio(null);
        toast.error('音频播放失败');
      };

      // 当音频被暂停时也要清理状态
      audio.onpause = () => {
        if (audio.currentTime === 0) {
          setPlayingAudio(null);
          setCurrentAudio(null);
        }
      };
      
      await audio.play();
    } catch (error) {
      setPlayingAudio(null);
      setCurrentAudio(null);
      toast.error('音频播放失败');
    }
  };

  // 停止播放
  const handleStopAudio = () => {
    stopCurrentAudio();
  };

  // 生成语音样本
  const handleGenerateAudio = (sampleId: string, languageCode: string, customText?: string) => {
    // 如果没有自定义文本，就使用数据库中已有的文本
    const sample = voiceSamples?.samples.find(s => s.id === sampleId);
    const textToUse = customText || sample?.sampleText || "请先编辑文本内容";
    
    if (!textToUse || textToUse === "请先编辑文本内容") {
      toast.error('请先编辑文本内容再生成音频');
      return;
    }
    
    setGeneratingAudio(sampleId);
    generateSampleMutation.mutate({
      roleId: roleId,
      language: languageCode,
      text: textToUse,
      model: selectedModel
    });
  };

  // 开始编辑文本
  const handleStartEditText = (sampleId: string, currentText: string) => {
    setEditingText(sampleId);
    setEditTextContent(currentText || "");
  };

  // 仅保存文本
  const handleSaveTextOnly = (sampleId: string) => {
    if (!editTextContent.trim()) {
      toast.error('文本内容不能为空');
      return;
    }
    updateTextMutation.mutate({
      id: sampleId,
      sampleText: editTextContent,
    });
  };

  // 保存编辑的文本并生成音频
  const handleSaveAndGenerate = (sampleId: string, languageCode: string) => {
    if (!editTextContent.trim()) {
      toast.error('文本内容不能为空');
      return;
    }
    setEditingText(null);
    handleGenerateAudio(sampleId, languageCode, editTextContent);
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditingText(null);
    setEditTextContent("");
  };

  if (roleLoading) {
    return <div className="flex justify-center p-8">加载中...</div>;
  }

  if (!role) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">角色未找到</h1>
          <Button asChild>
            <Link href="/admin/roles">返回角色列表</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      {/* 头部导航 */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <Button variant="ghost" asChild>
            <Link href="/admin/roles">
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              返回角色列表
            </Link>
          </Button>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">{role.name} - 语言支持</h1>
            <p className="text-muted-foreground mt-1">
              管理角色的语言支持和语音样本
            </p>
          </div>
        </div>
      </div>

      {/* 角色信息和生成设置 - 并排显示 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* 角色信息概览 */}
        <Card>
          <CardHeader>
            <CardTitle>角色信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4">
              <div>
                <Label className="text-sm font-medium">语音名称</Label>
                <div className="mt-1">{role.voiceName}</div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">状态</Label>
                  <div className="mt-1">
                    <Badge variant={role.isActive ? "default" : "secondary"}>
                      {role.isActive ? "启用" : "禁用"}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">标识符</Label>
                  <div className="mt-1 font-mono text-sm">{role.slug}</div>
                </div>
              </div>
            </div>
            
            {role.description && (
              <div className="mt-4">
                <Label className="text-sm font-medium">描述</Label>
                <div className="mt-1 text-sm text-muted-foreground">{role.description}</div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* TTS模型选择 */}
        <Card>
          <CardHeader>
            <CardTitle>生成设置</CardTitle>
            <CardDescription>
              选择用于生成语音样本的TTS模型
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="model-select" className="text-sm font-medium">TTS模型</Label>
                <Select 
                  value={selectedModel} 
                  onValueChange={(value) => setSelectedModel(value as "gemini-2.5-flash-preview-tts" | "gemini-2.5-pro-preview-tts")}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="选择TTS模型" />
                  </SelectTrigger>
                  <SelectContent>
                    {supportedModels?.models.map((model) => (
                      <SelectItem key={model.id} value={model.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{model.name}</span>
                          <span className="text-xs text-muted-foreground">{model.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="p-3 bg-muted rounded-lg">
                <div className="text-sm text-muted-foreground">
                  当前选择: <span className="font-medium text-foreground">{supportedModels?.models.find(m => m.id === selectedModel)?.name}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 语音样本管理 */}
      <Card>
        <CardHeader>
          <CardTitle>语音样本管理</CardTitle>
          <CardDescription>
            为这个角色管理不同语言的语音试听样本
          </CardDescription>
        </CardHeader>
        <CardContent>
          {voiceSamples?.samples && voiceSamples.samples.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {voiceSamples.samples.map((sample) => (
                <Card key={sample.id} className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="font-medium text-lg">{sample.languageCode}</div>
                    <div className="flex gap-2">
                      <Badge variant={sample.quality === 'premium' ? 'default' : 'secondary'}>
                        {sample.quality === 'premium' ? '高级' : '标准'}
                      </Badge>
                      {sample.isDefault && (
                        <Badge variant="outline">默认</Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-sm text-muted-foreground mb-3">
                    {sample.language?.nativeName || sample.languageCode}
                  </div>
                  
                  {/* 可编辑试听文本区域 */}
                  <div className="mb-3">
                    {editingText === sample.id ? (
                      <div className="space-y-2">
                        <Label className="text-xs font-medium">试听文本</Label>
                        <Textarea
                          value={editTextContent}
                          onChange={(e) => setEditTextContent(e.target.value)}
                          rows={3}
                          className="text-sm"
                          placeholder="输入要生成语音的文本内容..."
                        />
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleSaveTextOnly(sample.id)}
                            disabled={!editTextContent.trim() || updateTextMutation.isPending}
                            className="text-xs px-2 py-1 h-6"
                          >
                            {updateTextMutation.isPending ? (
                              <>
                                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                保存中
                              </>
                            ) : (
                              <>
                                <Save className="h-3 w-3 mr-1" />
                                仅保存
                              </>
                            )}
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleSaveAndGenerate(sample.id, sample.languageCode)}
                            disabled={!editTextContent.trim() || generatingAudio === sample.id}
                            className="text-xs px-2 py-1 h-6"
                          >
                            <Save className="h-3 w-3 mr-1" />
                            保存并生成
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={handleCancelEdit}
                            className="text-xs px-2 py-1 h-6"
                          >
                            <X className="h-3 w-3 mr-1" />
                            取消
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-sm bg-muted p-2 rounded relative group">
                        <div className="pr-8">
                          {sample.sampleText ? (
                            sample.sampleText.substring(0, 100) + (sample.sampleText.length > 100 ? '...' : '')
                          ) : (
                            <span className="text-muted-foreground italic">点击编辑添加试听文本</span>
                          )}
                        </div>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleStartEditText(sample.id, sample.sampleText || "")}
                          className="absolute top-1 right-1 text-xs px-1 py-0 h-6 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {sample.sampleUrl ? (
                        <Badge variant="default" className="text-xs">
                          <Volume2 className="h-3 w-3 mr-1" />
                          有音频
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="text-xs">
                          <VolumeX className="h-3 w-3 mr-1" />
                          待生成
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-1">
                      {editingText !== sample.id && (
                        <>
                          {/* 快速生成按钮 */}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleGenerateAudio(sample.id, sample.languageCode)}
                            disabled={generatingAudio === sample.id}
                            className="text-xs px-2 py-1 h-7"
                          >
                            {generatingAudio === sample.id ? (
                              <>
                                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                生成中
                              </>
                            ) : (
                              <>
                                <Volume2 className="h-3 w-3 mr-1" />
                                生成
                              </>
                            )}
                          </Button>
                          
                          {/* 播放音频按钮 */}
                          {sample.sampleUrl && (
                            <Button
                              size="sm"
                              variant={playingAudio === sample.id ? "destructive" : "default"}
                              onClick={() => handlePlayAudio(sample.sampleUrl!, sample.id)}
                              className="text-xs px-2 py-1 h-7"
                            >
                              {playingAudio === sample.id ? (
                                <>
                                  <VolumeX className="h-3 w-3 mr-1" />
                                  停止
                                </>
                              ) : (
                                <>
                                  <Play className="h-3 w-3 mr-1" />
                                  播放
                                </>
                              )}
                            </Button>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-muted-foreground mb-2">暂无语音样本</div>
              <div className="text-sm text-muted-foreground">
                这个角色还没有生成语音试听样本
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
