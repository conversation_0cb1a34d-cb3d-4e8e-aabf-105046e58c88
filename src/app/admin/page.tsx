"use client";

import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import {
  ChartBarIcon,
  UserGroupIcon,
  CreditCardIcon,
  ShoppingBagIcon,
} from "@heroicons/react/24/outline";
import { api } from "~/trpc/react";

interface ChartData {
  name: string;
  value: number;
  color: string;
}

const StatCard = ({ title, value, icon: Icon, color }: {
  title: string;
  value: string | number;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}) => (
  <div className="bg-card rounded-lg shadow p-6 border">
    <div className="flex items-center">
      <div className={`flex-shrink-0 p-3 rounded-md ${color}`}>
        <Icon className="h-6 w-6 text-white" />
      </div>
      <div className="ml-4">
        <p className="text-sm font-medium text-muted-foreground">{title}</p>
        <p className="text-2xl font-semibold text-foreground">{value}</p>
      </div>
    </div>
  </div>
);

const PieChart = ({ data, title }: { data: ChartData[]; title: string }) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  
  return (
    <div className="bg-card rounded-lg shadow p-6 border">
      <h3 className="text-lg font-medium text-foreground mb-4">{title}</h3>
      <div className="space-y-3">
        {data.map((item, index) => {
          const percentage = total > 0 ? (item.value / total * 100).toFixed(1) : '0';
          return (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center">
                <div 
                  className="w-3 h-3 rounded-full mr-2" 
                  style={{ backgroundColor: item.color }}
                />
                <span className="text-sm text-muted-foreground">{item.name}</span>
              </div>
              <div className="text-right">
                <span className="text-sm font-medium text-foreground">{item.value}</span>
                <span className="text-xs text-muted-foreground ml-1">({percentage}%)</span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const TrendChart = ({ data }: { data: Array<{ date: string; orders: number; revenue: number; newUsers: number }> }) => (
  <div className="bg-card rounded-lg shadow p-6 border">
    <h3 className="text-lg font-medium text-foreground mb-4">最近7天趋势</h3>
    <div className="space-y-4">
      {data.map((item, index) => (
        <div key={index} className="grid grid-cols-4 gap-4 text-sm">
          <div className="text-muted-foreground">{new Date(item.date).toLocaleDateString()}</div>
          <div className="text-blue-600 dark:text-blue-400">新用户: {item.newUsers}</div>
          <div className="text-green-600 dark:text-green-400">订单: {item.orders}</div>
          <div className="text-purple-600 dark:text-purple-400">收入: ¥{item.revenue}</div>
        </div>
      ))}
    </div>
  </div>
);

export default function AdminDashboard() {
  // 使用 tRPC 查询数据
  const { data: dashboardStats, isLoading: statsLoading } = api.stats.getDashboardStats.useQuery();
  const { data: userRoleData = [], isLoading: userRoleLoading } = api.stats.getUserRoleDistribution.useQuery();
  const { data: orderStatusData = [], isLoading: orderStatusLoading } = api.stats.getOrderStatusDistribution.useQuery();
  const { data: trendData = [], isLoading: trendLoading } = api.stats.getTrendData.useQuery();

  const loading = statsLoading || userRoleLoading || orderStatusLoading || trendLoading;

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-muted-foreground">加载中...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">管理后台仪表板</h1>
        <p className="text-muted-foreground mt-2">系统管理和数据统计</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="总用户数"
          value={dashboardStats?.users.total || 0}
          icon={UserGroupIcon}
          color="bg-blue-500"
        />
        <StatCard
          title="总订单数"
          value={dashboardStats?.orders.total || 0}
          icon={ShoppingBagIcon}
          color="bg-green-500"
        />
        <StatCard
          title="总收入"
          value={`¥${dashboardStats?.orders.revenue || 0}`}
          icon={CreditCardIcon}
          color="bg-purple-500"
        />
        <StatCard
          title="积分包数量"
          value={dashboardStats?.creditPackages.total || 0}
          icon={ChartBarIcon}
          color="bg-orange-500"
        />
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <PieChart data={userRoleData} title="用户角色分布" />
        <PieChart data={orderStatusData} title="订单状态分布" />
      </div>

      {/* 趋势图 */}
      <TrendChart data={trendData} />

      {/* 管理功能卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
        {/* 语音角色管理 */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🎭 语音角色管理
            </CardTitle>
            <CardDescription>
              管理系统中的语音角色，包括创建、编辑和删除
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Link 
              href="/admin/roles" 
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full"
            >
              进入管理
            </Link>
          </CardContent>
        </Card>

        {/* 积分包管理 */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              💎 积分包管理
            </CardTitle>
            <CardDescription>
              管理积分包套餐，设置价格和积分数量
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Link 
              href="/admin/credit-management" 
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full"
            >
              进入管理
            </Link>
          </CardContent>
        </Card>

        {/* 订单管理 */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📋 订单管理
            </CardTitle>
            <CardDescription>
              查看和管理用户的积分购买订单
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Link 
              href="/admin/orders" 
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full"
            >
              进入管理
            </Link>
          </CardContent>
        </Card>

        {/* 用户管理 */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              👥 用户管理
            </CardTitle>
            <CardDescription>
              管理系统用户，查看用户信息和积分余额
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Link 
              href="/admin/users" 
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full"
            >
              进入管理
            </Link>
          </CardContent>
        </Card>

        {/* 积分交易记录 */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              💰 积分交易记录
            </CardTitle>
            <CardDescription>
              查看所有积分交易记录和统计信息
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Link 
              href="/admin/transactions" 
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full"
            >
              进入管理
            </Link>
          </CardContent>
        </Card>

        {/* 系统统计 */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📊 系统统计
            </CardTitle>
            <CardDescription>
              查看系统整体数据和使用情况统计
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Link 
              href="/admin/statistics" 
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full"
            >
              查看统计
            </Link>
          </CardContent>
        </Card>

        {/* Token使用统计 */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🔧 Token使用统计
            </CardTitle>
            <CardDescription>
              查看API调用和token消耗统计分析
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Link 
              href="/admin/token-usage" 
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full"
            >
              查看统计
            </Link>
          </CardContent>
        </Card>

        {/* 模型定价管理 */}


        {/* 系统设置 */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              ⚙️ 系统设置
            </CardTitle>
            <CardDescription>
              管理系统配置参数和业务规则
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Link 
              href="/admin/system-settings" 
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full"
            >
              进入设置
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}