"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { api } from "~/trpc/react";
// import { formatCost, formatTokenCount } from "~/lib/token-utils"; // 暂时禁用
import { 
  Cpu, 
  DollarSign, 
  Users, 
  BarChart3 
} from "lucide-react";

export function DashboardStats() {
  // 获取系统级token使用统计
  const { data: tokenStats } = api.tokenUsage.getSystemUsageStats.useQuery({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 最近30天
    endDate: new Date(),
  });

  // 获取用户统计
  const { data: userStats } = api.user.getUserStats.useQuery();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总用户数</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{userStats?.totalUsers || 0}</div>
          <p className="text-xs text-muted-foreground">
            活跃用户: {userStats?.activeUsers || 0}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Token使用量</CardTitle>
          <Cpu className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {(tokenStats?.totalStats.totalTokens || 0).toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            最近30天
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">API成本</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            ${(tokenStats?.totalStats.totalCostUsd || 0).toFixed(4)}
          </div>
          <p className="text-xs text-muted-foreground">
            扣除积分: {(tokenStats?.totalStats.totalCreditsDeducted || 0).toLocaleString()}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">API调用次数</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {(tokenStats?.totalStats.totalUsages || 0).toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            最近30天
          </p>
        </CardContent>
      </Card>
    </div>
  );
}