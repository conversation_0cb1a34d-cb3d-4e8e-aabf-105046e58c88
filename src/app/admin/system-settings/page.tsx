"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Switch } from "~/components/ui/switch";
import { Save, RefreshCw, Plus, Trash2 } from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";

interface SettingItem {
  key: string;
  value: string;
  description?: string;
  isNew?: boolean;
}

export default function SystemSettingsPage() {
  const [settings, setSettings] = useState<SettingItem[]>([]);
  const [hasChanges, setHasChanges] = useState(false);

  // 获取所有系统设置
  const { data: systemSettings, isLoading, refetch } = api.systemSettings.getAll.useQuery();

  // 使用useEffect处理数据更新
  useEffect(() => {
    if (systemSettings) {
      setSettings(systemSettings.map(s => ({ key: s.key, value: s.value, description: s.description || undefined })));
      setHasChanges(false);
    }
  }, [systemSettings]);

  // 批量更新设置
  const updateMutation = api.systemSettings.batchUpdate.useMutation({
    onSuccess: () => {
      toast.success("系统设置保存成功");
      setHasChanges(false);
      void refetch();
    },
    onError: (error) => {
      toast.error(`保存失败: ${error.message}`);
    },
  });

  // 删除设置
  const deleteMutation = api.systemSettings.delete.useMutation({
    onSuccess: () => {
      toast.success("设置删除成功");
      void refetch();
    },
    onError: (error) => {
      toast.error(`删除失败: ${error.message}`);
    },
  });

  const updateSetting = (index: number, field: keyof SettingItem, value: string) => {
    const newSettings = [...settings];
    const currentSetting = newSettings[index];
    if (currentSetting) {
      newSettings[index] = { 
        key: currentSetting.key || "",
        value: currentSetting.value || "",
        description: currentSetting.description,
        isNew: currentSetting.isNew,
        [field]: value 
      };
      setSettings(newSettings);
      setHasChanges(true);
    }
  };

  const addNewSetting = () => {
    setSettings([...settings, { key: "", value: "", description: "", isNew: true }]);
    setHasChanges(true);
  };

  const removeSetting = (index: number) => {
    const setting = settings[index];
    if (setting?.isNew) {
      // 如果是新添加的设置，直接从列表中移除
      const newSettings = settings.filter((_, i) => i !== index);
      setSettings(newSettings);
      setHasChanges(true);
    } else if (setting) {
      // 如果是已存在的设置，调用删除API
      deleteMutation.mutate({ key: setting.key });
    }
  };

  const handleSave = () => {
    const validSettings = settings.filter(s => s.key.trim() && s.value.trim());
    updateMutation.mutate(validSettings.map(s => ({
      key: s.key,
      value: s.value,
      description: s.description,
    })));
  };

  // 预定义的重要设置
  const importantSettings = [
    {
      key: "CREDIT_USD_RATE",
      title: "积分汇率",
      description: "1个积分等于多少美元",
      type: "number",
    },
    {
      key: "AUTO_DEDUCT_CREDITS",
      title: "自动扣除积分",
      description: "是否自动扣除用户积分用于API调用",
      type: "boolean",
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">系统设置</h1>
          <p className="text-muted-foreground">
            管理系统级别的配置参数和业务规则
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => refetch()} disabled={isLoading}>
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>
          <Button
            onClick={handleSave}
            disabled={!hasChanges || updateMutation.isPending}
          >
            <Save className="mr-2 h-4 w-4" />
            {updateMutation.isPending ? "保存中..." : "保存更改"}
          </Button>
        </div>
      </div>

      {/* 重要设置 */}
      <Card>
        <CardHeader>
          <CardTitle>核心设置</CardTitle>
          <CardDescription>系统运行的关键配置参数</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {importantSettings.map((settingDef) => {
            const setting = settings.find(s => s.key === settingDef.key);
            const index = settings.findIndex(s => s.key === settingDef.key);
            
            return (
              <div key={settingDef.key} className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
                <div>
                  <Label className="text-base font-medium">{settingDef.title}</Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {settingDef.description}
                  </p>
                </div>
                <div className="md:col-span-2">
                  {settingDef.type === "boolean" ? (
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={setting?.value === "true"}
                        onCheckedChange={(checked) => {
                          if (index >= 0) {
                            updateSetting(index, "value", checked.toString());
                          }
                        }}
                      />
                      <span className="text-sm">
                        {setting?.value === "true" ? "启用" : "禁用"}
                      </span>
                    </div>
                  ) : (
                    <Input
                      type={settingDef.type === "number" ? "number" : "text"}
                      step={settingDef.type === "number" ? "0.001" : undefined}
                      value={setting?.value || ""}
                      onChange={(e) => {
                        if (index >= 0) {
                          updateSetting(index, "value", e.target.value);
                        }
                      }}
                      placeholder={`输入${settingDef.title}`}
                    />
                  )}
                </div>
              </div>
            );
          })}
        </CardContent>
      </Card>

      {/* 所有设置 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>所有设置</CardTitle>
              <CardDescription>系统中的所有配置参数</CardDescription>
            </div>
            <Button variant="outline" onClick={addNewSetting}>
              <Plus className="mr-2 h-4 w-4" />
              添加设置
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {settings.map((setting, index) => (
              <div key={`${setting.key}-${index}`} className="grid grid-cols-1 md:grid-cols-12 gap-4 items-start p-4 border rounded-lg">
                <div className="md:col-span-3">
                  <Label htmlFor={`key-${index}`}>设置键</Label>
                  <Input
                    id={`key-${index}`}
                    value={setting.key}
                    onChange={(e) => updateSetting(index, "key", e.target.value)}
                    placeholder="SETTING_KEY"
                    className="font-mono"
                  />
                </div>
                <div className="md:col-span-3">
                  <Label htmlFor={`value-${index}`}>设置值</Label>
                  <Input
                    id={`value-${index}`}
                    value={setting.value}
                    onChange={(e) => updateSetting(index, "value", e.target.value)}
                    placeholder="设置值"
                  />
                </div>
                <div className="md:col-span-5">
                  <Label htmlFor={`desc-${index}`}>描述</Label>
                  <Textarea
                    id={`desc-${index}`}
                    value={setting.description || ""}
                    onChange={(e) => updateSetting(index, "description", e.target.value)}
                    placeholder="设置描述（可选）"
                    rows={2}
                  />
                </div>
                <div className="md:col-span-1 flex items-end">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeSetting(index)}
                    disabled={deleteMutation.isPending}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      )}
    </div>
  );
}