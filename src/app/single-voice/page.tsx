"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Badge } from "~/components/ui/badge";
import { Loader2, Play, Download, Volume2, Globe, User, Zap, Pause, Square, History, Clock } from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";

// 简化类型定义，使用any避免复杂的类型匹配
type VoiceRole = any;

export default function SingleVoicePage() {
  const [selectedLanguage, setSelectedLanguage] = useState<string>("");
  const [selectedRole, setSelectedRole] = useState<VoiceRole | null>(null);
  const [selectedModel, setSelectedModel] = useState<any | null>(null);
  const [text, setText] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);
  const [playbackHistory, setPlaybackHistory] = useState<Array<{
    id: string;
    text: string;
    roleName: string;
    audioUrl: string;
    timestamp: Date;
  }>>([]);
  const [languageSearch, setLanguageSearch] = useState("");

  // 获取语言统计
  const { data: languages, isLoading: languagesLoading } = api.tts.getLanguageStats.useQuery();

  // 过滤语言列表
  const filteredLanguages = (languages as any)?.filter((lang: any) =>
    lang.name.toLowerCase().includes(languageSearch.toLowerCase()) ||
    lang.nativeName.toLowerCase().includes(languageSearch.toLowerCase()) ||
    lang.code.toLowerCase().includes(languageSearch.toLowerCase())
  ) || [];

  // 根据选择的语言获取角色
  const { data: roles, isLoading: rolesLoading } = api.tts.getRolesByLanguage.useQuery(
    {
      languageCode: selectedLanguage,
      limit: 50
    },
    {
      enabled: !!selectedLanguage
    }
  );

  // 生成语音
  const generateVoice = api.tts.generateVoiceSample.useMutation({
    onSuccess: (data) => {
      setAudioUrl(data.audioUrl);

      // 添加到播放历史
      if (selectedRole) {
        const historyItem = {
          id: Date.now().toString(),
          text: text.trim(),
          roleName: selectedRole.nameEn || selectedRole.nameZh || 'Unknown',
          audioUrl: data.audioUrl,
          timestamp: new Date(),
        };
        setPlaybackHistory(prev => [historyItem, ...prev.slice(0, 9)]); // 保留最近10条
      }

      toast.success("语音生成成功！");
      setIsGenerating(false);
    },
    onError: (error) => {
      toast.error(`生成失败: ${error.message}`);
      setIsGenerating(false);
    },
  });

  const handleGenerate = async () => {
    if (!selectedRole || !text.trim() || !selectedLanguage) {
      toast.error("请选择语言、角色并输入文本");
      return;
    }

    if (!selectedModel) {
      toast.error("请选择生成模型");
      return;
    }

    setIsGenerating(true);
    setAudioUrl(null);

    try {
      await generateVoice.mutateAsync({
        roleId: selectedRole.id,
        language: selectedLanguage,
        text: text.trim(),
        modelMappingId: selectedModel.id,
      });
    } catch (error) {
      // Error handled in onError callback
    }
  };

  const handlePlay = (url?: string) => {
    const targetUrl = url || audioUrl;
    if (targetUrl) {
      // 停止当前播放的音频
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0;
      }

      const audio = new Audio(targetUrl);
      setCurrentAudio(audio);
      setIsPlaying(true);

      audio.play().catch(() => {
        setIsPlaying(false);
        toast.error("音频播放失败");
      });

      audio.onended = () => {
        setIsPlaying(false);
        setCurrentAudio(null);
      };

      audio.onerror = () => {
        setIsPlaying(false);
        setCurrentAudio(null);
        toast.error("音频播放失败");
      };
    }
  };

  const handlePause = () => {
    if (currentAudio) {
      currentAudio.pause();
      setIsPlaying(false);
    }
  };

  const handleStop = () => {
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
      setIsPlaying(false);
      setCurrentAudio(null);
    }
  };

  const handleDownload = () => {
    if (audioUrl) {
      const link = document.createElement('a');
      link.href = audioUrl;
      link.download = `tts-${selectedRole?.nameEn || 'voice'}-${Date.now()}.wav`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">AI语音生成</h1>
        <p className="text-gray-600">选择语言和角色，输入文本生成高质量的AI语音</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：语言和角色选择 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 语言选择 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                选择语言
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <input
                type="text"
                placeholder="搜索语言..."
                value={languageSearch}
                onChange={(e) => setLanguageSearch(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                <SelectTrigger>
                  <SelectValue placeholder="请选择语言" />
                </SelectTrigger>
                <SelectContent>
                  {languagesLoading ? (
                    <SelectItem value="loading" disabled>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      加载中...
                    </SelectItem>
                  ) : filteredLanguages.length === 0 ? (
                    <SelectItem value="no-results" disabled>
                      未找到匹配的语言
                    </SelectItem>
                  ) : (
                    filteredLanguages?.map((lang: any) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        <div className="flex items-center justify-between w-full">
                          <span>{lang.name}</span>
                          <Badge variant="secondary" className="ml-2">
                            {lang.roleCount} 个角色
                          </Badge>
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* 角色选择 */}
          {selectedLanguage && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  选择角色
                  {rolesLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {rolesLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin" />
                    <span className="ml-2">加载角色中...</span>
                  </div>
                ) : roles && (roles as any).length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {(roles as any).map((role: any) => {
                      const primaryModel = role.modelMappings[0];
                      return (
                        <div
                          key={role.id}
                          className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                            selectedRole?.id === role.id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => {
                            setSelectedRole(role);
                            // 自动选择默认模型（优先级最高的活跃模型）
                            const defaultModel = role.modelMappings.find((mapping: any) =>
                              mapping.model.isActive && mapping.model.provider.isActive
                            );
                            setSelectedModel(defaultModel || null);
                          }}
                        >
                          <div className="flex items-start gap-3">
                            {role.avatarUrl ? (
                              <img
                                src={role.avatarUrl}
                                alt={role.nameEn || 'Avatar'}
                                className="w-12 h-12 rounded-full object-cover"
                              />
                            ) : (
                              <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                                <User className="h-6 w-6 text-gray-500" />
                              </div>
                            )}
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-gray-900 truncate">
                                {role.nameEn}
                              </h3>
                              <p className="text-sm text-gray-600 truncate">
                                {role.nameZh}
                              </p>
                              <div className="flex items-center gap-2 mt-2">
                                {primaryModel && (
                                  <Badge
                                    variant={primaryModel.model.provider.slug === 'gemini' ? 'default' : 'secondary'}
                                    className="text-xs"
                                  >
                                    <Zap className="h-3 w-3 mr-1" />
                                    {primaryModel.model.provider.name}
                                  </Badge>
                                )}
                                {(role.genderEn || role.genderZh) && (
                                  <Badge variant="outline" className="text-xs">
                                    {role.genderEn || role.genderZh}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    该语言暂无可用角色
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* 右侧：文本输入和生成 */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>文本输入</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="text">要转换的文本</Label>
                <Textarea
                  id="text"
                  placeholder="请输入要转换为语音的文本..."
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  rows={6}
                  className="mt-1"
                />
                <p className="text-sm text-gray-500 mt-1">
                  字符数: {text.length}
                </p>
              </div>

              {selectedRole && (
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                  <h4 className="font-medium text-sm text-gray-900 mb-3">选中角色详情</h4>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      {selectedRole.avatarUrl ? (
                        <img
                          src={selectedRole.avatarUrl}
                          alt={selectedRole.nameEn}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center">
                          <User className="h-5 w-5 text-white" />
                        </div>
                      )}
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{selectedRole.nameEn}</p>
                        <p className="text-xs text-gray-600">{selectedRole.nameZh}</p>
                      </div>
                    </div>

                    {/* 技术详情 */}
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="bg-white/60 rounded p-2">
                        <span className="text-gray-500">语音ID:</span>
                        <p className="font-mono text-gray-800 truncate">{selectedRole.voiceName}</p>
                      </div>
                      {selectedRole.gender && (
                        <div className="bg-white/60 rounded p-2">
                          <span className="text-gray-500">性别:</span>
                          <p className="text-gray-800">{selectedRole.gender}</p>
                        </div>
                      )}
                    </div>

                    {/* 模型选择器 */}
                    {selectedRole.modelMappings.length > 0 && (
                      <div className="bg-white/60 rounded p-3">
                        <label className="text-xs text-gray-500 mb-2 block">选择生成模型:</label>
                        <div className="space-y-2">
                          {selectedRole.modelMappings
                            .filter((mapping: any) => mapping.model.isActive && mapping.model.provider.isActive)
                            .map((mapping: any) => {
                              const isSelected = selectedModel?.id === mapping.id;
                              const isHighQuality = mapping.model.name.includes('hd') || mapping.model.name.includes('pro');
                              const isQuick = mapping.model.name.includes('turbo') || mapping.model.name.includes('flash');

                              return (
                                <div
                                  key={mapping.id}
                                  className={`p-2 border rounded cursor-pointer transition-all hover:shadow-sm ${
                                    isSelected
                                      ? 'border-blue-500 bg-blue-50'
                                      : 'border-gray-200 hover:border-gray-300'
                                  }`}
                                  onClick={() => setSelectedModel(mapping)}
                                >
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                      <Badge
                                        variant={mapping.model.provider.slug === 'gemini' ? 'default' : 'secondary'}
                                        className="text-xs px-1 py-0"
                                      >
                                        {mapping.model.provider.name}
                                      </Badge>
                                      <span className="text-xs font-medium text-gray-800">
                                        {mapping.model.displayName || mapping.model.name}
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      {isHighQuality && (
                                        <Badge variant="outline" className="text-xs px-1 py-0 text-green-600 border-green-300">
                                          高质量
                                        </Badge>
                                      )}
                                      {isQuick && (
                                        <Badge variant="outline" className="text-xs px-1 py-0 text-blue-600 border-blue-300">
                                          快速
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                  <div className="text-xs text-gray-500 mt-1">
                                    {isHighQuality && '高质量语音，生成时间较长'}
                                    {isQuick && '快速生成，适合预览'}
                                    {!isHighQuality && !isQuick && '标准质量'}
                                  </div>
                                </div>
                              );
                            })}
                        </div>
                      </div>
                    )}

                    {/* 当前选择的模型信息 */}
                    {selectedModel && (
                      <div className="bg-white/60 rounded p-2">
                        <span className="text-xs text-gray-500 mb-1 block">当前模型:</span>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1">
                            <Badge
                              variant={selectedModel.model.provider.slug === 'gemini' ? 'default' : 'secondary'}
                              className="text-xs px-1 py-0"
                            >
                              {selectedModel.model.provider.name}
                            </Badge>
                            <span className="text-xs text-gray-600">{selectedModel.model.displayName}</span>
                          </div>
                          <span className="text-xs text-gray-400">优先级 {selectedModel.priority}</span>
                        </div>
                      </div>
                    )}

                    {/* 预估消费 */}
                    <div className="bg-amber-50 border border-amber-200 rounded p-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-amber-700">预估消费:</span>
                        <span className="text-xs font-medium text-amber-800">
                          {Math.ceil(text.length / 100)} 积分
                        </span>
                      </div>
                      <p className="text-xs text-amber-600 mt-1">
                        按字符数计费，约 {text.length} 字符
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <Button
                onClick={handleGenerate}
                disabled={!selectedRole || !text.trim() || isGenerating}
                className="w-full"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Volume2 className="h-4 w-4 mr-2" />
                    生成语音
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* 音频播放 */}
          {audioUrl && (
            <Card>
              <CardHeader>
                <CardTitle>生成结果</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 播放控制按钮 */}
                <div className="grid grid-cols-4 gap-2">
                  <Button
                    onClick={() => handlePlay()}
                    disabled={isPlaying}
                    variant="outline"
                    size="sm"
                  >
                    <Play className="h-4 w-4 mr-1" />
                    播放
                  </Button>
                  <Button
                    onClick={handlePause}
                    disabled={!isPlaying}
                    variant="outline"
                    size="sm"
                  >
                    <Pause className="h-4 w-4 mr-1" />
                    暂停
                  </Button>
                  <Button
                    onClick={handleStop}
                    disabled={!currentAudio}
                    variant="outline"
                    size="sm"
                  >
                    <Square className="h-4 w-4 mr-1" />
                    停止
                  </Button>
                  <Button
                    onClick={handleDownload}
                    variant="outline"
                    size="sm"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </div>

                {/* 原生音频控制器 */}
                <audio
                  src={audioUrl}
                  controls
                  className="w-full"
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                  onEnded={() => setIsPlaying(false)}
                />

                {/* 音频信息 */}
                <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                  <div className="flex items-center justify-between">
                    <span>格式: WAV</span>
                    <span>质量: 高清</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 播放历史 */}
          {playbackHistory.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5" />
                  播放历史
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {playbackHistory.map((item) => (
                    <div
                      key={item.id}
                      className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => handlePlay(item.audioUrl)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {item.roleName}
                          </p>
                          <p className="text-xs text-gray-600 line-clamp-2 mt-1">
                            {item.text}
                          </p>
                          <div className="flex items-center gap-1 mt-2">
                            <Clock className="h-3 w-3 text-gray-400" />
                            <span className="text-xs text-gray-500">
                              {item.timestamp.toLocaleTimeString()}
                            </span>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePlay(item.audioUrl);
                          }}
                        >
                          <Play className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
