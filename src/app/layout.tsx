import "~/styles/globals.css";

import { type Metadata } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { SessionProvider } from "next-auth/react";

import { TRPCReactProvider } from "~/trpc/react";
import { Provider } from "~/components/ui/provider";

export const metadata: Metadata = {
  title: "Voctana - 现代化应用",
  description: "基于 T3 Stack 构建的现代化全栈应用",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="zh-CN" className={`${geist.variable}`} suppressHydrationWarning>
      <body>
        <SessionProvider>
          <Provider>
            <TRPCReactProvider>{children}</TRPCReactProvider>
          </Provider>
        </SessionProvider>
      </body>
    </html>
  );
}
