"use client";

import { useMemo, useState } from "react";
import { But<PERSON> } from "~/components/ui/button";

import { Play, Volume2, X } from "lucide-react";
import AudioSample from "./AudioSample";
import { ShineBorder } from "~/components/magicui/shine-border";
import { SparklesText } from "~/components/magicui/sparkles-text";

// 语音角色数据类型
interface VoiceCharacter {
  id: string;
  name: string;
  gender: "male" | "female" | "neutral";
  language: string; // 主语言标签（展示用）
  style: string;
  description: string;
  audioUrl: string; // 旧字段（不再直接使用）
  tags: string[];
  avatar?: string;
}

// 模拟语音角色数据（30个角色）
const voiceCharacters: VoiceCharacter[] = [
  {
    id: "voice-001",
    name: "小雅",
    gender: "female",
    language: "中文",
    style: "温柔甜美",
    description: "适合客服、教育、有声读物等场景",
    audioUrl: "/audio/voice-001.mp3",
    tags: ["温柔", "甜美", "客服"],
  },
  {
    id: "voice-002",
    name: "小明",
    gender: "male",
    language: "中文",
    style: "沉稳专业",
    description: "适合新闻播报、企业宣传等正式场景",
    audioUrl: "/audio/voice-002.mp3",
    tags: ["沉稳", "专业", "新闻"],
  },
  {
    id: "voice-003",
    name: "Emma",
    gender: "female",
    language: "English",
    style: "活泼自然",
    description: "Perfect for casual conversations and storytelling",
    audioUrl: "/audio/voice-003.mp3",
    tags: ["活泼", "自然", "英语"],
  },
  // 添加更多角色数据...
  ...Array.from({ length: 27 }, (_, i) => ({
    id: `voice-${String(i + 4).padStart(3, "0")}`,
    name: `角色${i + 4}`,
    gender: (["male", "female", "neutral"] as const)[(i % 3) as 0 | 1 | 2] as VoiceCharacter["gender"],
    language: (["中文", "English", "日本語"] as const)[(i % 3) as 0 | 1 | 2] as string,
    style: (["温柔", "活泼", "沉稳", "专业", "可爱"] as const)[(i % 5) as 0 | 1 | 2 | 3 | 4] as string,
    description: `适合各种应用场景的优质语音角色 ${i + 4}`,
    audioUrl: `/audio/voice-${String(i + 4).padStart(3, "0")}.mp3`,
    tags: ["优质", "多场景"],
  })),
];

// 生成该角色的28条试听样本（占位示例）
// 真实环境中应从后端/API获取
function getSamplesForCharacter(character: VoiceCharacter) {
  const base = `/audio/${character.id}`; // 建议路径：/public/audio/voice-xxx/style-01.mp3
  return Array.from({ length: 28 }, (_, i) => {
    const idx = String(i + 1).padStart(2, "0");
    return {
      url: `${base}/style-${idx}.mp3`,
      label: `语言${idx}`,
    };
  });
}

// 性别显示
function getGenderText(gender: string) {
  switch (gender) {
    case "male":
      return "男声";
    case "female":
      return "女声";
    case "neutral":
      return "中性";
    default:
      return gender;
  }
}

// 性别徽章颜色
function getGenderColor(_gender: string) {
  // 统一使用主题语义色，避免硬编码颜色
  return "bg-secondary text-secondary-foreground";
}

export default function VoicePreview() {



  const [openRoleId, setOpenRoleId] = useState<string | null>(null);

  const openRole = useMemo(
    () => voiceCharacters.find((c) => c.id === openRoleId) || null,
    [openRoleId]
  );

  // 列表（已移除筛选，直接展示全部）
  const filteredCharacters = voiceCharacters;

  return (
    <div id="voice-preview" className="w-full py-16 bg-background scroll-mt-24 md:scroll-mt-28 min-h-[40vh] lg:min-h-[50vh]">
      <div className="mx-auto w-full max-w-[1120px] px-4">
        {/* 标题 */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-foreground mb-4">
            <SparklesText>语音试听</SparklesText>
          </h2>

        </div>



        {/* 卡片模式（紧凑三行） */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {filteredCharacters.map((character) => (
            <div
              key={character.id}
              className="group flex items-center gap-3 rounded-lg border border-border bg-card/70 p-3 shadow-sm hover:shadow transition"
            >
              {/* 头像 */}
              <div className="flex-shrink-0 h-10 w-10 rounded-full overflow-hidden bg-gradient-to-br from-[hsl(var(--primary))] to-[hsl(var(--secondary))] text-primary-foreground text-[12px] font-semibold flex items-center justify-center">
                {character.avatar ? (
                  <img src={character.avatar} alt={character.name} className="h-full w-full object-cover" />
                ) : (
                  character.name.slice(0, 2)
                )}
              </div>

              {/* 中部文本（3行紧凑） */}
              <div className="min-w-0 flex-1 leading-tight">
                <div className="flex items-center gap-2 truncate">
                  <span className="text-sm font-medium text-foreground truncate">
                    {character.name}
                  </span>
                  <span className={`px-1.5 py-0.5 rounded-full text-[10px] font-medium ${getGenderColor(character.gender)}`}>
                    {getGenderText(character.gender)}
                  </span>
                  <span className="ml-1 hidden sm:inline px-1.5 py-0.5 rounded-full text-[10px] font-medium bg-accent text-accent-foreground">
                    28语
                  </span>
                </div>
                <div className="text-xs text-muted-foreground truncate">
                  {character.style} · {character.language}
                </div>
                <div className="text-xs text-muted-foreground line-clamp-1">
                  {character.description}
                </div>
              </div>

              {/* 右侧按钮 */}
              <div className="flex-shrink-0">
                <Button
                  onClick={() => setOpenRoleId(character.id)}
                  variant="outline"
                  className="h-8 px-3 flex items-center gap-1"
                  aria-label={`试听 ${character.name}`}
                >
                  <Play className="h-4 w-4" />
                  <span className="hidden sm:inline">试听</span>
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* 无结果 */}
        {filteredCharacters.length === 0 && (
          <div className="text-center py-12">
            <div className="text-muted-foreground mb-4">
              <Volume2 className="h-16 w-16 mx-auto mb-4 opacity-50" />
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">
              未找到匹配的语音角色
            </h3>
            <p className="text-muted-foreground">
              请尝试调整搜索条件或筛选选项
            </p>
          </div>
        )}

        {/* 试听列表弹窗 */}
        {openRole && (
          <div
            className="fixed inset-0 z-50 flex items-center justify-center"
            aria-modal="true"
            role="dialog"
          >
            {/* 遮罩 */}
            <div
              className="absolute inset-0 bg-background/80 backdrop-blur-sm"
              onClick={() => setOpenRoleId(null)}
            />
            {/* 内容 */}
            <div className="relative z-10 mx-4 w-full max-w-[1120px] rounded-2xl bg-card shadow-2xl overflow-hidden">
              <ShineBorder
                borderWidth={3}
                duration={10}
                shineColor={["var(--primary)", "var(--secondary)", "var(--accent)"]}
                className="rounded-2xl"
              />
              {/* 头部 */}
              <div className="flex items-center justify-between p-6 pb-4">
                <div className="min-w-0">
                  <div className="text-lg font-semibold text-foreground truncate">
                    {openRole.name} · 试听列表（28条）
                  </div>
                  <div className="text-xs text-muted-foreground truncate mt-1">
                    提示：若本地未放置 /public/audio/{openRole.id}/style-01.mp3 等文件，可替换为可访问链接以验证。
                  </div>
                </div>
                <Button
                  variant="outline"
                  className="h-8 w-8 p-0"
                  aria-label="关闭"
                  onClick={() => setOpenRoleId(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* 列表主体 */}
              <div className="px-6 pb-6 max-h-[75vh] overflow-auto">
                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-4">
                  {getSamplesForCharacter(openRole).map((s) => (
                    <AudioSample key={s.url} url={s.url} label={s.label} />
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}