"use client";

import { useEffect, useMemo, useRef, useState } from "react";
import { Button } from "~/components/ui/button";
import { Play, Pause } from "lucide-react";


export interface AudioSampleProps {
  url: string;
  label?: string;
  preload?: "none" | "metadata" | "auto";
  className?: string;
}

function formatTime(sec: number) {
  if (!isFinite(sec) || sec < 0) return "0:00";
  const m = Math.floor(sec / 60);
  const s = Math.floor(sec % 60);
  return `${m}:${s.toString().padStart(2, "0")}`;
}

/**
 * 轻量试听组件：传入 url 与可选 label 即可播放
 * - 支持播放/暂停、进度显示与拖动
 * - 采用独立 audio 元素（后续可升级为全局单例播放器）
 */
export default function AudioSample({
  url,
  label,
  preload = "metadata",
  className,
}: AudioSampleProps) {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [current, setCurrent] = useState(0);
  const [duration, setDuration] = useState(0);
  const [buffering, setBuffering] = useState(false);

  // 事件绑定
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const onLoaded = () => setDuration(audio.duration || 0);
    const onTime = () => setCurrent(audio.currentTime || 0);
    const onPlay = () => setIsPlaying(true);
    const onPause = () => setIsPlaying(false);
    const onEnded = () => {
      setIsPlaying(false);
      setCurrent(0);
    };
    const onWaiting = () => setBuffering(true);
    const onCanPlay = () => setBuffering(false);
    const onStalled = () => setBuffering(true);
    const onError = () => setIsPlaying(false);

    audio.addEventListener("loadedmetadata", onLoaded);
    audio.addEventListener("timeupdate", onTime);
    audio.addEventListener("play", onPlay);
    audio.addEventListener("pause", onPause);
    audio.addEventListener("ended", onEnded);
    audio.addEventListener("waiting", onWaiting);
    audio.addEventListener("canplay", onCanPlay);
    audio.addEventListener("stalled", onStalled);
    audio.addEventListener("error", onError);

    return () => {
      audio.removeEventListener("loadedmetadata", onLoaded);
      audio.removeEventListener("timeupdate", onTime);
      audio.removeEventListener("play", onPlay);
      audio.removeEventListener("pause", onPause);
      audio.removeEventListener("ended", onEnded);
      audio.removeEventListener("waiting", onWaiting);
      audio.removeEventListener("canplay", onCanPlay);
      audio.removeEventListener("stalled", onStalled);
      audio.removeEventListener("error", onError);
    };
  }, []);

  // url 变化时重置
  useEffect(() => {
    setIsPlaying(false);
    setCurrent(0);
    setDuration(0);
    const audio = audioRef.current;
    if (audio) {
      audio.pause();
      audio.currentTime = 0;
      audio.load();
    }
  }, [url]);

  const progress = useMemo(() => {
    if (!duration || duration <= 0) return 0;
    return Math.min(100, Math.max(0, (current / duration) * 100));
  }, [current, duration]);

  const toggle = () => {
    const audio = audioRef.current;
    if (!audio) return;
    if (isPlaying) {
      audio.pause();
    } else {
      void audio.play();
    }
  };

  const onSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    const audio = audioRef.current;
    if (!audio || !duration) return;
    const rect = (e.currentTarget as HTMLDivElement).getBoundingClientRect();
    const ratio = (e.clientX - rect.left) / rect.width;
    const next = Math.max(0, Math.min(1, ratio)) * duration;
    audio.currentTime = next;
    setCurrent(next);
  };

  // 从 label 中提取语言代码，返回国家代码用于获取真实国旗
  const getCountryCode = (label: string) => {
    const countryMap: Record<string, string> = {
      "STYLE_01": "cn", "STYLE_02": "us", "STYLE_03": "jp", "STYLE_04": "es", "STYLE_05": "fr",
      "STYLE_06": "de", "STYLE_07": "kr", "STYLE_08": "sa", "STYLE_09": "pt", "STYLE_10": "ru",
      "STYLE_11": "in", "STYLE_12": "it", "STYLE_13": "tr", "STYLE_14": "id", "STYLE_15": "th",
      "STYLE_16": "vn", "STYLE_17": "ir", "STYLE_18": "il", "STYLE_19": "se", "STYLE_20": "nl",
      "STYLE_21": "pl", "STYLE_22": "cz", "STYLE_23": "hu", "STYLE_24": "dk", "STYLE_25": "no",
      "STYLE_26": "fi", "STYLE_27": "ua", "STYLE_28": "gr"
    };
    return countryMap[label] || "un";
  };

  const getFlagUrl = (label: string) => {
    const countryCode = getCountryCode(label);
    return `https://flagcdn.com/w80/${countryCode}.png`;
  };

  return (
    <button
      className={[
        "relative flex items-center justify-center aspect-square w-16 h-16 rounded-full shadow-lg hover:shadow-xl transition-all cursor-pointer overflow-hidden group border-2 border-white/20",
        className || "",
      ].join(" ")}
      onClick={toggle}
      aria-label={`${getCountryCode(label || "").toUpperCase()} ${isPlaying ? "暂停试听" : "开始试听"}`}
      style={{
        background: `linear-gradient(135deg, rgba(0,0,0,0.3), rgba(0,0,0,0.5)), 
                     url("${getFlagUrl(label || "")}")`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      {/* 播放图标 - 居中显示 */}
      <div className="relative z-10 text-white drop-shadow-lg">
        {isPlaying ? (
          <Pause className="h-6 w-6" />
        ) : (
          <Play className="h-6 w-6" />
        )}
      </div>

      {/* 缓冲状态 */}
      {buffering && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-full">
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}

      {/* 隐藏的音频元素 */}
      <audio ref={audioRef} src={url} preload={preload} />
    </button>
  );
}