"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { Button } from "~/components/ui/button";
import { useTheme } from "next-themes";
import { useSession, signOut } from "next-auth/react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { ShinyButton } from "~/components/magicui/shiny-button";

export default function Navbar() {
  const [language, setLanguage] = useState("zh");
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { theme, setTheme, systemTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const { data: session, status } = useSession();



  // 语言选项
  const languages = [
    { code: "zh", name: "中文" },
    { code: "en", name: "English" },
    { code: "ja", name: "日本語" },
  ];
  const flagByCode: Record<string, string> = { zh: "🇨🇳", en: "🇺🇸", ja: "🇯🇵" };

  // 确保组件在客户端挂载后再渲染主题切换按钮
  useEffect(() => {
    setMounted(true);
  }, []);

  // 切换主题
  const toggleTheme = () => {
    if (theme === "system") {
      // 如果当前是系统主题，根据系统主题切换到相反的主题
      setTheme(systemTheme === "dark" ? "light" : "dark");
    } else {
      // 如果当前是深色或浅色主题，切换到相反的主题
      setTheme(theme === "dark" ? "light" : "dark");
    }
  };

  return (
    <nav className="fixed top-0 w-full bg-background border-b border-border z-50">
      <div className="mx-auto w-full max-w-[1120px] px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo 和名称 */}
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 mr-2"></div>
            <Link href="/" className="text-xl font-bold text-foreground">
              Voctana
            </Link>
          </div>

          {/* 桌面端导航 */}
          <div className="hidden md:flex items-center space-x-1">
            <Link 
              href="/" 
              className="px-3 py-2 rounded-md text-sm font-medium font-semibold text-foreground bg-muted"
            >
              首页
            </Link>
            <Link 
              href="/single-voice" 
              className="px-3 py-2 rounded-md text-sm font-medium text-foreground/80 hover:text-foreground hover:bg-muted transition-all"
            >
              单人语音
            </Link>
            <Link 
              href="/multi-dialogue" 
              className="px-3 py-2 rounded-md text-sm font-medium text-foreground/80 hover:text-foreground hover:bg-muted transition-all"
            >
              多人对话
            </Link>
          </div>

          {/* 右侧功能区 */}
          <div className="flex items-center space-x-4">
            {/* 语言切换 - 桌面端 */}
            <div className="hidden md:flex items-center">
              <Popover>
                <PopoverTrigger asChild>
                  <button className="flex items-center justify-center w-8 h-8 text-lg border border-border rounded-md bg-background hover:bg-muted transition-colors">
                    <span>{flagByCode[language] ?? "🏳️"}</span>
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-[100px] p-1" align="start">
                  <div className="space-y-1">
                    {languages.map((lang) => (
                      <button
                        key={lang.code}
                        onClick={() => setLanguage(lang.code)}
                        className={`flex items-center gap-1 w-full px-2 py-1.5 text-sm rounded-sm hover:bg-muted transition-colors ${
                          language === lang.code ? 'bg-muted' : ''
                        }`}
                      >
                        <span>{flagByCode[lang.code] ?? "🏳️"}</span>
                        <span className="truncate">{lang.name}</span>
                      </button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* 主题切换按钮 */}
            {mounted ? (
              <button
                onClick={toggleTheme}
                className="p-2 rounded-full bg-muted text-foreground hover:bg-muted/80 transition-all flex items-center justify-center"
                aria-label="切换主题"
              >
                {theme === "dark" || (theme === "system" && systemTheme === "dark") ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                  </svg>
                )}
              </button>
            ) : (
              <div className="w-10 h-10 rounded-full bg-muted"></div>
            )}

            {/* 登录/用户状态 */}
            {status === "loading" ? (
              <div className="hidden md:flex w-16 h-9 bg-muted rounded-md animate-pulse"></div>
            ) : session ? (
              <Popover>
                <PopoverTrigger asChild>
                  <button className="hidden md:flex items-center space-x-2 px-3 py-2 rounded-md bg-muted hover:bg-muted/80 transition-colors">
                    <div className="w-6 h-6 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white text-xs font-bold">
                      {session.user?.name?.charAt(0).toUpperCase() || session.user?.email?.charAt(0).toUpperCase()}
                    </div>
                    <span className="text-sm font-medium">{session.user?.name || session.user?.email}</span>
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-2" align="end">
                  <div className="space-y-2">
                    <div className="px-2 py-1.5 text-sm text-muted-foreground">
                      {session.user?.email}
                    </div>
                    <hr className="border-border" />
                    <button
                      onClick={() => signOut({ callbackUrl: "/" })}
                      className="w-full px-2 py-1.5 text-sm text-left rounded-sm hover:bg-muted transition-colors text-red-600 hover:text-red-700"
                    >
                      退出登录
                    </button>
                  </div>
                </PopoverContent>
              </Popover>
            ) : (
              <Link href="/login">
                <ShinyButton className="hidden md:flex">
                  登录
                </ShinyButton>
              </Link>
            )}

            {/* 移动端菜单按钮 */}
            <button 
              className="md:hidden text-foreground focus:outline-none"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* 移动端菜单 */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-border">
            <div className="flex flex-col space-y-3 px-2">
              <Link 
                href="/" 
                className="px-3 py-2 rounded-md text-base font-medium text-foreground bg-muted font-semibold"
                onClick={() => setIsMenuOpen(false)}
              >
                首页
              </Link>
              <Link 
                href="/single-voice" 
                className="px-3 py-2 rounded-md text-base font-medium text-foreground/80 hover:text-foreground hover:bg-muted transition-all"
                onClick={() => setIsMenuOpen(false)}
              >
                单人语音
              </Link>
              <Link 
                href="/multi-dialogue" 
                className="px-3 py-2 rounded-md text-base font-medium text-foreground/80 hover:text-foreground hover:bg-muted transition-all"
                onClick={() => setIsMenuOpen(false)}
              >
                多人对话
              </Link>
              
              {/* 语言切换 - 移动端 */}
              <div className="pt-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <button className="flex items-center justify-center w-full px-3 py-2 text-xl border border-border rounded-md bg-background hover:bg-muted transition-colors">
                      <span>{flagByCode[language] ?? "🏳️"}</span>
                    </button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-1" align="start">
                    <div className="space-y-1">
                      {languages.map((lang) => (
                        <button
                          key={lang.code}
                          onClick={() => {
                            setLanguage(lang.code);
                            setIsMenuOpen(false);
                          }}
                          className={`flex items-center gap-2 w-full px-2 py-1.5 text-base rounded-sm hover:bg-muted transition-colors ${
                            language === lang.code ? 'bg-muted' : ''
                          }`}
                        >
                          <span>{flagByCode[lang.code] ?? "🏳️"}</span>
                          <span>{lang.name}</span>
                        </button>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
              
              {/* 登录/用户状态 - 移动端 */}
              {status === "loading" ? (
                <div className="w-full h-10 bg-muted rounded-md animate-pulse"></div>
              ) : session ? (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 px-3 py-2 rounded-md bg-muted">
                    <div className="w-6 h-6 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white text-xs font-bold">
                      {session.user?.name?.charAt(0).toUpperCase() || session.user?.email?.charAt(0).toUpperCase()}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium">{session.user?.name || session.user?.email}</div>
                      <div className="text-xs text-muted-foreground">{session.user?.email}</div>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      signOut({ callbackUrl: "/" });
                      setIsMenuOpen(false);
                    }}
                    className="w-full px-3 py-2 text-sm text-left rounded-md hover:bg-muted transition-colors text-red-600 hover:text-red-700 border border-red-200"
                  >
                    退出登录
                  </button>
                </div>
              ) : (
                <Link href="/login">
                  <ShinyButton className="w-full" onClick={() => setIsMenuOpen(false)}>
                    登录
                  </ShinyButton>
                </Link>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}