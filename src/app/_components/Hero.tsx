"use client";

import { useMemo, useRef, createRef, useEffect, useState } from "react";

import { AnimatedGradientText } from "~/components/magicui/animated-gradient-text";
import { RainbowButton } from "~/components/magicui/rainbow-button";
import { AnimatedBeam } from "~/components/magicui/animated-beam";
import { Bot } from "lucide-react";

export default function Hero() {
  // Beam 场景的 refs
  const containerRef = useRef<HTMLDivElement | null>(null);
  const botRef = useRef<HTMLDivElement | null>(null);
  const languages = useMemo(
    () => ["ZH","EN","JA","ES","FR","DE","KO","AR","PT","RU","HI","IT","TR","ID","TH","VI","FA","HE","SV","NL","PL","CS","HU","DA","NO","FI","UK","EL"],
    []
  );
  const flagMap = useMemo(() => ({
    ZH: "🇨🇳", EN: "🇺🇸", JA: "🇯🇵", ES: "🇪🇸", FR: "🇫🇷", DE: "🇩🇪", KO: "🇰🇷", AR: "🇸🇦",
    PT: "🇵🇹", RU: "🇷🇺", HI: "🇮🇳", IT: "🇮🇹", TR: "🇹🇷", ID: "🇮🇩", TH: "🇹🇭", VI: "🇻🇳",
    FA: "🇮🇷", HE: "🇮🇱", SV: "🇸🇪", NL: "🇳🇱", PL: "🇵🇱", CS: "🇨🇿", HU: "🇭🇺", DA: "🇩🇰",
    NO: "🇳🇴", FI: "🇫🇮", UK: "🇺🇦", EL: "🇬🇷"
  }), []);
  const langRefs = useMemo(() => languages.map(() => createRef<HTMLDivElement>()), [languages]);
  // 仅为一半节点绘制光束，减少遮挡与“多一圈”的视觉错觉
  const beamIndices = useMemo(() => languages.map((_, i) => i).filter((i) => i % 2 === 0), [languages]);

  // 自适应半径：随容器尺寸变化，保持完美圆环
  const [radius, setRadius] = useState(120);
  useEffect(() => {
    const el = containerRef.current;
    if (!el) return;
    const ro = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const cr = entry.contentRect;
        const size = Math.min(cr.width, cr.height);
        // 预留边距与节点尺寸，取比例半径
        const ratio = size < 420 ? 0.36 : 0.44;
        setRadius(Math.max(45, size * ratio));
      }
    });
    ro.observe(el);
    return () => ro.disconnect();
  }, []);

  // 环绕位置（使用旋转+平移，适配不同容器尺寸）
  const nodeClass =
    "relative z-10 h-10 w-10 md:h-12 md:w-12 flex items-center justify-center rounded-full border border-border bg-card shadow backdrop-blur select-none";

  return (
    <div className="relative w-full h-[60vh] pt-24 md:pt-28">
      {/* 背景：Dot Pattern */}
      <div
        aria-hidden
        className="absolute inset-0"
        style={{
          backgroundImage:
            "radial-gradient(var(--border) 1px, transparent 1px)",
          backgroundSize: "16px 16px",
          backgroundPosition: "0 0",
        }}
      />

      {/* 内容 */}
      <div className="relative z-10 h-full flex items-center">
        <div className="mx-auto w-full max-w-[1120px] px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-center h-full min-h-[300px]">
            {/* 左侧文字 */}
            <div className="flex flex-col justify-center space-y-4">
              <div className="space-y-3">
                <h1 className="font-bold">
                  <AnimatedGradientText
                    colorFrom="var(--primary)"
                    colorTo="var(--secondary)"
                    speed={1.5}
                    className="text-3xl md:text-4xl lg:text-5xl font-bold"
                  >
                    Voctana · AI TTS 文本转语音
                  </AnimatedGradientText>
                </h1>

                <div className="text-sm md:text-base lg:text-base max-w-2xl leading-relaxed space-y-1">
                  <AnimatedGradientText colorFrom="var(--primary)" colorTo="var(--secondary)" speed={1.2} className="font-medium">
                    文本转语音的智能 AI TTS 工具，支持“魔法文字”精细控制角色风格。
                  </AnimatedGradientText>
                  <AnimatedGradientText colorFrom="var(--primary)" colorTo="var(--secondary)" speed={1.2} className="font-medium">
                    30 个角色 × 30 种语言，输出自然、流畅、接近真人的语音。
                  </AnimatedGradientText>
                </div>
              </div>

              <div className="flex flex-row flex-nowrap gap-3">
                <RainbowButton className="px-4 py-2 text-sm md:px-6 md:py-3 md:text-base w-auto whitespace-nowrap text-primary-foreground dark:text-background">开始使用</RainbowButton>
                <RainbowButton
                  variant="outline"
                  className="px-4 py-2 text-sm md:px-6 md:py-3 md:text-base w-auto whitespace-nowrap"
                  onClick={() => {
                    const el = document.getElementById("voice-preview");
                    if (!el) return;
                    const nav = document.querySelector("nav");
                    const navH = nav instanceof HTMLElement ? nav.offsetHeight : 0;
                    const top = el.getBoundingClientRect().top + window.scrollY - navH - 8;
                    window.scrollTo({ top, behavior: "smooth" });
                  }}
                >
                  试听语音
                </RainbowButton>
              </div>
            </div>

            {/* 右侧：机器人 + 语言环绕 + 光束 */}
            <div className="flex items-center justify-center lg:justify-end">
              <div
                ref={containerRef}
                className="relative w-full max-w-md md:max-w-lg lg:max-w-xl aspect-square"
              >
                {/* 动画光束（在最底层，绝对定位） */}
                {beamIndices.map((i) => {
                  const toRef = langRefs[i];
                  return (
                    <AnimatedBeam
                      key={`beam-${i}`}
                      containerRef={containerRef}
                      fromRef={botRef}
                      toRef={toRef as React.RefObject<HTMLElement>}
                      curvature={Math.max(10, radius * 0.20)}
                      reverse={i % 2 === 0}
                      delay={i * 0.08}
                      duration={4 + (i % 3) * 0.6}
                      pathColor="var(--primary)"
                      pathWidth={2}
                      pathOpacity={0.22}
                      gradientStartColor="var(--primary)"
                      gradientStopColor="var(--secondary)"
                      shrinkStart={8}
                      shrinkEnd={12}
                    />
                  );
                })}

                {/* 中心机器人 */}
                <div
                  ref={botRef}
                  className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 h-14 w-14 md:h-16 md:w-16 rounded-2xl bg-gradient-to-br from-[var(--primary)] to-[var(--secondary)] shadow-lg flex items-center justify-center text-primary-foreground"
                >
                  <Bot className="h-8 w-8 md:h-9 md:w-9" />
                </div>

                {/* 圆环参考线（调试/校准） */}
                <div
                  aria-hidden
                  className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full border border-border/50 pointer-events-none"
                  style={{ width: `${radius * 2}px`, height: `${radius * 2}px` }}
                />

                {/* 语言节点（环形排布） */}
                {languages.map((code, i) => {
                  const step = (Math.PI * 2) / languages.length;
                  const angleRad = -Math.PI / 2 + i * step; // 从正上方开始等分
                  const angleDeg = (angleRad * 180) / Math.PI;

                  return (
                    <div
                      key={code}
                      className="absolute left-1/2 top-1/2"
                      style={{
                        transform: `translate(-50%, -50%) rotate(${angleDeg}deg) translateY(-${radius}px)`,
                      }}
                    >
                      <div
                        ref={langRefs[i]}
                        className={`${nodeClass} transform-gpu text-base md:text-lg font-semibold text-foreground`}
                        style={{ transform: `rotate(${-angleDeg}deg)` }}
                        aria-label={`语言 ${code}`}
                        title={code}
                      >
                        {flagMap[code as keyof typeof flagMap] ?? code}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}