import { NextResponse } from "next/server";
import { db } from "~/server/db";

export async function GET(_req: Request, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const role = await db.ttsRole.findUnique({
    where: { id },
    include: { _count: { select: { languageSupports: true } } },
  });
  if (!role) return NextResponse.json({ error: "Not found" }, { status: 404 });
  return NextResponse.json({
    ...role,
    sampleCount: (role as any)._count?.languageSupports ?? 0,
  });
}

export async function PATCH(req: Request, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const body = await req.json().catch(() => ({}));
  try {
    const role = await db.ttsRole.update({
      where: { id },
      data: {
        slug: body.slug ?? undefined,
        name: body.name ?? undefined,
        avatarUrl: body.avatarUrl ?? undefined,
        description: body.description ?? undefined,
        stylesEn: body.stylesEn ?? undefined,
        stylesZh: body.stylesZh ?? undefined,
        voiceName: body.voiceName ?? undefined,
        isActive: typeof body.isActive === "boolean" ? body.isActive : undefined,
      },
    });
    return NextResponse.json(role);
  } catch (err: any) {
    return NextResponse.json({ error: err?.message ?? "更新失败" }, { status: 500 });
  }
}

export async function DELETE(_req: Request, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  try {
    await db.roleLanguageSupport.deleteMany({ where: { roleId: id } });
    await db.ttsRole.delete({ where: { id } });
    return NextResponse.json({ ok: true });
  } catch (err: any) {
    return NextResponse.json({ error: err?.message ?? "删除失败" }, { status: 500 });
  }
}