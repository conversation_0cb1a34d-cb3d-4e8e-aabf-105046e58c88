import { NextResponse } from "next/server";
import { db } from "~/server/db";
import { r2UploadBuffer } from "~/server/api/tts/r2";
import { createGeminiTTSClient } from "~/lib/gemini-tts";

// 列表：支持按 language 过滤
export async function GET(req: Request, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const url = new URL(req.url);
  const language = url.searchParams.get("language") || undefined;

  const role = await db.ttsRole.findUnique({ where: { id } });
  if (!role) return NextResponse.json({ error: "Role not found" }, { status: 404 });

  const samples = await db.roleLanguageSupport.findMany({
    where: { roleId: role.id, languageCode: language ?? undefined },
    include: { language: true },
    orderBy: { createdAt: "desc" },
  });

  return NextResponse.json(samples, { headers: { "Cache-Control": "no-store" } });
}

// 生成并上传试听（占位：需完善 Gemini TTS 接入）
// body: { text: string, language: string, style?: string }
export async function POST(req: Request, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const body = await req.json().catch(() => ({}));
  const { text, language, style } = body || {};
  if (!text || !language) {
    return NextResponse.json({ error: "text 与 language 为必填" }, { status: 400 });
  }

  const role = await db.ttsRole.findUnique({ where: { id } });
  if (!role) return NextResponse.json({ error: "Role not found" }, { status: 404 });
  if (!role.isActive) return NextResponse.json({ error: "角色未启用" }, { status: 400 });

  // 调用 Gemini TTS
  let audioBase64: string;
  try {
    const geminiTTS = createGeminiTTSClient(db);
    const result = await geminiTTS.synthesizeSpeech({
      text,
      voiceName: role.voiceName,
    });
    audioBase64 = result.audioContent;
  } catch (err: any) {
    return NextResponse.json({ error: err?.message ?? "TTS 合成失败" }, { status: 500 });
  }

  // 转换 Base64 为 Buffer
  const audio = Buffer.from(audioBase64, 'base64');

  const keyBase = `roles/${role.slug}/${language}/${style ? `${style}/` : ""}`;
  const key = `${keyBase}preview-${Date.now()}.mp3`;

  // 上传 R2
  let up;
  try {
    up = await r2UploadBuffer({ key, buffer: audio, contentType: "audio/mpeg" });
  } catch (err: any) {
    return NextResponse.json({ error: err?.message ?? "上传失败" }, { status: 500 });
  }

  // 更新语言支持记录的样本URL
  const sample = await db.roleLanguageSupport.upsert({
    where: {
      roleId_languageCode: {
        roleId: role.id,
        languageCode: language,
      },
    },
    update: {
      sampleUrl: up.url,
    },
    create: {
      roleId: role.id,
      languageCode: language,
      sampleUrl: up.url,
      quality: 'standard',
      isDefault: false,
    },
  });

  return NextResponse.json(sample, { status: 201 });
}