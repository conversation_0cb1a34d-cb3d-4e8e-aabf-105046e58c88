import { NextResponse } from "next/server";
import { db } from "~/server/db";

export async function GET() {
  const roles = await db.ttsRole.findMany({
    orderBy: { createdAt: "desc" },
    include: {
      _count: { select: { languageSupports: true } },
    },
  });
  return NextResponse.json(
    roles.map((r) => ({
      id: r.id,
      slug: r.slug,
      name: r.name,
      avatarUrl: r.avatarUrl,
      description: r.description,
      isActive: r.isActive,
      stylesEn: r.stylesEn,
      stylesZh: r.stylesZh,
      voiceName: r.voiceName,
      sampleCount: (r as any)._count?.languageSupports ?? 0,
      createdAt: r.createdAt,
      updatedAt: r.updatedAt,
    })),
    { headers: { "Cache-Control": "no-store" } },
  );
}

export async function POST(req: Request) {
  const body = await req.json().catch(() => ({}));
  const { slug, name, avatarUrl, description, stylesEn = [], stylesZh = [], voiceName, isActive = true } = body || {};
  if (!slug || !name || !voiceName) {
    return NextResponse.json({ error: "slug, name 与 voiceName 为必填" }, { status: 400 });
  }
  try {
    const role = await db.ttsRole.create({
      data: { slug, name, avatarUrl, description, stylesEn, stylesZh, voiceName, isActive },
    });
    return NextResponse.json(role, { status: 201 });
  } catch (err: any) {
    return NextResponse.json({ error: err?.message ?? "创建失败" }, { status: 500 });
  }
}