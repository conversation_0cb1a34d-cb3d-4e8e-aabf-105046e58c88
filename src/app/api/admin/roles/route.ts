import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/server/auth";
import { db } from "~/server/db";
import { z } from "zod";

// 验证管理员权限
async function verifyAdmin() {
  const session = await auth();
  if (!session?.user) {
    return { error: "未登录", status: 401 };
  }

  const user = await db.user.findUnique({
    where: { id: session.user.id },
    select: { role: true },
  });

  if (user?.role !== "ADMIN") {
    return { error: "权限不足", status: 403 };
  }

  return { user: session.user };
}

// 获取所有语音角色
export async function GET() {
  try {
    const authResult = await verifyAdmin();
    if (authResult.error) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const roles = await db.ttsRole.findMany({
      orderBy: { createdAt: "desc" },
      include: {
        _count: {
          select: {
            languageSupports: true,
          },
        },
      },
    });

    return NextResponse.json({ roles });
  } catch (error) {
    console.error("获取语音角色失败:", error);
    return NextResponse.json(
      { error: "获取语音角色失败" },
      { status: 500 }
    );
  }
}

// 创建语音角色的验证schema
const createRoleSchema = z.object({
  name: z.string().min(1, "角色名称不能为空"),
  slug: z.string().min(1, "角色标识不能为空"),
  description: z.string().optional(),
  languages: z.array(z.string()).min(1, "至少需要一种语言"),
  styles: z.array(z.string()).default([]),
  voiceName: z.string().min(1, "语音名称不能为空"),
  isActive: z.boolean().default(true),
  avatarUrl: z.string().url().optional(),
});

// 创建新语音角色
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAdmin();
    if (authResult.error) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const body = await request.json();
    const validatedData = createRoleSchema.parse(body);

    // 检查角色名称和slug是否已存在
    const existingRole = await db.ttsRole.findFirst({
      where: {
        OR: [
          { name: validatedData.name },
          { slug: validatedData.slug },
        ],
      },
    });

    if (existingRole) {
      return NextResponse.json(
        { error: "角色名称或标识已存在" },
        { status: 400 }
      );
    }

    const role = await db.ttsRole.create({
      data: validatedData,
    });

    return NextResponse.json({ role }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "数据验证失败", details: error.errors },
        { status: 400 }
      );
    }

    console.error("创建语音角色失败:", error);
    return NextResponse.json(
      { error: "创建语音角色失败" },
      { status: 500 }
    );
  }
}