import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/server/auth";
import { db } from "~/server/db";

// 验证管理员权限
async function verifyAdmin() {
  const session = await auth();
  if (!session?.user) {
    return { error: "未登录", status: 401 };
  }

  const user = await db.user.findUnique({
    where: { id: session.user.id },
  });

  if (user?.role !== "ADMIN") {
    return { error: "权限不足", status: 403 };
  }

  return { user: session.user };
}

// 删除语音角色
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await verifyAdmin();
    if (authResult.error) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { id } = await params;

    // 检查角色是否存在
    const role = await db.ttsRole.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            languageSupports: true,
          },
        },
      },
    });

    if (!role) {
      return NextResponse.json(
        { error: "语音角色不存在" },
        { status: 404 }
      );
    }

    // 如果有关联的语言支持，先删除语言支持
    if (role._count.languageSupports > 0) {
      await db.roleLanguageSupport.deleteMany({
        where: { roleId: id },
      });
    }

    // 删除角色
    await db.ttsRole.delete({
      where: { id },
    });

    return NextResponse.json({ message: "语音角色删除成功" });
  } catch (error) {
    console.error("删除语音角色失败:", error);
    return NextResponse.json(
      { error: "删除语音角色失败" },
      { status: 500 }
    );
  }
}

// 更新语音角色
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await verifyAdmin();
    if (authResult.error) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { id } = await params;
    const body = await request.json();

    // 检查角色是否存在
    const existingRole = await db.ttsRole.findUnique({
      where: { id },
    });

    if (!existingRole) {
      return NextResponse.json(
        { error: "语音角色不存在" },
        { status: 404 }
      );
    }

    // 更新角色
    const updatedRole = await db.ttsRole.update({
      where: { id },
      data: {
        ...body,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({ role: updatedRole });
  } catch (error) {
    console.error("更新语音角色失败:", error);
    return NextResponse.json(
      { error: "更新语音角色失败" },
      { status: 500 }
    );
  }
}