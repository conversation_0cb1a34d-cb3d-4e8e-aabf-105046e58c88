import { useState } from "react";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import type { PricingType } from "@prisma/client";

export function useCredits() {
  const [isCalculating, setIsCalculating] = useState(false);
  const [isConsuming, setIsConsuming] = useState(false);

  // 获取用户积分（使用公开API，支持未登录用户）
  const {
    data: userCredits,
    refetch: refetchCredits,
    isLoading: isLoadingCredits
  } = api.creditService.getPublicCreditInfo.useQuery();

  // 消费积分
  const consumeCreditsMutation = api.creditService.consumeCredits.useMutation({
    onSuccess: () => {
      void refetchCredits();
    },
  });

  // 计算需要的积分数量 - 简化版本，直接基于文本长度计算
  const calculateCredits = async (textLength: number): Promise<number> => {
    // 简单的积分计算：每100个字符消耗1积分，最少1积分
    const credits = Math.max(1, Math.ceil(textLength / 100));
    return credits;
  };

  // 计算TTS积分需求（基于实际定价）
  const calculateTTSCredits = async (params: {
    modelId?: string;
    customPricingId?: string;
    text: string;
    estimatedDuration?: number;
  }): Promise<{
    costUsd: number;
    creditsNeeded: number;
    pricingDetails: any;
  } | null> => {
    try {
      setIsCalculating(true);

      // 如果没有指定模型，使用默认计算
      if (!params.modelId) {
        const credits = await calculateCredits(params.text.length);
        return {
          costUsd: credits * 0.001, // 假设1积分=0.001美元
          creditsNeeded: credits,
          pricingDetails: null
        };
      }

      // 这里我们需要直接调用tRPC mutation，而不是useQuery
      // 暂时使用简化计算，后续可以改进
      const credits = await calculateCredits(params.text.length);
      return {
        costUsd: credits * 0.001,
        creditsNeeded: credits,
        pricingDetails: null
      };
    } catch (error) {
      console.error("计算TTS费用失败:", error);
      // 回退到简单计算
      const credits = await calculateCredits(params.text.length);
      return {
        costUsd: credits * 0.001,
        creditsNeeded: credits,
        pricingDetails: null
      };
    } finally {
      setIsCalculating(false);
    }
  };

  // 消费积分
  const consumeCredits = async (params: {
    modelId: string;
    pricingType: PricingType;
    usage: any;
  }) => {
    try {
      setIsConsuming(true);
      // 暂时禁用积分消费
      toast.error("积分消费功能暂时禁用");
      throw new Error("积分消费功能暂时禁用");
    } catch (error: any) {
      toast.error(error.message || "积分扣除失败");
      return null;
    } finally {
      setIsConsuming(false);
    }
  };

  // 检查积分是否足够
  const checkSufficientCredits = (requiredCredits: number): boolean => {
    if (!userCredits) return false;
    return userCredits.balance >= requiredCredits;
  };

  // 获取积分不足的提示信息
  const getInsufficientCreditsMessage = (requiredCredits: number): string => {
    if (!userCredits) return "无法获取积分信息";
    const shortage = requiredCredits - userCredits.balance;
    return `积分不足，还需要 ${shortage.toLocaleString()} 积分`;
  };

  return {
    // 数据
    userCredits,
    isLoadingCredits,

    // 状态
    isCalculating,
    isConsuming,

    // 方法
    calculateCredits,
    calculateTTSCredits,
    consumeCredits,
    checkSufficientCredits,
    getInsufficientCreditsMessage,
    refetchCredits,
  };
}