"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { CreditCardIcon, PlusIcon } from "lucide-react";
import { api } from "~/trpc/react";
// import { formatCost } from "~/lib/token-utils"; // 暂时禁用

interface UserCreditDisplayProps {
  userId?: string;
  showTopUp?: boolean;
  className?: string;
}

export function UserCreditDisplay({ 
  userId, 
  showTopUp = false, 
  className 
}: UserCreditDisplayProps) {
  // 获取用户积分信息
  const { data: creditInfo, isLoading } = api.user.getUserById.useQuery(
    { id: userId || "" },
    { enabled: !!userId }
  );

  // 获取积分汇率
  const { data: creditRate } = api.systemSettings.getCreditRate.useQuery();

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="animate-pulse">
            <div className="h-4 bg-muted rounded w-20 mb-2"></div>
            <div className="h-6 bg-muted rounded w-16"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 类型断言以访问credit字段
  const userWithCredit = creditInfo as typeof creditInfo & {
    credit?: {
      balance: number;
      totalSpent: number;
      totalEarned: number;
    };
  };
  
  const credit = userWithCredit?.credit;
  const balance = credit?.balance ?? 0;
  const totalSpent = credit?.totalSpent ?? 0;
  const totalEarned = credit?.totalEarned ?? 0;
  
  // 计算积分的美元价值
  const balanceUsd = balance * (creditRate?.rate || 0.001);
  const spentUsd = totalSpent * (creditRate?.rate || 0.001);

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium flex items-center">
          <CreditCardIcon className="mr-2 h-4 w-4" />
          积分余额
        </CardTitle>
        {showTopUp && (
          <Button size="sm" variant="outline">
            <PlusIcon className="mr-1 h-3 w-3" />
            充值
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-2xl font-bold">{balance.toLocaleString()}</span>
            <Badge variant="secondary">
              ≈ ${balanceUsd.toFixed(4)}
            </Badge>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
            <div>
              <div className="font-medium">总获得</div>
              <div>{totalEarned.toLocaleString()}</div>
            </div>
            <div>
              <div className="font-medium">总消费</div>
              <div>{totalSpent.toLocaleString()}</div>
              <div className="text-xs">≈ ${spentUsd.toFixed(4)}</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}