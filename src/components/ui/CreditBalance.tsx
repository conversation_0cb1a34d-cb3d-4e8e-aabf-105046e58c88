"use client";

import { useState } from "react";
import { Card, CardContent } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover";
import { 
  Coins, 
  TrendingUp, 
  TrendingDown, 
  RefreshCw,
  Info,
  CreditCard,
  Zap
} from "lucide-react";
import { api } from "~/trpc/react";
import { formatCredits, creditsToUsd } from "~/lib/creditUtils";
import { cn } from "~/lib/utils";

interface CreditBalanceProps {
  variant?: "default" | "compact" | "detailed";
  showUsdEquivalent?: boolean;
  showRefreshButton?: boolean;
  className?: string;
}

export function CreditBalance({ 
  variant = "default",
  showUsdEquivalent = true,
  showRefreshButton = false,
  className 
}: CreditBalanceProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  const { 
    data: userCredits, 
    refetch, 
    isLoading,
    isRefetching 
  } = api.creditService.getUserCredits.useQuery();

  // 暂时禁用交易记录
  const recentTransactions = { transactions: [] };

  const handleRefresh = () => {
    refetch();
  };

  // 紧凑模式
  if (variant === "compact") {
    return (
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button variant="ghost" size="sm" className={cn("h-8 px-2", className)}>
            <Coins className="h-4 w-4 mr-1" />
            <span className="font-medium">
              {isLoading ? "..." : formatCredits(userCredits?.balance || 0)}
            </span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="end">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">积分余额</h4>
              {showRefreshButton && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={handleRefresh}
                  disabled={isRefetching}
                >
                  <RefreshCw className={cn("h-3 w-3", isRefetching && "animate-spin")} />
                </Button>
              )}
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">当前余额</span>
                <span className="font-bold text-lg">
                  {formatCredits(userCredits?.balance || 0)}
                </span>
              </div>
              
              {showUsdEquivalent && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">等值美元</span>
                  <span className="text-sm">
                    ${creditsToUsd(userCredits?.balance || 0).toFixed(3)}
                  </span>
                </div>
              )}
            </div>

            {recentTransactions?.transactions && recentTransactions.transactions.length > 0 && (
              <div className="border-t pt-3">
                <h5 className="text-sm font-medium mb-2">最近交易</h5>
                <div className="space-y-1">
                  {recentTransactions.transactions.slice(0, 3).map((transaction: any) => (
                    <div key={transaction.id} className="flex items-center justify-between text-xs">
                      <span className="text-muted-foreground truncate flex-1">
                        {transaction.description || "无描述"}
                      </span>
                      <span className={cn(
                        "font-medium ml-2",
                        transaction.amount > 0 ? "text-green-600" : "text-red-600"
                      )}>
                        {transaction.amount > 0 ? '+' : ''}{transaction.amount}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>
    );
  }

  // 详细模式
  if (variant === "detailed") {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Coins className="h-5 w-5 text-muted-foreground" />
                <h3 className="font-medium">积分余额</h3>
              </div>
              {showRefreshButton && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={handleRefresh}
                  disabled={isRefetching}
                >
                  <RefreshCw className={cn("h-4 w-4", isRefetching && "animate-spin")} />
                </Button>
              )}
            </div>

            <div className="space-y-3">
              <div>
                <div className="text-2xl font-bold">
                  {isLoading ? "..." : formatCredits(userCredits?.balance || 0)}
                </div>
                {showUsdEquivalent && (
                  <div className="text-sm text-muted-foreground">
                    ≈ ${creditsToUsd(userCredits?.balance || 0).toFixed(3)}
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <div>
                    <div className="font-medium text-green-600">
                      {formatCredits(userCredits?.totalEarned || 0)}
                    </div>
                    <div className="text-muted-foreground">累计获得</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <TrendingDown className="h-4 w-4 text-red-600" />
                  <div>
                    <div className="font-medium text-red-600">
                      {formatCredits(userCredits?.totalSpent || 0)}
                    </div>
                    <div className="text-muted-foreground">累计消费</div>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <Info className="h-3 w-3" />
                <span>1000积分 = $1.00</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 默认模式
  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <Badge variant="secondary" className="flex items-center space-x-1">
        <Coins className="h-3 w-3" />
        <span>{isLoading ? "..." : formatCredits(userCredits?.balance || 0)}</span>
      </Badge>
      
      {showUsdEquivalent && (
        <span className="text-xs text-muted-foreground">
          (${creditsToUsd(userCredits?.balance || 0).toFixed(3)})
        </span>
      )}
      
      {showRefreshButton && (
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={handleRefresh}
          disabled={isRefetching}
          className="h-6 w-6 p-0"
        >
          <RefreshCw className={cn("h-3 w-3", isRefetching && "animate-spin")} />
        </Button>
      )}
    </div>
  );
}

// 积分状态指示器
interface CreditStatusProps {
  requiredCredits: number;
  userCredits?: { balance: number };
  className?: string;
}

export function CreditStatus({ requiredCredits, userCredits, className }: CreditStatusProps) {
  const isLoading = !userCredits;
  const balance = userCredits?.balance || 0;
  const isSufficient = balance >= requiredCredits;
  const shortage = Math.max(0, requiredCredits - balance);

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <div className="flex items-center space-x-1">
        <Zap className={cn(
          "h-4 w-4",
          isLoading ? "text-muted-foreground" :
          isSufficient ? "text-green-600" : "text-red-600"
        )} />
        <span className="text-sm">
          需要 {formatCredits(requiredCredits)} 积分
        </span>
      </div>
      
      {!isLoading && (
        <Badge variant={isSufficient ? "default" : "destructive"}>
          {isSufficient ? "余额充足" : `不足 ${formatCredits(shortage)}`}
        </Badge>
      )}
    </div>
  );
}