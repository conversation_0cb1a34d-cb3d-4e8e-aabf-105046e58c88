"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "~/components/ui/button";
import { PlayIcon, PauseIcon } from "@heroicons/react/24/outline";
import { cn } from "~/lib/utils";

interface AudioPlayerProps {
  audioContent?: string; // Base64 encoded audio
  audioUrl?: string; // Direct audio URL
  audioFormat?: "MP3" | "OGG_OPUS" | "LINEAR16";
  className?: string;
  autoPlay?: boolean;
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
}

export function AudioPlayer({
  audioContent,
  audioUrl,
  audioFormat = "MP3",
  className,
  autoPlay = false,
  onPlay,
  onPause,
  onEnded
}: AudioPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const audioRef = useRef<HTMLAudioElement>(null);

  // 获取音频MIME类型
  const getMimeType = (format: string) => {
    switch (format) {
      case "MP3":
        return "audio/mpeg";
      case "OGG_OPUS":
        return "audio/ogg";
      case "LINEAR16":
        return "audio/wav";
      default:
        return "audio/mpeg";
    }
  };

  // 创建音频URL
  const createAudioUrl = (base64Content: string, format: string) => {
    try {
      console.log("[AudioPlayer] 开始创建音频URL", {
        format,
        contentLength: base64Content.length,
        contentPreview: base64Content.substring(0, 50) + "...",
        isBase64Valid: /^[A-Za-z0-9+/]*={0,2}$/.test(base64Content)
      });
      
      const mimeType = getMimeType(format);
      console.log("[AudioPlayer] 使用MIME类型:", mimeType);
      
      const byteCharacters = atob(base64Content);
      console.log("[AudioPlayer] Base64解码完成", {
        decodedLength: byteCharacters.length,
        firstBytes: Array.from(byteCharacters.substring(0, 10)).map(c => c.charCodeAt(0).toString(16)).join(' ')
      });
      
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      
      // 检查音频文件头
      const header = Array.from(byteArray.slice(0, 12)).map(b => String.fromCharCode(b)).join('');
      console.log("[AudioPlayer] 音频文件头信息", {
        header,
        headerBytes: Array.from(byteArray.slice(0, 12)).map(b => b.toString(16)).join(' '),
        arrayLength: byteArray.length
      });
      
      const blob = new Blob([byteArray], { type: mimeType });
      const url = URL.createObjectURL(blob);
      
      console.log("[AudioPlayer] 音频URL创建成功", {
        url,
        blobSize: blob.size,
        blobType: blob.type
      });
      
      return url;
    } catch (error) {
      console.error("[AudioPlayer] 创建音频URL失败:", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        audioContentLength: base64Content.length
      });
      return null;
    }
  };

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio || (!audioContent && !audioUrl)) return;

    setIsLoading(true);
    setError(null);

    let finalAudioUrl: string;

    if (audioUrl) {
      // 如果提供了直接的音频URL，直接使用
      finalAudioUrl = audioUrl;
    } else if (audioContent) {
      // 如果提供了Base64内容，转换为URL
      const createdUrl = createAudioUrl(audioContent, audioFormat);
      if (!createdUrl) {
        setError("音频格式不支持");
        setIsLoading(false);
        return;
      }
      finalAudioUrl = createdUrl;
    } else {
      setError("未提供音频内容");
      setIsLoading(false);
      return;
    }

    audio.src = finalAudioUrl;

    const handleLoadStart = () => {
      console.log("[AudioPlayer] 音频开始加载", {
        src: audio.src,
        readyState: audio.readyState,
        networkState: audio.networkState
      });
    };

    const handleLoadedMetadata = () => {
      console.log("[AudioPlayer] 音频元数据加载完成", {
        duration: audio.duration,
        readyState: audio.readyState,
        networkState: audio.networkState
      });
      setIsLoading(false);
      if (autoPlay) {
        audio.play().catch((err) => {
          console.error("[AudioPlayer] 自动播放失败:", err);
        });
      }
    };

    const handleLoadedData = () => {
      console.log("[AudioPlayer] 音频数据加载完成", {
        duration: audio.duration,
        readyState: audio.readyState,
        networkState: audio.networkState,
        buffered: audio.buffered.length > 0 ? audio.buffered.end(0) : 0
      });
      setError(null);
    };

    const handleCanPlay = () => {
      console.log("[AudioPlayer] 音频可以播放", {
        duration: audio.duration,
        readyState: audio.readyState
      });
    };

    const handlePlay = () => {
      console.log("[AudioPlayer] 音频开始播放", {
        currentTime: audio.currentTime,
        duration: audio.duration
      });
      setIsPlaying(true);
      onPlay?.();
    };

    const handlePause = () => {
      console.log("[AudioPlayer] 音频暂停", {
        currentTime: audio.currentTime,
        duration: audio.duration
      });
      setIsPlaying(false);
      onPause?.();
    };

    const handleEnded = () => {
      console.log("[AudioPlayer] 音频播放结束");
      setIsPlaying(false);
      onEnded?.();
    };

    const handleError = (event: Event) => {
      const audioElement = event.target as HTMLAudioElement;
      console.error("[AudioPlayer] 音频加载失败", {
        error: audioElement.error,
        errorCode: audioElement.error?.code,
        errorMessage: audioElement.error?.message,
        networkState: audioElement.networkState,
        readyState: audioElement.readyState,
        src: audioElement.src
      });
      setError(`音频加载失败: ${audioElement.error?.message || '未知错误'}`);
      setIsLoading(false);
    };

    audio.addEventListener("loadstart", handleLoadStart);
    audio.addEventListener("loadedmetadata", handleLoadedMetadata);
    audio.addEventListener("loadeddata", handleLoadedData);
    audio.addEventListener("canplay", handleCanPlay);
    audio.addEventListener("play", handlePlay);
    audio.addEventListener("pause", handlePause);
    audio.addEventListener("ended", handleEnded);
    audio.addEventListener("error", handleError);

    return () => {
      audio.removeEventListener("loadstart", handleLoadStart);
      audio.removeEventListener("loadedmetadata", handleLoadedMetadata);
      audio.removeEventListener("loadeddata", handleLoadedData);
      audio.removeEventListener("canplay", handleCanPlay);
      audio.removeEventListener("play", handlePlay);
      audio.removeEventListener("pause", handlePause);
      audio.removeEventListener("ended", handleEnded);
      audio.removeEventListener("error", handleError);
      if (audioContent && !audioUrl) {
        URL.revokeObjectURL(finalAudioUrl);
      }
    };
  }, [audioContent, audioUrl, audioFormat, autoPlay, onPlay, onPause, onEnded]);

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) {
      console.warn("[AudioPlayer] 音频元素不存在");
      return;
    }

    console.log("[AudioPlayer] 切换播放状态", {
      currentlyPlaying: isPlaying,
      readyState: audio.readyState,
      networkState: audio.networkState,
      src: audio.src,
      duration: audio.duration,
      currentTime: audio.currentTime,
      hasAudioContent: !!audioContent,
      hasAudioUrl: !!audioUrl,
      audioContentLength: audioContent?.length || 0
    });

    if (isPlaying) {
      audio.pause();
      console.log("[AudioPlayer] 音频已暂停");
    } else {
      console.log("[AudioPlayer] 尝试播放音频", {
        audioSrc: audio.src,
        readyState: audio.readyState,
        networkState: audio.networkState
      });
      
      audio.play().catch((err) => {
        console.error("[AudioPlayer] 播放失败:", {
          error: err instanceof Error ? err.message : String(err),
          audioSrc: audio.src,
          readyState: audio.readyState,
          networkState: audio.networkState
        });
        setError(`播放失败: ${err instanceof Error ? err.message : '未知错误'}`);
      });
    }
  };



  if (error) {
    return (
      <div className={cn("flex items-center justify-center p-4 text-destructive", className)}>
        <span className="text-sm">{error}</span>
      </div>
    );
  }

  return (
    <div className={cn("flex items-center gap-2 p-2 bg-muted/50 rounded-lg", className)}>
      <audio ref={audioRef} preload="metadata" />
      
      {/* 播放/暂停按钮 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={togglePlayPause}
        disabled={isLoading}
        className="h-8 w-8 p-0"
      >
        {isLoading ? (
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        ) : isPlaying ? (
          <PauseIcon className="h-4 w-4" />
        ) : (
          <PlayIcon className="h-4 w-4" />
        )}
      </Button>
      
      {error && (
        <span className="text-xs text-destructive">{error}</span>
      )}
    </div>
  );
}

export default AudioPlayer;