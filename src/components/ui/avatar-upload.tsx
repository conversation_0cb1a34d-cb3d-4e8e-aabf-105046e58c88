"use client";

import { useState, useRef } from "react";
import { Button } from "~/components/ui/button";
import { Label } from "~/components/ui/label";
import { Card, CardContent } from "~/components/ui/card";
import { Upload, X, User } from "lucide-react";
import { validateAvatarFile, fileToBase64 } from "~/lib/avatar-utils";
import { toast } from "sonner";

interface AvatarUploadProps {
  currentAvatarUrl?: string;
  onAvatarChange: (avatarUrl: string) => void;
  onAvatarRemove: () => void;
  disabled?: boolean;
}

export default function AvatarUpload({ 
  currentAvatarUrl, 
  onAvatarChange, 
  onAvatarRemove, 
  disabled = false 
}: AvatarUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件
    const validation = validateAvatarFile(file);
    if (!validation.valid) {
      toast.error(validation.error);
      return;
    }

    try {
      setIsUploading(true);

      // 创建预览URL
      const base64Data = await fileToBase64(file);
      setPreviewUrl(base64Data);

      // 通知父组件
      onAvatarChange(base64Data);

      toast.success('头像已选择，保存角色后将上传到云端');
    } catch (error) {
      console.error('Error processing avatar file:', error);
      toast.error('处理图片文件时出错');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveAvatar = () => {
    setPreviewUrl(null);
    onAvatarRemove();
    
    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    
    toast.success('头像已移除');
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  // 确定显示的图片URL
  const displayImageUrl = previewUrl || currentAvatarUrl;

  return (
    <div className="space-y-4">
      <Label className="text-sm font-medium">角色头像</Label>
      
      <Card className="overflow-hidden">
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            {/* 头像预览区域 */}
            <div className="relative">
              <div className="w-20 h-20 rounded-lg border border-border bg-muted flex items-center justify-center overflow-hidden">
                {displayImageUrl ? (
                  <img
                    src={displayImageUrl}
                    alt="角色头像"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <User className="w-8 h-8 text-muted-foreground" />
                )}
              </div>
              
              {/* 删除按钮 */}
              {displayImageUrl && !disabled && (
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                  onClick={handleRemoveAvatar}
                >
                  <X className="w-3 h-3" />
                </Button>
              )}
            </div>

            {/* 上传控件 */}
            <div className="flex-1 space-y-2">
              <div className="text-sm text-muted-foreground">
                支持 JPEG、PNG、WebP、GIF 格式，最大 5MB
              </div>
              
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleUploadClick}
                  disabled={disabled || isUploading}
                  className="flex items-center gap-2"
                >
                  <Upload className="w-4 h-4" />
                  {isUploading ? '处理中...' : displayImageUrl ? '更换头像' : '上传头像'}
                </Button>
                
                {displayImageUrl && !disabled && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleRemoveAvatar}
                  >
                    移除
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* 隐藏的文件输入 */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/jpeg,image/png,image/webp,image/gif"
            onChange={handleFileSelect}
            className="hidden"
            disabled={disabled || isUploading}
          />
        </CardContent>
      </Card>

      {/* 提示信息 */}
      {previewUrl && !currentAvatarUrl && (
        <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
          💡 头像将在保存角色时上传到云端存储
        </div>
      )}
      
      {previewUrl && currentAvatarUrl && previewUrl !== currentAvatarUrl && (
        <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
          💡 头像已更新，保存角色后将替换云端文件
        </div>
      )}
    </div>
  );
}
