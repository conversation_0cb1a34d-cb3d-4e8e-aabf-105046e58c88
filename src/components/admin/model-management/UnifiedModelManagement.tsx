"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "~/components/ui/tabs";
import { Badge } from "~/components/ui/badge";
import { 
  Server, 
  Database, 
  DollarSign, 
  BarChart3, 
  Plus,
  RefreshCw,
  Cpu,
  Network,
  TrendingUp
} from "lucide-react";
import { api } from "~/trpc/react";
import { ProviderManagement } from "./provider/ProviderManagement";
import { ModelManagement } from "./model";
import { UsageAnalytics } from "./analytics/UsageAnalytics";

export function UnifiedModelManagement() {
  const [activeTab, setActiveTab] = useState("providers");
  
  // 获取统计数据
  const { data: stats } = api.unifiedModel.getModelStats.useQuery();

  const displayStats = {
    providers: stats?.providers?.total || 0,
    models: stats?.models?.total || 0,
    activePricings: stats?.pricings?.active || 0,
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和统计概览 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">模型管理中心</h1>
          <p className="text-muted-foreground">
            统一管理模型提供商、模型注册、定价策略和使用分析
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200">
            <Database className="mr-1 h-3 w-3" />
            统一管理
          </Badge>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">提供商</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{displayStats.providers}</div>
            <p className="text-xs text-muted-foreground">
              已注册的模型提供商
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">模型</CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{displayStats.models}</div>
            <p className="text-xs text-muted-foreground">
              可用的AI模型
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">使用统计</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">-</div>
            <p className="text-xs text-muted-foreground">
              总请求次数
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 主要功能标签页 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="providers" className="flex items-center space-x-2">
            <Server className="h-4 w-4" />
            <span>提供商</span>
          </TabsTrigger>
          <TabsTrigger value="models" className="flex items-center space-x-2">
            <Cpu className="h-4 w-4" />
            <span>模型</span>
          </TabsTrigger>

          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>分析</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-4">
          <ProviderManagement />
        </TabsContent>

        <TabsContent value="models" className="space-y-4">
          <ModelManagement />
        </TabsContent>



        <TabsContent value="analytics" className="space-y-4">
          <UsageAnalytics />
        </TabsContent>
      </Tabs>
    </div>
  );
}