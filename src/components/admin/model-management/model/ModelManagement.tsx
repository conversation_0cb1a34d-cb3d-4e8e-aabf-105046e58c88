"use client";

import { useState, useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { 
  Plus, 
  Edit, 
  Trash2, 
  RefreshCw, 
  Cpu, 
  Search,
  Filter,
  Eye,
  Settings
} from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { EnhancedModelForm } from "./EnhancedModelForm";
import { getModelTypeLabel } from "~/lib/model-types";

export function ModelManagement() {
  const utils = api.useUtils();
  const { data: models, isLoading, refetch } = api.model.adminList.useQuery();
  const { data: providers } = api.provider.list.useQuery();
  
  const [selectedModel, setSelectedModel] = useState<any>(null);
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  
  // 筛选状态
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProvider, setSelectedProvider] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");

  const deleteMutation = api.model.delete.useMutation({
    onSuccess: () => {
      toast.success("模型删除成功");
      utils.model.adminList.invalidate();
    },
    onError: (error) => {
      toast.error(error.message || "删除失败");
    },
  });

  // 筛选模型
    const filteredModels = useMemo(() => {
    return models?.filter((model: any) => {
      const matchesSearch = searchTerm === "" || 
                           model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           model.displayName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           model.provider.name.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = selectedStatus === "all" || 
                           (selectedStatus === "active" && model.isActive) ||
                           (selectedStatus === "inactive" && !model.isActive);
      
      return matchesSearch && matchesStatus;
    }) || [];
  }, [models, searchTerm, selectedStatus]);

  const handleDelete = (model: any) => {
    if (!confirm(`确认删除模型 "${model.displayName || model.name}"？`)) {
      return;
    }
    deleteMutation.mutate({ id: model.id });
  };

  const handleEdit = (model: any) => {
    setSelectedModel(model);
    setIsEditOpen(true);
  };

  const handleViewDetail = (model: any) => {
    setSelectedModel(model);
    setIsDetailOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* 操作栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">模型管理</h2>
          <p className="text-muted-foreground">管理AI模型注册和配置</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => refetch()} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
            刷新
          </Button>
          <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                添加模型
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>添加新模型</DialogTitle>
              </DialogHeader>
              <EnhancedModelForm 
                onSuccess={() => {
                  setIsCreateOpen(false);
                  utils.model.adminList.invalidate();
                }}
                onCancel={() => setIsCreateOpen(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 筛选栏 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索模型名称..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={selectedProvider} onValueChange={setSelectedProvider}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="提供商" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有提供商</SelectItem>
                {providers?.map((provider) => (
                  <SelectItem key={provider.id} value={provider.id}>
                    {provider.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有状态</SelectItem>
                <SelectItem value="active">活跃</SelectItem>
                <SelectItem value="inactive">禁用</SelectItem>
                <SelectItem value="deprecated">已弃用</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 模型列表 */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : filteredModels.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Cpu className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {models?.length === 0 ? "暂无模型" : "未找到匹配的模型"}
            </h3>
            <p className="text-muted-foreground text-center mb-4">
              {models?.length === 0 
                ? "开始添加AI模型来构建您的服务" 
                : "尝试调整筛选条件或搜索关键词"
              }
            </p>
            {models?.length === 0 && (
              <Button onClick={() => setIsCreateOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                添加第一个模型
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredModels.map((model) => (
            <Card key={model.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Cpu className="h-5 w-5 text-muted-foreground" />
                    <CardTitle className="text-lg">
                      {model.displayName || model.name}
                    </CardTitle>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Badge variant={model.isActive ? "default" : "secondary"}>
                      {model.isActive ? "活跃" : "禁用"}
                    </Badge>
                  </div>
                </div>
                <CardDescription>
                  {model.provider.name} • {getModelTypeLabel(model.modelType)}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">模型名称</span>
                    <code className="bg-muted px-2 py-1 rounded text-xs">
                      {model.name}
                    </code>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">定价配置</span>
                    <span className="font-medium">{model._count.customPricings}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">定价配置</span>
                    <span className="font-medium">{(model as any)._count?.customPricings || 0}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">使用记录</span>
                    <span className="font-medium">{(model as any)._count?.usages || 0}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2 pt-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => handleViewDetail(model)}
                      className="flex-1"
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      详情
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => handleEdit(model)}
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => handleDelete(model)}
                      disabled={model._count.usages > 0 || deleteMutation.isPending}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* 编辑对话框 */}
      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>编辑模型 - {selectedModel?.displayName || selectedModel?.name}</DialogTitle>
          </DialogHeader>
          {selectedModel && (
            <EnhancedModelForm 
              model={selectedModel}
              onSuccess={() => {
                setIsEditOpen(false);
                setSelectedModel(null);
                utils.model.adminList.invalidate();
              }}
              onCancel={() => {
                setIsEditOpen(false);
                setSelectedModel(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* 详情对话框 */}
      <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>模型详情 - {selectedModel?.displayName || selectedModel?.name}</DialogTitle>
          </DialogHeader>
          {selectedModel && (
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">概览</TabsTrigger>
                <TabsTrigger value="cost">成本价格</TabsTrigger>
                <TabsTrigger value="pricing">销售定价</TabsTrigger>
                <TabsTrigger value="usage">使用统计</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="space-y-6">
                {/* 基本信息 */}
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold mb-3">基本信息</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">显示名称:</span>
                        <span>{selectedModel.displayName || selectedModel.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">模型名称:</span>
                        <code className="bg-muted px-1 rounded">{selectedModel.name}</code>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">提供商:</span>
                        <span>{selectedModel.provider.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">模型类型:</span>
                        <Badge variant="outline">{getModelTypeLabel(selectedModel.modelType)}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">状态:</span>
                        <Badge variant={selectedModel.isActive ? "default" : "secondary"}>
                          {selectedModel.isActive ? "活跃" : "禁用"}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">创建时间:</span>
                        <span>{new Date(selectedModel.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-3">描述信息</h3>
                    <div className="text-sm text-muted-foreground">
                      {selectedModel.description || "暂无描述"}
                    </div>
                  </div>
                </div>

                {/* 统计信息 */}
                <div>
                  <h3 className="font-semibold mb-3">统计信息</h3>
                  <div className="grid grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold">{selectedModel._count.customPricings}</div>
                        <div className="text-sm text-muted-foreground">定价配置</div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold">{selectedModel._count.usages}</div>
                        <div className="text-sm text-muted-foreground">使用记录</div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold">
                          {selectedModel.isActive ? "启用" : "禁用"}
                        </div>
                        <div className="text-sm text-muted-foreground">当前状态</div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="cost">
                <div className="text-center py-8">
                  <p className="text-muted-foreground">成本价格信息请在编辑模式中查看和修改</p>
                  <Button 
                    onClick={() => {
                      setIsDetailOpen(false);
                      handleEdit(selectedModel);
                    }}
                    className="mt-4"
                  >
                    编辑模型
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="pricing">
                <div className="text-center py-8">
                  <p className="text-muted-foreground">销售定价信息请在编辑模式中查看和修改</p>
                  <Button 
                    onClick={() => {
                      setIsDetailOpen(false);
                      handleEdit(selectedModel);
                    }}
                    className="mt-4"
                  >
                    编辑模型
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="usage">
                <div className="text-center py-8">
                  <p className="text-muted-foreground">使用统计功能开发中...</p>
                </div>
              </TabsContent>
            </Tabs>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}