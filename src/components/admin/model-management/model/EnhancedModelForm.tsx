"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Switch } from "~/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Badge } from "~/components/ui/badge";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { DollarSign, Calculator, TrendingUp } from "lucide-react";

interface EnhancedModelFormProps {
  model?: any;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function EnhancedModelForm({ model, onSuccess, onCancel }: EnhancedModelFormProps) {
  const { data: providers } = api.provider.list.useQuery();
  
  const [formData, setFormData] = useState({
    providerId: model?.providerId || "",
    name: model?.name || "",
    displayName: model?.displayName || "",
    description: model?.description || "",
    modelType: model?.modelType || "TEXT_GENERATION",
    isActive: model?.isActive ?? true,
  });

  const [costPricingData, setCostPricingData] = useState({
    costPricingType: model?.costPricingType || "TOKEN",
    costInputTokenPrice: model?.costInputTokenPrice || 0,
    costOutputTokenPrice: model?.costOutputTokenPrice || 0,
    costInputTokenUnit: model?.costInputTokenUnit || 1000,
    costOutputTokenUnit: model?.costOutputTokenUnit || 1000,
    costRequestPrice: model?.costRequestPrice || 0,
    costCharacterPrice: model?.costCharacterPrice || 0,
    costCharacterUnit: model?.costCharacterUnit || 1000,
    costImagePrice: model?.costImagePrice || 0,
    costVideoPricePerSecond: model?.costVideoPricePerSecond || 0,
    costAudioPricePerSecond: model?.costAudioPricePerSecond || 0,
    costCurrency: model?.costCurrency || "USD",
  });

  // 销售定价数据 - 简化为单一定价策略
  const [salePricingData, setSalePricingData] = useState({
    name: "默认定价",
    description: "默认销售定价策略",
    multiplier: 2.0,
    inputTokenPrice: 0,
    outputTokenPrice: 0,
    requestPrice: 0,
    characterPrice: 0,
    characterUnit: 1000,
    imagePrice: 0,
    videoPricePerSecond: 0,
    audioPricePerSecond: 0,
    currency: "USD",
    isActive: true,
    isDefault: true,
  });

  const [createSalePricing, setCreateSalePricing] = useState(!model); // 新建模型时默认创建销售定价

  const createMutation = api.model.create.useMutation({
    onSuccess: async (newModel) => {
      toast.success("模型已创建");
      
      // 如果选择创建销售定价，则创建默认定价策略
      if (createSalePricing && newModel?.id) {
        try {
          await createPricingMutation.mutateAsync({
            modelId: newModel.id,
            ...salePricingData,
            pricingType: costPricingData.costPricingType,
          });
          toast.success("默认销售定价已创建");
        } catch (error) {
          toast.error("销售定价创建失败，但模型已创建成功");
        }
      }
      
      onSuccess?.();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const createPricingMutation = api.customPricing.create.useMutation();

  const updateMutation = api.model.update.useMutation({
    onSuccess: () => {
      toast.success("模型已更新");
      onSuccess?.();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  // 自动计算销售价格
  const calculateSalePrice = () => {
    const multiplier = salePricingData.multiplier;
    setSalePricingData(prev => ({
      ...prev,
      inputTokenPrice: costPricingData.costInputTokenPrice * multiplier,
      outputTokenPrice: costPricingData.costOutputTokenPrice * multiplier,
      requestPrice: costPricingData.costRequestPrice * multiplier,
      characterPrice: costPricingData.costCharacterPrice * multiplier,
      characterUnit: costPricingData.costCharacterUnit, // 使用相同的字符单位
      imagePrice: costPricingData.costImagePrice * multiplier,
      videoPricePerSecond: costPricingData.costVideoPricePerSecond * multiplier,
      audioPricePerSecond: costPricingData.costAudioPricePerSecond * multiplier,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const modelData = {
      name: formData.name,
      displayName: formData.displayName,
      description: formData.description || undefined,
      modelType: formData.modelType,
      isActive: formData.isActive,
      providerId: formData.providerId,
      // 包含成本价格数据
      ...costPricingData,
    };

    if (model) {
      updateMutation.mutate({ id: model.id, ...modelData });
    } else {
      createMutation.mutate(modelData);
    }
  };

  const modelTypes = [
    { value: "TEXT_GENERATION", label: "文本生成" },
    { value: "SPEECH_GENERATION", label: "语音生成" },
    { value: "IMAGE_GENERATION", label: "图片生成" },
    { value: "VIDEO_GENERATION", label: "视频生成" },
    { value: "MUSIC_GENERATION", label: "音乐生成" },
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">基本信息</TabsTrigger>
          <TabsTrigger value="cost">成本价格</TabsTrigger>
          <TabsTrigger value="sale">销售定价</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
              <CardDescription>配置模型的基本信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="providerId">提供商</Label>
                  <Select
                    value={formData.providerId}
                    onValueChange={(value) => setFormData({ ...formData, providerId: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择提供商" />
                    </SelectTrigger>
                    <SelectContent>
                      {providers?.map((provider) => (
                        <SelectItem key={provider.id} value={provider.id}>
                          {provider.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="modelType">模型类型</Label>
                  <Select
                    value={formData.modelType}
                    onValueChange={(value) => setFormData({ ...formData, modelType: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择模型类型" />
                    </SelectTrigger>
                    <SelectContent>
                      {modelTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">模型名称</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="例如: gemini-1.5-flash"
                    required
                  />
                  <p className="text-xs text-muted-foreground mt-1">系统内部使用的模型标识名称</p>
                </div>

                <div>
                  <Label htmlFor="displayName">显示名称</Label>
                  <Input
                    id="displayName"
                    value={formData.displayName}
                    onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
                    placeholder="例如: Gemini 1.5 Flash"
                    required
                  />
                  <p className="text-xs text-muted-foreground mt-1">用户界面显示的友好名称</p>
                </div>
              </div>

              <div>
                <Label htmlFor="description">模型描述</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="描述模型的特点和用途..."
                  rows={3}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                />
                <Label htmlFor="isActive">启用模型</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cost" className="space-y-6">
          {/* 成本价格设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5" />
                <span>成本价格设置</span>
              </CardTitle>
              <CardDescription>配置模型提供商的成本价格（用于计算利润和销售定价）</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>定价类型</Label>
                  <Select
                    value={costPricingData.costPricingType}
                    onValueChange={(value: any) => setCostPricingData({ ...costPricingData, costPricingType: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="TOKEN">按Token计费</SelectItem>
                      <SelectItem value="CHARACTER">按字符计费</SelectItem>
                      <SelectItem value="IMAGE">按图片计费</SelectItem>
                      <SelectItem value="VIDEO">按视频计费</SelectItem>
                      <SelectItem value="AUDIO">按音频计费</SelectItem>
                      <SelectItem value="REQUEST">按请求计费</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>货币</Label>
                  <Select
                    value={costPricingData.costCurrency}
                    onValueChange={(value) => setCostPricingData({ ...costPricingData, costCurrency: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">美元 (USD)</SelectItem>
                      <SelectItem value="CNY">人民币 (CNY)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {costPricingData.costPricingType === "TOKEN" && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>输入Token价格</Label>
                      <Input
                        type="number"
                        step="0.000001"
                        value={costPricingData.costInputTokenPrice}
                        onChange={(e) => setCostPricingData({ ...costPricingData, costInputTokenPrice: parseFloat(e.target.value) || 0 })}
                        placeholder="0.000001"
                      />
                    </div>
                    <div>
                      <Label>输入Token单位</Label>
                      <Input
                        type="number"
                        value={costPricingData.costInputTokenUnit}
                        onChange={(e) => setCostPricingData({ ...costPricingData, costInputTokenUnit: parseInt(e.target.value) || 1000 })}
                        placeholder="1000"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>输出Token价格</Label>
                      <Input
                        type="number"
                        step="0.000001"
                        value={costPricingData.costOutputTokenPrice}
                        onChange={(e) => setCostPricingData({ ...costPricingData, costOutputTokenPrice: parseFloat(e.target.value) || 0 })}
                        placeholder="0.000001"
                      />
                    </div>
                    <div>
                      <Label>输出Token单位</Label>
                      <Input
                        type="number"
                        value={costPricingData.costOutputTokenUnit}
                        onChange={(e) => setCostPricingData({ ...costPricingData, costOutputTokenUnit: parseInt(e.target.value) || 1000 })}
                        placeholder="1000"
                      />
                    </div>
                  </div>
                </div>
              )}

              {costPricingData.costPricingType === "CHARACTER" && (
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>字符价格 (USD)</Label>
                      <Input
                        type="number"
                        step="0.000001"
                        value={costPricingData.costCharacterPrice}
                        onChange={(e) => setCostPricingData({ ...costPricingData, costCharacterPrice: parseFloat(e.target.value) || 0 })}
                        placeholder="0.000015"
                      />
                    </div>
                    <div>
                      <Label>字符数量</Label>
                      <Input
                        type="number"
                        min="1"
                        value={costPricingData.costCharacterUnit}
                        onChange={(e) => setCostPricingData({ ...costPricingData, costCharacterUnit: parseInt(e.target.value) || 1000 })}
                        placeholder="1000"
                      />
                    </div>
                  </div>
                  <p className="text-xs text-gray-500">
                    成本价格：每 {costPricingData.costCharacterUnit} 个字符 ${costPricingData.costCharacterPrice} 美元
                  </p>
                </div>
              )}

              {costPricingData.costPricingType === "AUDIO" && (
                <div>
                  <Label>音频价格 (per second)</Label>
                  <Input
                    type="number"
                    step="0.000001"
                    value={costPricingData.costAudioPricePerSecond}
                    onChange={(e) => setCostPricingData({ ...costPricingData, costAudioPricePerSecond: parseFloat(e.target.value) || 0 })}
                    placeholder="0.05"
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sale" className="space-y-6">
          {/* 销售定价设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>销售定价设置</span>
              </CardTitle>
              <CardDescription>配置面向用户的销售价格</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>定价策略名称</Label>
                  <Input
                    value={salePricingData.name}
                    onChange={(e) => setSalePricingData({ ...salePricingData, name: e.target.value })}
                    placeholder="标准定价"
                  />
                </div>
                <div>
                  <Label>利润倍数</Label>
                  <div className="flex space-x-2">
                    <Input
                      type="number"
                      step="0.1"
                      min="1"
                      value={salePricingData.multiplier}
                      onChange={(e) => {
                        const value = e.target.value;
                        setSalePricingData({ 
                          ...salePricingData, 
                          multiplier: value === '' ? 1 : (parseFloat(value) || 1)
                        });
                      }}
                      placeholder="1.5"
                    />
                    <Button type="button" onClick={calculateSalePrice} variant="outline">
                      <Calculator className="h-4 w-4 mr-2" />
                      计算
                    </Button>
                  </div>
                </div>
              </div>

              <div>
                <Label>策略描述</Label>
                <Textarea
                  value={salePricingData.description}
                  onChange={(e) => setSalePricingData({ ...salePricingData, description: e.target.value })}
                  placeholder="描述此定价策略..."
                  rows={2}
                />
              </div>

              {/* 显示计算后的销售价格 */}
              {costPricingData.costPricingType === "TOKEN" && (
                <div className="space-y-3 p-4 bg-muted rounded-lg">
                  <h4 className="font-medium">预计销售价格</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex justify-between">
                      <span>输入Token:</span>
                      <Badge variant="outline">
                        ${(costPricingData.costInputTokenPrice * salePricingData.multiplier).toFixed(6)}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>输出Token:</span>
                      <Badge variant="outline">
                        ${(costPricingData.costOutputTokenPrice * salePricingData.multiplier).toFixed(6)}
                      </Badge>
                    </div>
                  </div>
                </div>
              )}

              {costPricingData.costPricingType === "CHARACTER" && (
                <div className="space-y-3 p-4 bg-muted rounded-lg">
                  <h4 className="font-medium">预计销售价格</h4>
                  <div className="flex justify-between text-sm">
                    <span>字符价格 (每{salePricingData.characterUnit}字符):</span>
                    <Badge variant="outline">
                      ${(costPricingData.costCharacterPrice * salePricingData.multiplier).toFixed(6)}
                    </Badge>
                  </div>
                  <div className="text-xs text-gray-500">
                    单字符价格: ${((costPricingData.costCharacterPrice * salePricingData.multiplier) / salePricingData.characterUnit).toFixed(8)}
                  </div>
                </div>
              )}

              {costPricingData.costPricingType === "AUDIO" && (
                <div className="space-y-3 p-4 bg-muted rounded-lg">
                  <h4 className="font-medium">预计销售价格</h4>
                  <div className="flex justify-between text-sm">
                    <span>音频价格/秒:</span>
                    <Badge variant="outline">
                      ${(costPricingData.costAudioPricePerSecond * salePricingData.multiplier).toFixed(6)}
                    </Badge>
                  </div>
                </div>
              )}

              {costPricingData.costPricingType === "REQUEST" && (
                <div className="space-y-3 p-4 bg-muted rounded-lg">
                  <h4 className="font-medium">预计销售价格</h4>
                  <div className="flex justify-between text-sm">
                    <span>请求价格:</span>
                    <Badge variant="outline">
                      ${(costPricingData.costRequestPrice * salePricingData.multiplier).toFixed(6)}
                    </Badge>
                  </div>
                </div>
              )}

              <div className="space-y-4">
                {!model && (
                  <div className="flex items-center space-x-2 p-4 bg-blue-50 rounded-lg">
                    <Switch
                      checked={createSalePricing}
                      onCheckedChange={setCreateSalePricing}
                    />
                    <Label>创建模型时同时创建默认销售定价策略</Label>
                  </div>
                )}
                
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={salePricingData.isActive}
                      onCheckedChange={(checked) => setSalePricingData({ ...salePricingData, isActive: checked })}
                    />
                    <Label>启用此定价策略</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={salePricingData.isDefault}
                      onCheckedChange={(checked) => setSalePricingData({ ...salePricingData, isDefault: checked })}
                    />
                    <Label>设为默认定价</Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end space-x-2">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            取消
          </Button>
        )}
        <Button 
          type="submit" 
          disabled={createMutation.isPending || updateMutation.isPending}
        >
          {createMutation.isPending || updateMutation.isPending ? "保存中..." : model ? "更新模型" : "创建模型"}
        </Button>
      </div>
    </form>
  );
}