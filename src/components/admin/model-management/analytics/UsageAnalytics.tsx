"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign, 
  Cpu, 
  RefreshCw,
  Calendar,
  Users,
  Activity
} from "lucide-react";
import { api } from "~/trpc/react";

export function UsageAnalytics() {
  const [selectedDays, setSelectedDays] = useState<number>(7);
  const [selectedModule, setSelectedModule] = useState<string>("all");
  
  const { data: modelStats, isLoading: modelStatsLoading, refetch: refetchModelStats } = 
    api.model.getStats.useQuery({ days: selectedDays });
  
  // 暂时禁用模块统计，因为API不存在
  const moduleStats = null;
  const moduleStatsLoading = false;

  const isLoading = modelStatsLoading || moduleStatsLoading;

  const modules = [
    { value: "TTS_DEFAULT", label: "默认TTS" },
    { value: "TTS_HIGH_QUALITY", label: "高质量TTS" },
    { value: "CHAT_DEFAULT", label: "默认对话" },
    { value: "CHAT_ADVANCED", label: "高级对话" },
    { value: "IMAGE_GENERATION", label: "图像生成" },
    { value: "TEXT_ANALYSIS", label: "文本分析" },
    { value: "TRANSLATION", label: "翻译服务" },
    { value: "VOICE_ROLE_MANAGEMENT", label: "语音角色管理" },
  ];

  const dayOptions = [
    { value: 1, label: "今天" },
    { value: 7, label: "最近7天" },
    { value: 30, label: "最近30天" },
    { value: 90, label: "最近90天" },
  ];

  // 计算总体统计
  const totalStats = modelStats?.reduce((acc, stat) => ({
    requests: acc.requests + (stat._count.id || 0),
    inputTokens: acc.inputTokens + (stat._sum.inputTokens || 0),
    outputTokens: acc.outputTokens + (stat._sum.outputTokens || 0),
    totalTokens: acc.totalTokens + (stat._sum.totalTokens || 0),
    cost: acc.cost + (stat._sum.costUsd || 0),
    credits: acc.credits + (stat._sum.creditsDeducted || 0),
  }), {
    requests: 0,
    inputTokens: 0,
    outputTokens: 0,
    totalTokens: 0,
    cost: 0,
    credits: 0,
  }) || {
    requests: 0,
    inputTokens: 0,
    outputTokens: 0,
    totalTokens: 0,
    cost: 0,
    credits: 0,
  };

  return (
    <div className="space-y-6">
      {/* 操作栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">使用分析</h2>
          <p className="text-muted-foreground">查看模型使用统计和性能指标</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedDays.toString()} onValueChange={(v) => setSelectedDays(parseInt(v))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {dayOptions.map((option) => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={selectedModule} onValueChange={setSelectedModule}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="选择模块" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有模块</SelectItem>
              {modules.map((module) => (
                <SelectItem key={module.value} value={module.value}>
                  {module.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={() => refetchModelStats()} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
            刷新
          </Button>
        </div>
      </div>

      {/* 总体统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总请求数</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStats.requests.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              最近 {selectedDays} 天
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总Token数</CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStats.totalTokens.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              输入: {totalStats.inputTokens.toLocaleString()} | 输出: {totalStats.outputTokens.toLocaleString()}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总成本</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalStats.cost.toFixed(4)}</div>
            <p className="text-xs text-muted-foreground">
              USD 计费
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">消耗积分</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStats.credits.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              用户积分消耗
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 模块统计 */}
      {selectedModule !== "all" && moduleStats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>{modules.find(m => m.value === selectedModule)?.label} 统计</span>
            </CardTitle>
            <CardDescription>
              最近 {selectedDays} 天的模块使用情况
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">配置数量</div>
                <div className="text-2xl font-bold">-</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">请求次数</div>
                <div className="text-2xl font-bold">-</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Token消耗</div>
                <div className="text-2xl font-bold">-</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">总成本</div>
                <div className="text-2xl font-bold">-</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 模型使用排行 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>模型使用排行</span>
          </CardTitle>
          <CardDescription>
            按使用量排序的模型统计
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : !modelStats || modelStats.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>暂无使用数据</p>
            </div>
          ) : (
            <div className="space-y-4">
              {modelStats
                .sort((a, b) => (b._count.id || 0) - (a._count.id || 0))
                .slice(0, 10)
                .map((stat, index) => (
                  <div key={stat.modelId} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center justify-center w-8 h-8 bg-muted rounded-full text-sm font-medium">
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium">
                          {stat.model?.displayName || stat.model?.name || "未知模型"}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {stat.model?.provider?.name}
                        </div>
                      </div>
                    </div>
                    <div className="text-right space-y-1">
                      <div className="flex items-center space-x-4 text-sm">
                        <span className="text-muted-foreground">请求:</span>
                        <span className="font-medium">{(stat._count.id || 0).toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm">
                        <span className="text-muted-foreground">Token:</span>
                        <span className="font-medium">{(stat._sum.totalTokens || 0).toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm">
                        <span className="text-muted-foreground">成本:</span>
                        <span className="font-medium">${(stat._sum.costUsd || 0).toFixed(4)}</span>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}