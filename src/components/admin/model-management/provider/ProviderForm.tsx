"use client";

import { useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Switch } from "~/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { Server } from "lucide-react";

interface ProviderFormProps {
  provider?: any;
  onSuccess?: () => void;
}

export function ProviderForm({ provider, onSuccess }: ProviderFormProps) {
  const [formData, setFormData] = useState({
    name: provider?.name || "",
    slug: provider?.slug || "",
    description: provider?.description || "",
    apiKey: provider?.apiKey || "",
    isActive: provider?.isActive ?? true,
    config: provider?.config || {},
  });

  const createMutation = api.provider.create.useMutation({
    onSuccess: () => {
      toast.success("提供商创建成功");
      onSuccess?.();
    },
    onError: (error) => {
      toast.error(error.message || "创建失败");
    },
  });

  const updateMutation = api.provider.update.useMutation({
    onSuccess: () => {
      toast.success("提供商更新成功");
      onSuccess?.();
    },
    onError: (error) => {
      toast.error(error.message || "更新失败");
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (provider) {
      updateMutation.mutate({ id: provider.id, ...formData });
    } else {
      createMutation.mutate(formData);
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Server className="h-5 w-5" />
            <span>提供商信息</span>
          </CardTitle>
          <CardDescription>
            {provider ? "编辑提供商的基本信息" : "添加新的AI模型提供商"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">提供商名称 *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="例如：Google"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="slug">标识符 *</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => setFormData({ ...formData, slug: e.target.value.toLowerCase().replace(/[^a-z0-9-_]/g, '') })}
                placeholder="例如：google"
                pattern="^[a-z0-9-_]+$"
                disabled={!!provider}
                required
              />
              <p className="text-xs text-muted-foreground">
                标识符只能包含小写字母、数字、连字符和下划线
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">描述</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="提供商的简要描述"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="apiKey">API 密钥</Label>
            <Input
              id="apiKey"
              type="password"
              value={formData.apiKey}
              onChange={(e) => setFormData({ ...formData, apiKey: e.target.value })}
              placeholder="输入API密钥（可选）"
            />
            <p className="text-xs text-muted-foreground">
              API密钥将被安全存储，用于与提供商的API进行通信
            </p>
          </div>

          {/* Minimax特定配置 */}
          {formData.slug === "minimax" && (
            <div className="space-y-2">
              <Label htmlFor="groupId">Group ID</Label>
              <Input
                id="groupId"
                value={(formData.config as any)?.groupId || ""}
                onChange={(e) => setFormData({
                  ...formData,
                  config: {
                    ...formData.config,
                    groupId: e.target.value
                  }
                })}
                placeholder="输入Minimax Group ID"
              />
              <p className="text-xs text-muted-foreground">
                Minimax API需要Group ID进行认证
              </p>
            </div>
          )}

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
            />
            <Label htmlFor="isActive">启用提供商</Label>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end space-x-2">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "保存中..." : provider ? "更新提供商" : "创建提供商"}
        </Button>
      </div>
    </form>
  );
}