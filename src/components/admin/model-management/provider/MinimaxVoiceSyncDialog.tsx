"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { Checkbox } from "~/components/ui/checkbox";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { ScrollArea } from "~/components/ui/scroll-area";
import { Separator } from "~/components/ui/separator";
import { 
  Download, 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  Plus, 
  Edit,
  User,
  Globe,
  Mic
} from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";

interface MinimaxVoiceSyncDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function MinimaxVoiceSyncDialog({ 
  open, 
  onOpenChange, 
  onSuccess 
}: MinimaxVoiceSyncDialogProps) {
  const [step, setStep] = useState<'preview' | 'options' | 'syncing'>('preview');
  const [preserveCustomizations, setPreserveCustomizations] = useState(true);
  const [selectedVoices, setSelectedVoices] = useState<string[]>([]);
  const [syncPlan, setSyncPlan] = useState<any>(null);

  // 预览同步计划
  const previewMutation = api.provider.syncMinimaxVoices.useMutation({
    onSuccess: (result) => {
      if (result.mode === 'preview') {
        setSyncPlan(result.syncPlan);
        setStep('options');
      }
    },
    onError: (error) => {
      toast.error(error.message || "预览失败");
    },
  });

  // 执行同步
  const syncMutation = api.provider.syncMinimaxVoices.useMutation({
    onSuccess: (result) => {
      toast.success(result.message);
      onSuccess?.();
      onOpenChange(false);
      resetDialog();
    },
    onError: (error) => {
      toast.error(error.message || "同步失败");
    },
  });

  const resetDialog = () => {
    setStep('preview');
    setSyncPlan(null);
    setSelectedVoices([]);
    setPreserveCustomizations(true);
  };

  const handlePreview = () => {
    setStep('syncing');
    previewMutation.mutate({ mode: 'preview' });
  };

  const handleSync = () => {
    setStep('syncing');
    syncMutation.mutate({
      mode: 'sync',
      preserveCustomizations,
      selectedVoices: selectedVoices.length > 0 ? selectedVoices : undefined
    });
  };

  const handleClose = () => {
    onOpenChange(false);
    resetDialog();
  };

  const toggleVoiceSelection = (voiceId: string) => {
    setSelectedVoices(prev => 
      prev.includes(voiceId) 
        ? prev.filter(id => id !== voiceId)
        : [...prev, voiceId]
    );
  };

  const selectAllNewVoices = () => {
    if (syncPlan?.newVoices) {
      const newVoiceIds = syncPlan.newVoices.map((v: any) => v.voice_id);
      setSelectedVoices(prev => [...new Set([...prev, ...newVoiceIds])]);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Download className="h-5 w-5" />
            <span>同步Minimax语音角色</span>
          </DialogTitle>
          <DialogDescription>
            从Minimax API获取最新的语音角色并同步到系统中
          </DialogDescription>
        </DialogHeader>

        {step === 'preview' && (
          <div className="space-y-4">
            <div className="text-center py-8">
              <Mic className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">准备同步语音角色</h3>
              <p className="text-muted-foreground mb-6">
                点击预览按钮查看将要同步的语音角色列表
              </p>
              <Button 
                onClick={handlePreview}
                disabled={previewMutation.isPending}
                className="min-w-32"
              >
                {previewMutation.isPending ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    获取中...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    预览同步
                  </>
                )}
              </Button>
            </div>
          </div>
        )}

        {step === 'options' && syncPlan && (
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center">
                    <Plus className="h-4 w-4 mr-1 text-green-600" />
                    新增角色
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {syncPlan.newVoices.length}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center">
                    <Edit className="h-4 w-4 mr-1 text-blue-600" />
                    更新角色
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">
                    {syncPlan.existingVoices.length}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center">
                    <User className="h-4 w-4 mr-1 text-orange-600" />
                    自定义角色
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-orange-600">
                    {syncPlan.customizedVoices.length}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="preserve-customizations"
                  checked={preserveCustomizations}
                  onCheckedChange={(checked) => setPreserveCustomizations(checked === true)}
                />
                <label htmlFor="preserve-customizations" className="text-sm font-medium">
                  保留自定义设置
                </label>
              </div>
              <p className="text-xs text-muted-foreground ml-6">
                启用后，已自定义的角色（如自定义头像、描述等）将保留用户设置，只更新核心语音参数
              </p>
            </div>

            <Tabs defaultValue="new" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="new">新增角色 ({syncPlan.newVoices.length})</TabsTrigger>
                <TabsTrigger value="existing">更新角色 ({syncPlan.existingVoices.length})</TabsTrigger>
                <TabsTrigger value="customized">自定义角色 ({syncPlan.customizedVoices.length})</TabsTrigger>
              </TabsList>

              <TabsContent value="new" className="mt-4">
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {syncPlan.newVoices.length > 0 && (
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={selectAllNewVoices}
                        className="mb-2"
                      >
                        全选新增角色
                      </Button>
                    )}
                    {syncPlan.newVoices.map((voice: any) => (
                      <div key={voice.voice_id} className="flex items-center space-x-3 p-2 border rounded">
                        <Checkbox
                          checked={selectedVoices.includes(voice.voice_id)}
                          onCheckedChange={() => toggleVoiceSelection(voice.voice_id)}
                        />
                        <div className="flex-1">
                          <div className="font-medium">{voice.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {voice.voice_id} • {voice.gender} • {voice.language}
                          </div>
                        </div>
                        <Badge variant="outline" className="text-green-600">新增</Badge>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="existing" className="mt-4">
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {syncPlan.existingVoices.map((voice: any) => (
                      <div key={voice.voice_id} className="flex items-center space-x-3 p-2 border rounded">
                        <CheckCircle className="h-4 w-4 text-blue-600" />
                        <div className="flex-1">
                          <div className="font-medium">{voice.name}</div>
                          <div className="text-sm text-muted-foreground">{voice.voice_id}</div>
                        </div>
                        <Badge variant="outline" className="text-blue-600">更新</Badge>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="customized" className="mt-4">
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {syncPlan.customizedVoices.map((voice: any) => (
                      <div key={voice.voice_id} className="flex items-center space-x-3 p-2 border rounded">
                        <AlertTriangle className="h-4 w-4 text-orange-600" />
                        <div className="flex-1">
                          <div className="font-medium">{voice.currentName}</div>
                          <div className="text-sm text-muted-foreground">
                            {voice.voice_id}
                            {voice.hasAvatar && " • 自定义头像"}
                            {voice.hasCustomDescription && " • 自定义描述"}
                            {voice.languageCount > 1 && ` • ${voice.languageCount}种语言`}
                          </div>
                        </div>
                        <Badge variant="outline" className="text-orange-600">
                          {preserveCustomizations ? "保留" : "覆盖"}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </div>
        )}

        {step === 'syncing' && (
          <div className="text-center py-8">
            <RefreshCw className="h-12 w-12 mx-auto text-blue-600 animate-spin mb-4" />
            <h3 className="text-lg font-medium mb-2">
              {previewMutation.isPending ? "获取语音列表中..." : "同步中..."}
            </h3>
            <p className="text-muted-foreground">
              请稍候，正在处理语音角色数据
            </p>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            取消
          </Button>
          {step === 'options' && (
            <Button 
              onClick={handleSync}
              disabled={syncMutation.isPending}
            >
              {syncMutation.isPending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  同步中...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  开始同步
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
