"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import {
  Plus,
  Edit,
  Trash2,
  RefreshCw,
  Server,
  ExternalLink,
  Settings,
  Download
} from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { ProviderForm } from "./ProviderForm";
import { MinimaxVoiceSyncDialog } from "./MinimaxVoiceSyncDialog";

export function ProviderManagement() {
  const utils = api.useUtils();
  const { data: providers, isLoading, refetch } = api.provider.adminList.useQuery();
  const [selectedProvider, setSelectedProvider] = useState<any>(null);
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);

  const deleteMutation = api.provider.delete.useMutation({
    onSuccess: () => {
      toast.success("提供商删除成功");
      utils.provider.adminList.invalidate();
    },
    onError: (error) => {
      toast.error(error.message || "删除失败");
    },
  });

  // 移除旧的同步mutation，使用新的对话框

  const handleDelete = (provider: any) => {
    if (!confirm(`确认删除提供商 "${provider.name}"？这将删除所有相关模型。`)) {
      return;
    }
    deleteMutation.mutate({ id: provider.id });
  };

  const handleEdit = (provider: any) => {
    setSelectedProvider(provider);
    setIsEditOpen(true);
  };

  const handleSyncVoices = () => {
    setIsSyncDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* 操作栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">提供商管理</h2>
          <p className="text-muted-foreground">管理AI模型提供商和API配置</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => refetch()} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
            刷新
          </Button>
          <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                添加提供商
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>添加新提供商</DialogTitle>
              </DialogHeader>
              <ProviderForm 
                onSuccess={() => {
                  setIsCreateOpen(false);
                  utils.provider.adminList.invalidate();
                }}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 提供商列表 */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : !providers || providers.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Server className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">暂无提供商</h3>
            <p className="text-muted-foreground text-center mb-4">
              开始添加AI模型提供商来管理您的模型资源
            </p>
            <Button onClick={() => setIsCreateOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              添加第一个提供商
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {providers.map((provider) => (
            <Card key={provider.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Server className="h-5 w-5 text-muted-foreground" />
                    <CardTitle className="text-lg">{provider.name}</CardTitle>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Badge variant={provider.isActive ? "default" : "secondary"}>
                      {provider.isActive ? "活跃" : "禁用"}
                    </Badge>
                  </div>
                </div>
                <CardDescription>
                  {provider.description || `${provider.slug} 提供商`}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">标识符</span>
                    <code className="bg-muted px-2 py-1 rounded text-xs">{provider.slug}</code>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">模型数量</span>
                    <span className="font-medium">{provider._count?.models || 0}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(provider)}
                      className="flex-1"
                    >
                      <Edit className="h-3 w-3 mr-1" />
                      编辑
                    </Button>

                    {/* Minimax特殊功能：同步语音 */}
                    {provider.slug === 'minimax' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleSyncVoices}
                        className="text-blue-600 hover:text-blue-700"
                        title="同步Minimax语音角色"
                      >
                        <Download className="h-3 w-3" />
                      </Button>
                    )}

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(provider)}
                      disabled={(provider._count?.models || 0) > 0 || deleteMutation.isPending}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* 编辑对话框 */}
      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑提供商</DialogTitle>
          </DialogHeader>
          {selectedProvider && (
            <ProviderForm 
              provider={selectedProvider}
              onSuccess={() => {
                setIsEditOpen(false);
                setSelectedProvider(null);
                utils.provider.adminList.invalidate();
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Minimax语音同步对话框 */}
      <MinimaxVoiceSyncDialog
        open={isSyncDialogOpen}
        onOpenChange={setIsSyncDialogOpen}
        onSuccess={() => {
          utils.voiceRole.getVoiceRoles.invalidate();
        }}
      />
    </div>
  );
}