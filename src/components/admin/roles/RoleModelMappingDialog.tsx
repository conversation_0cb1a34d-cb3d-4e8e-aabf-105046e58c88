"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { Badge } from "~/components/ui/badge";
import { Checkbox } from "~/components/ui/checkbox";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Loader2, <PERSON>, Unlink, Star } from "lucide-react";
import { toast } from "sonner";
import { api } from "~/trpc/react";

interface RoleModelMappingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  roleId: string;
  roleName: string;
}

export function RoleModelMappingDialog({ 
  open, 
  onOpenChange, 
  roleId, 
  roleName 
}: RoleModelMappingDialogProps) {
  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [defaultModelId, setDefaultModelId] = useState<string>("");

  const utils = api.useUtils();

  // 暂时禁用TTS模型获取
  const allModels = { models: [] };
  const isLoadingModels = false;

  // 获取当前角色的模型关联
  const { data: currentMappings, isLoading: isLoadingMappings } = api.roleModelMapping.getModelsByRoleId.useQuery(
    { roleId },
    { enabled: !!roleId }
  );

  // 批量创建关联的mutation
  const createBatchMappings = api.roleModelMapping.createBatchMappings.useMutation({
    onSuccess: () => {
      toast.success("模型关联更新成功");
      // 刷新相关数据
      utils.roleModelMapping.getModelsByRoleId.invalidate({ roleId });
      utils.voiceRole.getVoiceRoles.invalidate(); // 刷新角色列表
      // utils.tts.getTTSModelsWithPricing.invalidate(); // 暂时禁用
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error(error.message || "更新失败");
    },
  });

  // 初始化选中的模型
  useEffect(() => {
    if (currentMappings?.models) {
      const modelIds = currentMappings.models.map(m => m.id);
      const defaultModel = currentMappings.models.find(m => m.isDefault);
      
      setSelectedModels(modelIds);
      setDefaultModelId(defaultModel?.id || "");
    }
  }, [currentMappings]);

  const handleModelToggle = (modelId: string, checked: boolean) => {
    if (checked) {
      setSelectedModels(prev => [...prev, modelId]);
      // 如果是第一个选中的模型，设为默认
      if (selectedModels.length === 0) {
        setDefaultModelId(modelId);
      }
    } else {
      setSelectedModels(prev => prev.filter(id => id !== modelId));
      // 如果取消选中的是默认模型，清空默认设置
      if (modelId === defaultModelId) {
        setDefaultModelId("");
      }
    }
  };

  const handleSave = () => {
    if (selectedModels.length === 0) {
      toast.error("请至少选择一个模型");
      return;
    }

    if (!defaultModelId) {
      toast.error("请设置一个默认模型");
      return;
    }

    createBatchMappings.mutate({
      roleId,
      modelIds: selectedModels,
      defaultModelId
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader className="bg-gradient-to-r from-primary/10 to-primary/5 -mx-6 -mt-6 px-6 py-4 border-b">
          <DialogTitle className="flex items-center gap-2">
            <Link className="h-5 w-5 text-primary" />
            为角色 "{roleName}" 关联模型
          </DialogTitle>
          <DialogDescription className="mt-2">
            选择该语音角色支持的TTS模型，并设置默认模型。用户选择此角色时将优先使用默认模型。
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 px-1 py-2">
          {/* 当前关联状态 */}
          {currentMappings?.models && currentMappings.models.length > 0 && (
            <Card className="bg-primary/5 border-primary/20">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">当前关联状态</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex flex-wrap gap-2">
                  {currentMappings.models.map((model) => (
                    <Badge
                      key={model.id}
                      variant={model.isDefault ? "default" : "secondary"}
                      className="flex items-center gap-1"
                    >
                      {model.isDefault && <Star className="h-3 w-3" />}
                      {model.displayName || model.name}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 模型选择 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">选择支持的模型</Label>
              <div className="text-sm text-muted-foreground bg-muted px-3 py-1 rounded-full">
                已选择 {selectedModels.length} 个模型
              </div>
            </div>

            {isLoadingModels ? (
              <div className="flex justify-center p-8">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {allModels?.models?.map((model: any) => {
                  const isSelected = selectedModels.includes(model.id);
                  const isDefault = defaultModelId === model.id;

                  return (
                    <Card
                      key={model.id}
                      className={`cursor-pointer transition-all border-2 ${
                        isSelected
                          ? "border-primary bg-primary/5 shadow-md"
                          : "border-border hover:border-primary/50 hover:bg-muted/50"
                      }`}
                      onClick={() => handleModelToggle(model.id, !isSelected)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                          <Checkbox
                            checked={isSelected}
                            onChange={() => {}} // 由Card的onClick处理
                            className="mt-1"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">{model.displayName || model.name}</h4>
                              {isDefault && (
                                <Badge variant="secondary" className="text-xs">
                                  <Star className="h-3 w-3 mr-1 fill-current" />
                                  默认
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground mt-1">{model.description}</p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="outline" className="text-xs">
                                {model.provider.name}
                              </Badge>
                              {model.customPricings?.[0] && (
                                <Badge variant="outline" className="text-xs">
                                  ${model.customPricings[0].characterPrice}/{model.customPricings[0].characterUnit || 1000}字符
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </div>

          {/* 默认模型选择 */}
          {selectedModels.length > 0 && (
            <Card className="bg-secondary/50 border-secondary">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <Label className="text-base font-medium flex items-center gap-2">
                    <Star className="h-4 w-4 fill-current" />
                    设置默认模型
                  </Label>
                  <Select value={defaultModelId} onValueChange={setDefaultModelId}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择默认模型" />
                    </SelectTrigger>
                    <SelectContent>
                      {selectedModels.map((modelId) => {
                        const model = allModels?.models?.find((m: any) => m.id === modelId);
                        return (
                          <SelectItem key={modelId} value={modelId}>
                            <div className="flex items-center gap-2">
                              <Star className="h-4 w-4 fill-current" />
                              {(model as any)?.displayName || (model as any)?.name}
                            </div>
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground bg-muted p-2 rounded">
                    💡 用户选择此角色时将自动使用默认模型，也可以手动切换到其他关联模型。
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter className="bg-muted/50 px-6 py-4 -mx-6 -mb-6 mt-6 rounded-b-lg">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            取消
          </Button>
          <Button
            onClick={handleSave}
            disabled={createBatchMappings.isPending || selectedModels.length === 0}
          >
            {createBatchMappings.isPending && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
            {createBatchMappings.isPending ? "保存中..." : "保存关联"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
