"use client";

import { useState, useMemo } from "react";
import Link from "next/link";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { PencilIcon, TrashIcon, ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { Volume2, Languages, Filter, BarChart3, Search, SortAsc, SortDesc, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";
import { api } from "~/trpc/react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "~/components/ui/collapsible";

interface TtsRole {
  id: string;
  slug: string;
  name: string;
  nameEn: string | null;
  nameZh: string | null;
  description: string | null;
  descriptionEn: string | null;
  descriptionZh: string | null;
  styles: string[];
  stylesEn: string[] | null;
  stylesZh: string[] | null;
  voiceName: string;
  isActive: boolean;
  avatarUrl: string | null;
  genderZh: string | null;
  genderEn: string | null;
  createdAt: Date;
  updatedAt: Date;
  languageSupports?: {
    id: string;
    languageCode: string;
    quality: string;
    isDefault: boolean;
    sampleText: string | null;
    sampleUrl: string | null;
    language: {
      code: string;
      name: string;
      nativeName: string;
      region: string;
    };
  }[];
  modelMappings?: {
    id: string;
    isDefault: boolean;
    priority: number;
    model: {
      id: string;
      name: string;
      displayName: string | null;
      provider: {
        id: string;
        name: string;
        slug: string;
      };
    };
  }[];
}

interface EnhancedRoleListProps {
  onEditRole: (role: TtsRole) => void;
}

export default function EnhancedRoleList({ onEditRole }: EnhancedRoleListProps) {
  // 筛选状态
  const [search, setSearch] = useState("");
  const [gender, setGender] = useState<string>("");
  const [language, setLanguage] = useState<string>("");
  const [region, setRegion] = useState<string>("");
  const [provider, setProvider] = useState<string>("");
  const [style, setStyle] = useState<string>("");
  const [isActive, setIsActive] = useState<string>("");
  
  // 分页状态
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);
  
  // 排序状态
  const [sortBy, setSortBy] = useState<"name" | "createdAt" | "updatedAt">("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  
  // UI状态
  const [showFilters, setShowFilters] = useState(false);

  // tRPC 查询
  const utils = api.useUtils();
  
  // 获取角色列表
  const { data: rolesData, isLoading: rolesLoading } = api.voiceRole.getVoiceRoles.useQuery({
    search: search || undefined,
    gender: gender ? (gender as "Male" | "Female") : undefined,
    language: language || undefined,
    region: region || undefined,
    provider: provider || undefined,
    style: style || undefined,
    isActive: isActive ? isActive === "true" : undefined,
    page,
    limit,
    sortBy,
    sortOrder,
    includeModelMappings: true,
  });

  // 获取统计信息
  const { data: voiceRoleStats } = api.voiceRole.getVoiceRoleStats.useQuery();
  
  // 获取所有支持的语言
  const { data: languages } = api.language.getAll.useQuery();
  
  // 获取语言地区
  const { data: regions } = api.language.getRegions.useQuery();

  // 获取提供商列表
  const { data: providers } = api.provider.getAll.useQuery();

  // 计算可用的风格选项 - 暂时禁用
  const availableStyles: any[] = [];

  // 删除角色mutation
  const deleteMutation = api.voiceRole.deleteVoiceRole.useMutation({
    onSuccess: () => {
      toast.success('语音角色删除成功');
      utils.voiceRole.getVoiceRoles.invalidate();
      utils.voiceRole.getVoiceRoleStats.invalidate();
    },
    onError: (error: any) => {
      toast.error(error.message || '删除失败');
    },
  });

  const handleDelete = (id: string) => {
    if (confirm('确定要删除这个语音角色吗？此操作无法撤销。')) {
      deleteMutation.mutate({ id });
    }
  };

  const handleSort = (field: "name" | "createdAt" | "updatedAt") => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("desc");
    }
    setPage(1); // 重置到第一页
  };

  const clearFilters = () => {
    setSearch("");
    setGender("");
    setLanguage("");
    setRegion("");
    setProvider("");
    setStyle("");
    setIsActive("");
    setPage(1);
  };

  const hasActiveFilters = search || gender || language || region || provider || style || isActive;

  if (rolesLoading) {
    return <div className="flex justify-center p-8">加载中...</div>;
  }

  const roles = rolesData?.roles || [];
  const pagination = rolesData?.pagination;

  return (
    <>
      {/* 统计信息卡片 - 暂时禁用 */}

      {/* 筛选器 */}
      <Card className="mb-6">
        <Collapsible open={showFilters} onOpenChange={setShowFilters}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Filter className="h-4 w-4 mr-2" />
                  筛选器
                  {hasActiveFilters && (
                    <Badge variant="secondary" className="ml-2">
                      {[search, gender, language, region, provider, style, isActive].filter(Boolean).length} 个筛选条件
                    </Badge>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  {hasActiveFilters && (
                    <Button variant="ghost" size="sm" onClick={clearFilters}>
                      清除筛选
                    </Button>
                  )}
                </div>
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {/* 搜索 */}
                <div>
                  <Label htmlFor="search">搜索</Label>
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="search"
                      placeholder="搜索角色名称、描述..."
                      value={search}
                      onChange={(e) => {
                        setSearch(e.target.value);
                        setPage(1);
                      }}
                      className="pl-8"
                    />
                  </div>
                </div>
                
                {/* 性别筛选 */}
                <div>
                  <Label>性别</Label>
                  <Select value={gender} onValueChange={(value) => { setGender(value); setPage(1); }}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择性别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">所有性别</SelectItem>
                      <SelectItem value="Male">男性</SelectItem>
                      <SelectItem value="Female">女性</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 语言筛选 */}
                <div>
                  <Label>语言</Label>
                  <Select value={language} onValueChange={(value) => { setLanguage(value); setPage(1); }}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择语言" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">所有语言</SelectItem>
                      {languages?.map((lang: any) => (
                        <SelectItem key={lang.code} value={lang.code}>
                          {lang.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 地区筛选 */}
                <div>
                  <Label>地区</Label>
                  <Select value={region} onValueChange={(value) => { setRegion(value); setPage(1); }}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择地区" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">所有地区</SelectItem>
                      {regions?.map((region: any) => (
                        <SelectItem key={region.region} value={region.region}>
                          {region.region}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 提供商筛选 */}
                <div>
                  <Label>提供商</Label>
                  <Select value={provider} onValueChange={(value) => { setProvider(value); setPage(1); }}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择提供商" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">所有提供商</SelectItem>
                      {providers?.map((provider: any) => (
                        <SelectItem key={provider.slug} value={provider.slug}>
                          {provider.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 风格筛选 */}
                <div>
                  <Label>风格</Label>
                  <Select value={style} onValueChange={(value) => { setStyle(value); setPage(1); }}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择风格" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">所有风格</SelectItem>
                      {availableStyles.map((style: any) => (
                        <SelectItem key={style.style} value={style.style}>
                          {style.style} ({style.count})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 状态筛选 */}
                <div>
                  <Label>状态</Label>
                  <Select value={isActive} onValueChange={(value) => { setIsActive(value); setPage(1); }}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">所有状态</SelectItem>
                      <SelectItem value="true">活跃</SelectItem>
                      <SelectItem value="false">非活跃</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 每页显示数量 */}
                <div>
                  <Label>每页显示</Label>
                  <Select value={limit.toString()} onValueChange={(value) => { setLimit(Number(value)); setPage(1); }}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10 条</SelectItem>
                      <SelectItem value="20">20 条</SelectItem>
                      <SelectItem value="50">50 条</SelectItem>
                      <SelectItem value="100">100 条</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* 角色列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>
              角色列表
              {pagination && (
                <span className="text-sm font-normal text-muted-foreground ml-2">
                  ({pagination.total} 个角色，第 {pagination.page} / {pagination.totalPages} 页)
                </span>
              )}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort("name")}
                      className="h-auto p-0 font-semibold"
                    >
                      角色信息
                      {sortBy === "name" && (
                        sortOrder === "asc" ? <SortAsc className="ml-1 h-3 w-3" /> : <SortDesc className="ml-1 h-3 w-3" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead>语音配置</TableHead>
                  <TableHead>语言支持</TableHead>
                  <TableHead>模型映射</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort("createdAt")}
                      className="h-auto p-0 font-semibold"
                    >
                      创建时间
                      {sortBy === "createdAt" && (
                        sortOrder === "asc" ? <SortAsc className="ml-1 h-3 w-3" /> : <SortDesc className="ml-1 h-3 w-3" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {roles.map((role: any) => (
                  <TableRow key={role.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <div className="font-medium">{role.name}</div>
                          {role.genderEn && (
                            <Badge variant={role.genderEn === 'Male' ? 'default' : 'secondary'} className="text-xs">
                              {role.genderEn === 'Male' ? '男' : '女'}
                            </Badge>
                          )}
                        </div>
                        {(role.nameEn || role.nameZh) && (
                          <div className="text-xs text-muted-foreground">
                            {role.nameEn && <div>EN: {role.nameEn}</div>}
                            {role.nameZh && <div>ZH: {role.nameZh}</div>}
                          </div>
                        )}
                        <div className="text-xs text-muted-foreground">
                          ID: {role.slug}
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-sm font-mono">{role.voiceName}</div>
                        {role.avatarUrl && (
                          <div className="text-xs text-muted-foreground">有头像</div>
                        )}
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1">
                        {role.languageSupports && role.languageSupports.length > 0 ? (
                          <div className="flex flex-wrap gap-1">
                            {role.languageSupports.slice(0, 3).map((support: any) => (
                              <Badge
                                key={support.id}
                                variant={support.isDefault ? "default" : "secondary"}
                                className="text-xs"
                              >
                                {support.language.code}
                              </Badge>
                            ))}
                            {role.languageSupports.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{role.languageSupports.length - 3}
                              </Badge>
                            )}
                          </div>
                        ) : (
                          <span className="text-xs text-muted-foreground">无语言支持</span>
                        )}
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1">
                        {role.modelMappings && role.modelMappings.length > 0 ? (
                          <div className="space-y-1">
                            {role.modelMappings.slice(0, 2).map((mapping: any) => (
                              <div key={mapping.id} className="flex items-center gap-1">
                                <Badge
                                  variant={mapping.isDefault ? "default" : "outline"}
                                  className="text-xs"
                                >
                                  {mapping.model.provider.name}
                                </Badge>
                                <span className="text-xs text-muted-foreground">
                                  {mapping.model.displayName || mapping.model.name}
                                </span>
                              </div>
                            ))}
                            {role.modelMappings.length > 2 && (
                              <div className="text-xs text-muted-foreground">
                                +{role.modelMappings.length - 2} 个模型
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-xs text-muted-foreground">无模型映射</span>
                        )}
                      </div>
                    </TableCell>

                    <TableCell>
                      <Badge variant={role.isActive ? "default" : "secondary"}>
                        {role.isActive ? "活跃" : "非活跃"}
                      </Badge>
                    </TableCell>

                    <TableCell>
                      <div className="text-xs text-muted-foreground">
                        {new Date(role.createdAt).toLocaleDateString('zh-CN')}
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onEditRole(role)}
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>

                        <Button
                          size="sm"
                          variant="ghost"
                          asChild
                        >
                          <Link href={`/admin/roles/${role.id}/voice-samples`}>
                            <Volume2 className="h-4 w-4" />
                          </Link>
                        </Button>

                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDelete(role.id)}
                          disabled={deleteMutation.isPending}
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {roles.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">暂无符合条件的角色</p>
            </div>
          )}

          {/* 分页 */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-muted-foreground">
                显示第 {(pagination.page - 1) * pagination.limit + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
                共 {pagination.total} 条记录
              </div>

              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page - 1)}
                  disabled={page <= 1}
                >
                  <ChevronLeftIcon className="h-4 w-4" />
                  上一页
                </Button>

                <div className="flex items-center space-x-1">
                  {/* 页码按钮 */}
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    let pageNum;
                    if (pagination.totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (page <= 3) {
                      pageNum = i + 1;
                    } else if (page >= pagination.totalPages - 2) {
                      pageNum = pagination.totalPages - 4 + i;
                    } else {
                      pageNum = page - 2 + i;
                    }

                    return (
                      <Button
                        key={pageNum}
                        variant={page === pageNum ? "default" : "outline"}
                        size="sm"
                        onClick={() => setPage(pageNum)}
                        className="w-8 h-8 p-0"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={page >= pagination.totalPages}
                >
                  下一页
                  <ChevronRightIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
}
