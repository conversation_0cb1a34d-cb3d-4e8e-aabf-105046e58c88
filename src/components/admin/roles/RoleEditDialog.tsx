"use client";

import { useState, useEffect } from "react";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { Checkbox } from "~/components/ui/checkbox";
import AvatarUpload from "~/components/ui/avatar-upload";
import { toast } from "sonner";
import { api } from "~/trpc/react";

interface TtsRole {
  id: string;
  slug: string;
  name: string;
  nameEn: string | null;
  nameZh: string | null;
  description: string | null;
  descriptionEn: string | null;
  descriptionZh: string | null;
  styles: string[];
  stylesEn: string[] | null;
  stylesZh: string[] | null;
  voiceName: string;
  isActive: boolean;
  avatarUrl: string | null;
  genderZh: string | null;
  genderEn: string | null;
  languageSupports?: {
    languageCode: string;
    language: {
      code: string;
      name: string;
      nativeName: string;
      region: string;
    };
  }[];
}

interface CreateRoleData {
  slug: string;
  name: string;
  nameEn?: string;
  nameZh?: string;
  description: string;
  descriptionEn?: string;
  descriptionZh?: string;
  selectedLanguages: string[];
  styles: string[];
  stylesEn: string[];
  stylesZh: string[];
  voiceName: string;
  avatarUrl?: string;
  isActive: boolean;
  genderZh: string;
  genderEn: string;
  // 新增头像上传相关字段
  avatarData?: string; // base64图片数据
}

interface RoleEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingRole?: TtsRole | null;
  mode: 'create' | 'edit';
}

export default function RoleEditDialog({ isOpen, onClose, editingRole, mode }: RoleEditDialogProps) {
  const [formData, setFormData] = useState<CreateRoleData>({
    slug: "",
    name: "",
    nameEn: "",
    nameZh: "",
    description: "",
    descriptionEn: "",
    descriptionZh: "",
    selectedLanguages: ["zh-CN"],
    styles: ["neutral"],
    stylesEn: ["neutral"],
    stylesZh: ["中性"],
    voiceName: "",
    avatarUrl: "",
    isActive: true,
    genderZh: "未设置",
    genderEn: "Not set",
    avatarData: undefined, // 头像上传数据
  });

  // tRPC 查询
  const utils = api.useUtils();
  
  // 获取所有支持的语言
  const { data: languages, isLoading: languagesLoading } = api.language.getAll.useQuery();

  // 创建角色mutation
  const createMutation = api.tts.create.useMutation({
    onSuccess: () => {
      toast.success('语音角色创建成功');
      onClose();
      resetForm();
      utils.tts.getAll.invalidate();
    },
    onError: (error: any) => {
      toast.error(error.message || '创建失败');
    },
  });

  // 更新角色mutation
  const updateMutation = api.tts.update.useMutation({
    onSuccess: () => {
      toast.success('语音角色更新成功');
      onClose();
      resetForm();
      utils.tts.getAll.invalidate();
    },
    onError: (error: any) => {
      toast.error(error.message || '更新失败');
    },
  });

  // 当编辑角色变化时，更新表单数据
  useEffect(() => {
    if (mode === 'edit' && editingRole) {
      setFormData({
        slug: editingRole.slug,
        name: editingRole.name,
        nameEn: editingRole.nameEn || "",
        nameZh: editingRole.nameZh || "",
        description: editingRole.description || "",
        descriptionEn: editingRole.descriptionEn || "",
        descriptionZh: editingRole.descriptionZh || "",
        selectedLanguages: editingRole.languageSupports?.map(s => s.languageCode) || [],
        styles: editingRole.styles,
        stylesEn: editingRole.stylesEn || [],
        stylesZh: editingRole.stylesZh || [],
        voiceName: editingRole.voiceName,
        avatarUrl: editingRole.avatarUrl || "",
        isActive: editingRole.isActive,
        genderZh: editingRole.genderZh || "未设置",
        genderEn: editingRole.genderEn || "Not set",
        avatarData: undefined, // 编辑时不设置avatarData，使用现有的avatarUrl
      });
    } else if (mode === 'create') {
      resetForm();
    }
  }, [mode, editingRole]);

  const resetForm = () => {
    setFormData({
      slug: "",
      name: "",
      nameEn: "",
      nameZh: "",
      description: "",
      descriptionEn: "",
      descriptionZh: "",
      selectedLanguages: ["zh-CN"],
      styles: ["neutral"],
      stylesEn: ["neutral"],
      stylesZh: ["中性"],
      voiceName: "",
      avatarUrl: "",
      isActive: true,
      genderZh: "未设置",
      genderEn: "Not set",
      avatarData: undefined,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 转换为后端期望的格式
    const submitData = {
      ...formData,
      languages: formData.selectedLanguages, // 兼容旧API
    };
    
    if (mode === 'edit' && editingRole) {
      updateMutation.mutate({ id: editingRole.id, ...submitData });
    } else {
      createMutation.mutate(submitData);
    }
  };

  const handleLanguageToggle = (languageCode: string, checked: boolean) => {
    if (checked) {
      setFormData(prev => ({
        ...prev,
        selectedLanguages: [...prev.selectedLanguages, languageCode]
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        selectedLanguages: prev.selectedLanguages.filter(code => code !== languageCode)
      }));
    }
  };

  const getLanguagesByRegion = () => {
    if (!languages) return {};
    return languages.reduce((acc: any, lang: any) => {
      if (!acc[lang.region]) acc[lang.region] = [];
      acc[lang.region].push(lang);
      return acc;
    }, {});
  };

  const languagesByRegion = getLanguagesByRegion();

  // 头像处理函数
  const handleAvatarChange = (avatarData: string) => {
    setFormData(prev => ({ 
      ...prev, 
      avatarData,
      // 如果是data URL格式，清空原有的avatarUrl
      avatarUrl: avatarData.startsWith('data:') ? '' : avatarData
    }));
  };

  const handleAvatarRemove = () => {
    setFormData(prev => ({ 
      ...prev, 
      avatarData: undefined,
      avatarUrl: ''
    }));
  };

  if (languagesLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl">
          <div className="flex justify-center p-8">加载中...</div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? '创建新的语音角色' : '编辑语音角色'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create' ? '填写角色信息并选择支持的语言' : '修改角色信息和语言支持设置'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基本信息卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 头像上传组件 */}
              <AvatarUpload
                currentAvatarUrl={formData.avatarUrl}
                onAvatarChange={handleAvatarChange}
                onAvatarRemove={handleAvatarRemove}
                disabled={createMutation.isPending || updateMutation.isPending}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">角色名称 *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="slug">角色标识 *</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                    placeholder="role-name"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="nameEn">英文名称</Label>
                  <Input
                    id="nameEn"
                    value={formData.nameEn}
                    onChange={(e) => setFormData(prev => ({ ...prev, nameEn: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="nameZh">中文名称</Label>
                  <Input
                    id="nameZh"
                    value={formData.nameZh}
                    onChange={(e) => setFormData(prev => ({ ...prev, nameZh: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="voiceName">语音名称 *</Label>
                  <Input
                    id="voiceName"
                    value={formData.voiceName}
                    onChange={(e) => setFormData(prev => ({ ...prev, voiceName: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="genderEn">性别 (英文)</Label>
                  <Select
                    value={formData.genderEn}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, genderEn: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择性别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Male">Male</SelectItem>
                      <SelectItem value="Female">Female</SelectItem>
                      <SelectItem value="Not set">Not set</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="genderZh">性别 (中文)</Label>
                  <Select
                    value={formData.genderZh}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, genderZh: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择性别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="男性">男性</SelectItem>
                      <SelectItem value="女性">女性</SelectItem>
                      <SelectItem value="未设置">未设置</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="mt-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  />
                  <Label htmlFor="isActive">启用角色</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 描述信息卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">描述信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="description">默认描述</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  placeholder="角色的默认描述"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="descriptionEn">英文描述</Label>
                  <Textarea
                    id="descriptionEn"
                    value={formData.descriptionEn}
                    onChange={(e) => setFormData(prev => ({ ...prev, descriptionEn: e.target.value }))}
                    rows={3}
                    placeholder="English description"
                  />
                </div>
                <div>
                  <Label htmlFor="descriptionZh">中文描述</Label>
                  <Textarea
                    id="descriptionZh"
                    value={formData.descriptionZh}
                    onChange={(e) => setFormData(prev => ({ ...prev, descriptionZh: e.target.value }))}
                    rows={3}
                    placeholder="中文描述"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 风格标签卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">风格标签</CardTitle>
              <CardDescription className="text-sm">使用逗号分隔多个标签</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="styles">默认风格</Label>
                <Input
                  id="styles"
                  value={formData.styles.join(', ')}
                  onChange={(e) => {
                    const styles = e.target.value.split(',').map(s => s.trim()).filter(s => s);
                    setFormData(prev => ({ ...prev, styles }));
                  }}
                  placeholder="中性, 友好, 专业"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="stylesEn">英文风格</Label>
                  <Input
                    id="stylesEn"
                    value={formData.stylesEn.join(', ')}
                    onChange={(e) => {
                      const styles = e.target.value.split(',').map(s => s.trim()).filter(s => s);
                      setFormData(prev => ({ ...prev, stylesEn: styles }));
                    }}
                    placeholder="neutral, friendly, professional"
                  />
                </div>
                <div>
                  <Label htmlFor="stylesZh">中文风格</Label>
                  <Input
                    id="stylesZh"
                    value={formData.stylesZh.join(', ')}
                    onChange={(e) => {
                      const styles = e.target.value.split(',').map(s => s.trim()).filter(s => s);
                      setFormData(prev => ({ ...prev, stylesZh: styles }));
                    }}
                    placeholder="中性, 友好, 专业"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 语言选择卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center justify-between">
                <span>支持的语言 *</span>
                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const allLanguageCodes = languages?.map(lang => lang.code) || [];
                      setFormData(prev => ({ ...prev, selectedLanguages: allLanguageCodes }));
                    }}
                  >
                    全选
                  </Button>
                  <Button
                    type="button"
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      setFormData(prev => ({ ...prev, selectedLanguages: [] }));
                    }}
                  >
                    清空
                  </Button>
                </div>
              </CardTitle>
              <CardDescription>
                已选择 {formData.selectedLanguages.length} 种语言 / 总共 {languages?.length || 0} 种语言
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="max-h-60 overflow-y-auto border rounded-lg p-4">
                {Object.entries(languagesByRegion).map(([region, regionLanguages]) => (
                  <div key={region} className="mb-4">
                    <h4 className="font-medium text-sm mb-2 text-muted-foreground">{region}</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {(regionLanguages as any[]).map((lang: any) => (
                        <div key={lang.code} className="flex items-center space-x-2">
                          <Checkbox
                            id={lang.code}
                            checked={formData.selectedLanguages.includes(lang.code)}
                            onCheckedChange={(checked: boolean) =>
                              handleLanguageToggle(lang.code, checked)
                            }
                          />
                          <Label htmlFor={lang.code} className="text-sm">
                            {lang.name} ({lang.nativeName})
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button 
              type="submit" 
              disabled={createMutation.isPending || updateMutation.isPending}
            >
              {mode === 'create' 
                ? (createMutation.isPending ? '创建中...' : '创建角色')
                : (updateMutation.isPending ? '更新中...' : '更新角色')
              }
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
