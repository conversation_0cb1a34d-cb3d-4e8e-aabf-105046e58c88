"use client";

import { useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { PencilIcon, TrashIcon } from "@heroicons/react/24/outline";
import { Volume2, Languages, Link as LinkIcon } from "lucide-react";
import { toast } from "sonner";
import { api } from "~/trpc/react";
import { RoleModelMappingDialog } from "./RoleModelMappingDialog";

interface TtsRole {
  id: string;
  slug: string;
  name: string;
  nameEn: string | null;
  nameZh: string | null;
  description: string | null;
  descriptionEn: string | null;
  descriptionZh: string | null;
  styles: string[];
  stylesEn: string[] | null;
  stylesZh: string[] | null;
  voiceName: string;
  isActive: boolean;
  avatarUrl: string | null;
  genderZh: string | null;
  genderEn: string | null;
  createdAt: Date;
  updatedAt: Date;
  languageSupports?: {
    id: string;
    languageCode: string;
    quality: string;
    isDefault: boolean;
    sampleText: string | null;
    sampleUrl: string | null;
    language: {
      code: string;
      name: string;
      nativeName: string;
      region: string;
    };
  }[];
}

interface RoleListProps {
  onEditRole: (role: TtsRole) => void;
}

export default function RoleList({ onEditRole }: RoleListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [languageFilter, setLanguageFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [mappingDialogOpen, setMappingDialogOpen] = useState(false);
  const [selectedRoleForMapping, setSelectedRoleForMapping] = useState<{ id: string; name: string } | null>(null);
  const [regionFilter, setRegionFilter] = useState<string>("all");

  // tRPC 查询
  const utils = api.useUtils();
  
  // 获取角色列表（带语言支持信息和模型关联）
  const { data: rolesData, isLoading: rolesLoading } = api.voiceRole.getVoiceRoles.useQuery({
    page: 1,
    limit: 100,
    includeModelMappings: true
  });

  const roles = rolesData?.roles;
  
  // 获取所有支持的语言
  const { data: languages, isLoading: languagesLoading } = api.language.getAll.useQuery();
  
  // 获取语言地区
  const { data: regions } = api.language.getRegions.useQuery();

  // 过滤角色数据
  const filteredRoles = roles?.filter((role: any) => {
    const matchesSearch = !searchTerm || 
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'true' && role.isActive) ||
      (statusFilter === 'false' && !role.isActive);
    
    // 地区筛选
    let matchesRegion = regionFilter === 'all';
    if (!matchesRegion && role.languageSupports) {
      matchesRegion = role.languageSupports.some((support: any) => 
        support.language.region === regionFilter
      );
    }
    
    return matchesSearch && matchesStatus && matchesRegion;
  }) || [];

  // 删除角色mutation
  const deleteMutation = api.tts.delete.useMutation({
    onSuccess: () => {
      toast.success('语音角色删除成功');
      utils.tts.getAll.invalidate();
    },
    onError: (error: any) => {
      toast.error(error.message || '删除失败');
    },
  });

  const handleDelete = (id: string) => {
    if (confirm('确定要删除这个语音角色吗？此操作无法撤销。')) {
      deleteMutation.mutate({ id });
    }
  };

  const handleOpenMappingDialog = (role: any) => {
    setSelectedRoleForMapping({ id: role.id, name: role.name });
    setMappingDialogOpen(true);
  };

  if (rolesLoading || languagesLoading) {
    return <div className="flex justify-center p-8">加载中...</div>;
  }

  return (
    <>
      {/* 筛选器 */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="search">搜索角色</Label>
              <Input
                id="search"
                placeholder="搜索角色名称或描述"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="language-filter">语言筛选</Label>
              <Select value={languageFilter} onValueChange={setLanguageFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择语言" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有语言</SelectItem>
                  {languages?.map((lang: any) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      {lang.name} ({lang.roleCount})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="region-filter">地区筛选</Label>
              <Select value={regionFilter} onValueChange={setRegionFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择地区" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有地区</SelectItem>
                  {regions?.map((region: any) => (
                    <SelectItem key={region.region} value={region.region}>
                      {region.region} ({region.languageCount})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="status-filter">状态筛选</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="true">启用</SelectItem>
                  <SelectItem value="false">禁用</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 角色列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>角色列表 ({filteredRoles.length})</span>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Languages className="h-4 w-4" />
              <span>总计支持 {languages?.length} 种语言</span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>角色信息</TableHead>
                  <TableHead>语音配置</TableHead>
                  <TableHead>描述 & 风格</TableHead>
                  <TableHead>关联模型</TableHead>
                  <TableHead>语言支持</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRoles.map((role: any) => (
                  <TableRow key={role.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          {role.avatarUrl && (
                            <img 
                              src={role.avatarUrl} 
                              alt={role.name}
                              className="w-6 h-6 rounded-full object-cover"
                            />
                          )}
                          <div className="font-medium">{role.name}</div>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {role.nameEn && `EN: ${role.nameEn}`}
                          {role.nameZh && (role.nameEn ? ` | ZH: ${role.nameZh}` : `ZH: ${role.nameZh}`)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          #{role.slug}
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-sm font-medium">{role.voiceName}</div>
                        <div className="text-xs text-muted-foreground">
                          {(role.genderZh || role.genderEn) ? (
                            <>
                              {role.genderZh && `${role.genderZh}`}
                              {role.genderEn && role.genderZh && ` / `}
                              {role.genderEn && role.genderEn !== role.genderZh && `${role.genderEn}`}
                            </>
                          ) : (
                            '性别未设置'
                          )}
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-2 max-w-xs">
                        {/* 描述 */}
                        {role.description && (
                          <div className="text-xs text-muted-foreground truncate">
                            {role.description}
                          </div>
                        )}
                        
                        {/* 风格标签 */}
                        <div className="flex flex-wrap gap-1">
                          {role.styles?.slice(0, 3).map((style: string, index: number) => (
                            <Badge key={index} variant="outline" className="text-xs px-1.5 py-0.5">
                              {style}
                            </Badge>
                          ))}
                          {role.styles && role.styles.length > 3 && (
                            <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                              +{role.styles.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </TableCell>

                    {/* 关联模型 */}
                    <TableCell>
                      <div className="space-y-2">
                        {role.modelMappings && role.modelMappings.length > 0 ? (
                          <>
                            <div className="text-sm font-medium text-center">
                              <span className="text-lg font-bold text-primary">{role.modelMappings.length}</span>
                              <div className="text-xs text-muted-foreground">个模型</div>
                            </div>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleOpenMappingDialog(role)}
                              className="text-xs text-primary hover:bg-primary/10 w-full h-6"
                            >
                              编辑关联
                            </Button>
                          </>
                        ) : (
                          <div className="space-y-2">
                            <div className="text-sm text-muted-foreground text-center">
                              <span className="text-lg font-bold text-muted-foreground">0</span>
                              <div className="text-xs">个模型</div>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleOpenMappingDialog(role)}
                              className="text-xs text-primary hover:bg-primary/10 w-full h-6"
                            >
                              立即配置
                            </Button>
                          </div>
                        )}
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="text-center">
                        <span className="text-lg font-bold text-emerald-600 dark:text-emerald-400">{role.languageSupports?.length || 0}</span>
                        <div className="text-xs text-muted-foreground">种语言</div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <Badge variant={role.isActive ? "default" : "secondary"}>
                        {role.isActive ? "启用" : "禁用"}
                      </Badge>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onEditRole(role)}
                          title="编辑角色"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>

                        <Button
                          size="sm"
                          variant="ghost"
                          asChild
                          title="语音样本"
                        >
                          <Link href={`/admin/roles/${role.id}/voice-samples`}>
                            <Volume2 className="h-4 w-4" />
                          </Link>
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleOpenMappingDialog(role)}
                          title="关联模型"
                          className="text-primary hover:bg-primary/10"
                        >
                          <LinkIcon className="h-4 w-4 mr-1" />
                          模型
                        </Button>

                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDelete(role.id)}
                          disabled={deleteMutation.isPending}
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredRoles.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">暂无符合条件的角色</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 模型关联对话框 */}
      {selectedRoleForMapping && (
        <RoleModelMappingDialog
          open={mappingDialogOpen}
          onOpenChange={setMappingDialogOpen}
          roleId={selectedRoleForMapping.id}
          roleName={selectedRoleForMapping.name}
        />
      )}
    </>
  );
}
