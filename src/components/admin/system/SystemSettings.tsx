"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Badge } from "~/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { RefreshCw, Plus, Edit, Trash2, Save, Settings, Database, Server, Shield, Palette } from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";

interface SystemSettingFormData {
  key: string;
  value: string;
  description: string;
  category: string;
}

// 系统设置分类
const SETTING_CATEGORIES = {
  GENERAL: "常规设置",
  API: "API配置",
  SECURITY: "安全设置", 
  UI: "界面设置",
  INTEGRATION: "第三方集成",
  BACKUP: "备份配置",
} as const;

// 预设的系统设置模板
const SETTING_TEMPLATES = [
  {
    key: "site_name",
    value: "VocTana AI语音平台",
    description: "网站名称",
    category: "GENERAL"
  },
  {
    key: "site_description", 
    value: "专业的AI语音合成和角色管理平台",
    description: "网站描述",
    category: "GENERAL"
  },
  {
    key: "max_upload_size",
    value: "10485760",
    description: "最大上传文件大小（字节）",
    category: "GENERAL"
  },
  {
    key: "gemini_api_endpoint",
    value: "https://generativelanguage.googleapis.com",
    description: "Gemini API端点",
    category: "API"
  },
  {
    key: "rate_limit_per_minute",
    value: "60",
    description: "每分钟API调用限制",
    category: "API"
  },
  {
    key: "session_timeout",
    value: "1800",
    description: "会话超时时间（秒）",
    category: "SECURITY"
  },
  {
    key: "password_min_length",
    value: "8",
    description: "密码最小长度",
    category: "SECURITY"
  },
  {
    key: "theme_color",
    value: "#3b82f6",
    description: "主题色",
    category: "UI"
  },
  {
    key: "items_per_page",
    value: "20",
    description: "每页显示条目数",
    category: "UI"
  },
];

export default function SystemSettings() {
  const [activeTab, setActiveTab] = useState("settings");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [formData, setFormData] = useState<SystemSettingFormData>({
    key: "",
    value: "",
    description: "",
    category: "GENERAL",
  });

  // 获取现有的系统设置
  const { data: settings, isLoading, refetch } = api.systemSettings.getAll.useQuery(
    undefined,
    {
      // 如果API不存在则静默失败
      enabled: true,
    }
  );

  const handleRefresh = () => {
    void refetch();
  };

  const handleInitializeDefaults = () => {
    // TODO: 实现默认设置初始化
    toast.info("系统设置API开发中，即将支持默认设置初始化");
  };

  const handleCreate = () => {
    if (!formData.key.trim()) {
      toast.error("设置键不能为空");
      return;
    }
    // TODO: 实现创建逻辑
    toast.info("系统设置API开发中，即将支持创建功能");
    setIsCreateDialogOpen(false);
  };

  const handleUpdate = (id: string) => {
    // TODO: 实现更新逻辑
    toast.info("系统设置API开发中，即将支持更新功能");
    setEditingId(null);
  };

  const handleDelete = (id: string) => {
    if (confirm("确定要删除这个系统设置吗？")) {
      // TODO: 实现删除逻辑
      toast.info("系统设置API开发中，即将支持删除功能");
    }
  };

  const resetForm = () => {
    setFormData({
      key: "",
      value: "",
      description: "",
      category: "GENERAL",
    });
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'GENERAL': return <Settings className="h-4 w-4" />;
      case 'API': return <Server className="h-4 w-4" />;
      case 'SECURITY': return <Shield className="h-4 w-4" />;
      case 'UI': return <Palette className="h-4 w-4" />;
      default: return <Database className="h-4 w-4" />;
    }
  };

  // 按分类分组设置（如果没有数据则显示模拟数据）
  const settingsByCategory = settings?.reduce((acc: Record<string, any[]>, setting: any) => {
    const category = setting.category || 'GENERAL';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(setting);
    return acc;
  }, {}) || {};

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">系统设置</h2>
          <p className="text-muted-foreground">
            配置系统参数、API设置、安全策略等
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>
          <Button variant="outline" onClick={handleInitializeDefaults}>
            <Database className="mr-2 h-4 w-4" />
            初始化默认设置
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                添加设置
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>添加系统设置</DialogTitle>
                <DialogDescription>
                  添加新的系统配置参数
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="key">设置键</Label>
                  <Input
                    id="key"
                    value={formData.key}
                    onChange={(e) => setFormData(prev => ({ ...prev, key: e.target.value }))}
                    placeholder="例如: max_upload_size"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="value">设置值</Label>
                  <Input
                    id="value"
                    value={formData.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                    placeholder="例如: 10485760"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">描述</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="设置项的描述信息"
                    rows={2}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleCreate}>
                  创建
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="settings">系统设置</TabsTrigger>
          <TabsTrigger value="templates">设置模板</TabsTrigger>
          <TabsTrigger value="status">系统状态</TabsTrigger>
        </TabsList>

        <TabsContent value="settings" className="space-y-4">
          {/* 系统设置列表 */}
          {settings && settings.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>当前系统设置</CardTitle>
                <CardDescription>{settings.length} 个配置项</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>设置键</TableHead>
                      <TableHead>设置值</TableHead>
                      <TableHead>描述</TableHead>
                      <TableHead>最后更新</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {settings.map((setting: any) => (
                      <TableRow key={setting.id}>
                        <TableCell>
                          <Badge variant="outline" className="font-mono">
                            {setting.key}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <span className="font-mono text-sm">
                            {setting.value.length > 50 
                              ? `${setting.value.substring(0, 50)}...` 
                              : setting.value}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {setting.description || "-"}
                          </span>
                        </TableCell>
                        <TableCell className="text-sm">
                          {new Date(setting.updatedAt).toLocaleDateString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                          })}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button 
                              size="sm" 
                              variant="outline" 
                              onClick={() => handleUpdate(setting.id)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(setting.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <Settings className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">暂无系统设置</h3>
                <p className="text-muted-foreground mb-4">
                  点击"初始化默认设置"来创建常用的系统配置
                </p>
                <Button onClick={handleInitializeDefaults}>
                  <Database className="mr-2 h-4 w-4" />
                  初始化默认设置
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>系统设置模板</CardTitle>
              <CardDescription>
                预设的常用系统设置模板，可快速创建
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {Object.entries(SETTING_CATEGORIES).map(([categoryKey, categoryName]) => {
                  const categoryTemplates = SETTING_TEMPLATES.filter(t => t.category === categoryKey);
                  if (categoryTemplates.length === 0) return null;

                  return (
                    <div key={categoryKey} className="space-y-3">
                      <div className="flex items-center space-x-2">
                        {getCategoryIcon(categoryKey)}
                        <h3 className="text-lg font-medium">{categoryName}</h3>
                      </div>
                      <div className="grid gap-2">
                        {categoryTemplates.map((template) => (
                          <div key={template.key} className="flex items-center justify-between p-3 border rounded-lg">
                            <div>
                              <Badge variant="outline" className="font-mono mb-1">
                                {template.key}
                              </Badge>
                              <p className="text-sm text-muted-foreground">{template.description}</p>
                              <p className="text-sm font-mono bg-muted px-2 py-1 rounded mt-1 inline-block">
                                {template.value}
                              </p>
                            </div>
                            <Button 
                              size="sm"
                              onClick={() => {
                                setFormData(template);
                                setIsCreateDialogOpen(true);
                              }}
                            >
                              使用模板
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="status" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">API状态</CardTitle>
                <Server className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">正常</div>
                <p className="text-xs text-muted-foreground">
                  所有API服务正常运行
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">数据库连接</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">已连接</div>
                <p className="text-xs text-muted-foreground">
                  数据库连接正常
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">系统负载</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">中等</div>
                <p className="text-xs text-muted-foreground">
                  CPU: 45%, 内存: 67%
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>系统信息</CardTitle>
              <CardDescription>当前系统的运行状态和配置信息</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">系统监控功能开发中</p>
                <p className="text-sm">详细的系统状态监控和日志查看即将上线</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      )}
    </div>
  );
}
