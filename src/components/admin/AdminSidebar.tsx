"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "~/lib/utils";
import {
  HomeIcon,
  UsersIcon,
  SpeakerWaveIcon,
  CreditCardIcon,
  ShoppingCartIcon,
  ChartBarIcon,
  CpuChipIcon,
  CogIcon,
} from "@heroicons/react/24/outline";

const navigation = [
  { name: "仪表板", href: "/admin", icon: HomeIcon },
  { name: "用户管理", href: "/admin/users", icon: UsersIcon },
  { name: "语音角色", href: "/admin/roles", icon: SpeakerWaveIcon },
  { name: "积分管理", href: "/admin/credit-management", icon: CreditCardIcon },
  { name: "订单管理", href: "/admin/orders", icon: ShoppingCartIcon },
  { name: "统计分析", href: "/admin/stats", icon: ChartBarIcon },
  { name: "模型管理", href: "/admin/model-management", icon: CpuChipIcon },
  { name: "系统设置", href: "/admin/system-settings", icon: CogIcon },
];

export default function AdminSidebar() {
  const pathname = usePathname();

  return (
    <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
      <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-card px-6 pb-4 shadow-sm border-r">
        <div className="flex h-16 shrink-0 items-center">
          <h1 className="text-xl font-bold text-foreground">管理后台</h1>
        </div>
        <nav className="flex flex-1 flex-col">
          <ul role="list" className="flex flex-1 flex-col gap-y-7">
            <li>
              <ul role="list" className="-mx-2 space-y-1">
                {navigation.map((item) => {
                  // 特殊处理模型管理相关路由的高亮
                  let isActive = pathname === item.href;
                  if (item.href === "/admin/model-management") {
                    isActive = pathname === item.href || 
                              pathname === "/admin/token-usage" || 
                              pathname === "/admin/providers";
                  }
                  
                  return (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className={cn(
                          isActive
                            ? "bg-accent text-accent-foreground"
                            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground",
                          "group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6"
                        )}
                      >
                        <item.icon
                          className={cn(
                            isActive
                              ? "text-accent-foreground"
                              : "text-muted-foreground group-hover:text-accent-foreground",
                            "h-6 w-6 shrink-0"
                          )}
                          aria-hidden="true"
                        />
                        {item.name}
                      </Link>
                    </li>
                  );
                })}
              </ul>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  );
}