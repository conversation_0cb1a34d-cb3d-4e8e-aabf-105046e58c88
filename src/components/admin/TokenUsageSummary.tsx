"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { api } from "~/trpc/react";
// import { MODEL_TYPE_NAMES, getModelTypeColor, formatCost, formatTokenCount } from "~/lib/token-utils"; // 暂时禁用
import { Cpu, DollarSign, BarChart3 } from "lucide-react";

interface TokenUsageSummaryProps {
  userId?: string;
  className?: string;
}

export function TokenUsageSummary({ userId, className }: TokenUsageSummaryProps) {
  // 获取用户token使用统计
  const { data: usageStats, isLoading } = api.tokenUsage.getUserUsageStats.useQuery({
    userId,
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 最近30天
    endDate: new Date(),
  });

  if (isLoading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${className}`}>
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="animate-pulse">
                <div className="h-4 bg-muted rounded w-20 mb-2"></div>
                <div className="h-6 bg-muted rounded w-16"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const stats = usageStats?.totalStats;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 总体统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总Token使用</CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(stats?.totalTokens || 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              输入: {(stats?.totalInputTokens || 0).toLocaleString()} |
              输出: {(stats?.totalOutputTokens || 0).toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总成本</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${(stats?.totalCostUsd || 0).toFixed(4)}
            </div>
            <p className="text-xs text-muted-foreground">
              扣除积分: {(stats?.totalCreditsDeducted || 0).toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API调用次数</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(stats?.totalRequests || 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              最近30天
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 最近使用记录 */}
      {usageStats?.usages && usageStats.usages.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>最近使用记录</CardTitle>
            <CardDescription>最近的API调用和token消耗</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {usageStats.usages.slice(0, 5).map((usage) => (
                <div key={usage.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Badge>
                      {usage.modelType}
                    </Badge>
                    <div>
                      <div className="font-medium">{usage.modelName}</div>
                      <div className="text-sm text-muted-foreground">
                        {usage.endpoint} • {usage.totalTokens.toLocaleString()} tokens
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">${usage.costUsd.toFixed(4)}</div>
                    <div className="text-sm text-muted-foreground">
                      {usage.creditsDeducted > 0 && `${usage.creditsDeducted} 积分`}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}