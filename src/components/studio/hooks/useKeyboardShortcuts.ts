'use client';

import { useEffect, useCallback } from 'react';

interface KeyboardShortcutsConfig {
  onGenerate?: () => void;
  onPlayPause?: () => void;
  onStop?: () => void;
  onSave?: () => void;
  onToggleSidebar?: () => void;
  onClearError?: () => void;
  onFocusTextInput?: () => void;
}

export const useKeyboardShortcuts = (config: KeyboardShortcutsConfig) => {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // 忽略在输入框中的快捷键（除了特定的组合键）
    const isInputElement = (event.target as HTMLElement)?.tagName === 'INPUT' || 
                          (event.target as HTMLElement)?.tagName === 'TEXTAREA' ||
                          (event.target as HTMLElement)?.contentEditable === 'true';

    // Ctrl+Enter 或 Cmd+Enter: 生成语音
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
      event.preventDefault();
      config.onGenerate?.();
      return;
    }

    // Ctrl+S 或 Cmd+S: 保存项目
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
      event.preventDefault();
      config.onSave?.();
      return;
    }

    // Ctrl+B 或 Cmd+B: 切换侧边栏
    if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
      event.preventDefault();
      config.onToggleSidebar?.();
      return;
    }

    // Escape: 清除错误或停止播放
    if (event.key === 'Escape') {
      event.preventDefault();
      config.onClearError?.();
      config.onStop?.();
      return;
    }

    // Ctrl+/ 或 Cmd+/: 聚焦到文本输入框
    if ((event.ctrlKey || event.metaKey) && event.key === '/') {
      event.preventDefault();
      config.onFocusTextInput?.();
      return;
    }

    // 以下快捷键只在非输入元素中生效
    if (isInputElement) return;

    // Space: 播放/暂停
    if (event.key === ' ') {
      event.preventDefault();
      config.onPlayPause?.();
      return;
    }

    // S: 停止播放
    if (event.key === 's' || event.key === 'S') {
      event.preventDefault();
      config.onStop?.();
      return;
    }

    // G: 生成语音
    if (event.key === 'g' || event.key === 'G') {
      event.preventDefault();
      config.onGenerate?.();
      return;
    }
  }, [config]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // 返回快捷键说明
  const shortcuts = [
    { key: 'Ctrl+Enter', description: 'Generate voice' },
    { key: 'Space', description: 'Play/Pause audio' },
    { key: 'S', description: 'Stop audio' },
    { key: 'G', description: 'Generate voice' },
    { key: 'Ctrl+S', description: 'Save project' },
    { key: 'Ctrl+B', description: 'Toggle sidebar' },
    { key: 'Ctrl+/', description: 'Focus text input' },
    { key: 'Escape', description: 'Clear error/Stop' },
  ];

  return { shortcuts };
};
