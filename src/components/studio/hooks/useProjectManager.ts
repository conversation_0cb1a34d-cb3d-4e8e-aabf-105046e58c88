'use client';

import { useCallback, useState } from 'react';
import { api } from '~/trpc/react';
import type { Project } from '@prisma/client';

interface ProjectContent {
  inputText: string;
  styleInstructions: string;
}

interface ProjectSettings {
  selectedLanguage?: string;
  selectedRoleId?: string;
  selectedModelId?: string;
  selectedMode: 'single' | 'multi';
}

export const useProjectManager = () => {
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [isProjectDialogOpen, setIsProjectDialogOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // API queries and mutations
  const { data: projects, refetch: refetchProjects } = api.project.getProjects.useQuery({
    limit: 50,
  });

  const createProjectMutation = api.project.createProject.useMutation({
    onSuccess: (project) => {
      setCurrentProject(project);
      refetchProjects();
    },
  });

  const updateProjectMutation = api.project.updateProject.useMutation({
    onSuccess: (project) => {
      setCurrentProject(project);
      refetchProjects();
    },
  });

  const deleteProjectMutation = api.project.deleteProject.useMutation({
    onSuccess: () => {
      refetchProjects();
    },
  });

  const duplicateProjectMutation = api.project.duplicateProject.useMutation({
    onSuccess: (project) => {
      setCurrentProject(project);
      refetchProjects();
    },
  });

  const addHistoryMutation = api.project.addHistory.useMutation();

  // Project operations
  const createProject = useCallback(async (
    name: string,
    description?: string,
    content?: ProjectContent,
    settings?: ProjectSettings
  ) => {
    try {
      setIsSaving(true);
      const project = await createProjectMutation.mutateAsync({
        name,
        description,
        content,
        settings,
      });
      return project;
    } finally {
      setIsSaving(false);
    }
  }, [createProjectMutation]);

  const saveProject = useCallback(async (
    content: ProjectContent,
    settings: ProjectSettings,
    projectName?: string
  ) => {
    try {
      setIsSaving(true);
      
      if (currentProject) {
        // Update existing project
        const updatedProject = await updateProjectMutation.mutateAsync({
          id: currentProject.id,
          name: projectName || currentProject.name,
          content,
          settings,
        });
        return updatedProject;
      } else {
        // Create new project
        const newProject = await createProjectMutation.mutateAsync({
          name: projectName || 'Untitled Project',
          content,
          settings,
        });
        return newProject;
      }
    } finally {
      setIsSaving(false);
    }
  }, [currentProject, createProjectMutation, updateProjectMutation]);

  const loadProject = useCallback(async (projectId: string) => {
    try {
      const project = await api.project.getProject.query({ id: projectId });
      setCurrentProject(project);
      return project;
    } catch (error) {
      console.error('Failed to load project:', error);
      throw error;
    }
  }, []);

  const deleteProject = useCallback(async (projectId: string) => {
    try {
      await deleteProjectMutation.mutateAsync({ id: projectId });
      if (currentProject?.id === projectId) {
        setCurrentProject(null);
      }
    } catch (error) {
      console.error('Failed to delete project:', error);
      throw error;
    }
  }, [currentProject, deleteProjectMutation]);

  const duplicateProject = useCallback(async (projectId: string, newName?: string) => {
    try {
      const duplicated = await duplicateProjectMutation.mutateAsync({
        id: projectId,
        name: newName,
      });
      return duplicated;
    } catch (error) {
      console.error('Failed to duplicate project:', error);
      throw error;
    }
  }, [duplicateProjectMutation]);

  const addToHistory = useCallback(async (
    action: string,
    changes?: Record<string, any>,
    audioUrl?: string
  ) => {
    if (!currentProject) return;

    try {
      await addHistoryMutation.mutateAsync({
        projectId: currentProject.id,
        action,
        changes,
        audioUrl,
      });
    } catch (error) {
      console.error('Failed to add history:', error);
    }
  }, [currentProject, addHistoryMutation]);

  const autoSave = useCallback(async (
    content: ProjectContent,
    settings: ProjectSettings
  ) => {
    if (!currentProject) return;

    try {
      await updateProjectMutation.mutateAsync({
        id: currentProject.id,
        content,
        settings,
      });
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  }, [currentProject, updateProjectMutation]);

  const newProject = useCallback(() => {
    setCurrentProject(null);
  }, []);

  const openProjectDialog = useCallback(() => {
    setIsProjectDialogOpen(true);
  }, []);

  const closeProjectDialog = useCallback(() => {
    setIsProjectDialogOpen(false);
  }, []);

  return {
    // State
    currentProject,
    projects: projects || [],
    isProjectDialogOpen,
    isSaving,
    
    // Actions
    createProject,
    saveProject,
    loadProject,
    deleteProject,
    duplicateProject,
    addToHistory,
    autoSave,
    newProject,
    openProjectDialog,
    closeProjectDialog,
    setCurrentProject,
    
    // Loading states
    isCreating: createProjectMutation.isPending,
    isUpdating: updateProjectMutation.isPending,
    isDeleting: deleteProjectMutation.isPending,
    isDuplicating: duplicateProjectMutation.isPending,
  };
};
