'use client';

import React from 'react';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Skeleton } from '~/components/ui/skeleton';
import { VoiceRoleCard } from './VoiceRoleCard';
import type { VoiceRoleGridProps } from './types';

export const VoiceRoleGrid: React.FC<VoiceRoleGridProps> = ({
  roles,
  selectedRole,
  favoriteRoles,
  onRoleSelect,
  onToggleFavorite,
  onPreview,
  isLoading = false,
  playingRoleId
}) => {
  // 检查角色是否被收藏
  const isRoleFavorited = (roleId: string) => {
    return favoriteRoles.some(favRole => favRole.id === roleId);
  };

  // 加载状态骨架屏
  const LoadingSkeleton = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {Array.from({ length: 8 }).map((_, index) => (
        <div key={index} className="space-y-3 p-4 border rounded-lg">
          <div className="flex items-start gap-3">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-3/4" />
            </div>
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-5 w-12" />
            <Skeleton className="h-5 w-16" />
          </div>
          <div className="flex justify-between">
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-16" />
          </div>
        </div>
      ))}
    </div>
  );

  // 空状态
  const EmptyState = () => (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="text-muted-foreground mb-2">
        <svg
          className="h-12 w-12 mx-auto mb-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
          />
        </svg>
      </div>
      <h3 className="text-lg font-medium mb-1">没有找到匹配的角色</h3>
      <p className="text-sm text-muted-foreground">
        尝试调整筛选条件或搜索关键词
      </p>
    </div>
  );

  if (isLoading) {
    return (
      <ScrollArea className="h-[400px] w-full">
        <LoadingSkeleton />
      </ScrollArea>
    );
  }

  if (roles.length === 0) {
    return <EmptyState />;
  }

  return (
    <ScrollArea className="h-[400px] w-full">
      <div className="space-y-2 p-1">
        {roles.map((role) => (
          <VoiceRoleCard
            key={role.id}
            role={role}
            isSelected={selectedRole?.id === role.id}
            isFavorited={isRoleFavorited(role.id)}
            onSelect={onRoleSelect}
            onToggleFavorite={onToggleFavorite}
            onPreview={onPreview}
            isPlaying={playingRoleId === role.id}
          />
        ))}
      </div>
    </ScrollArea>
  );
};
