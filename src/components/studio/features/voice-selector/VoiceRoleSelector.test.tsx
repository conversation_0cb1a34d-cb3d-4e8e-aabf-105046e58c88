import { render, screen, fireEvent } from '@testing-library/react';
import { VoiceRoleSelector } from './VoiceRoleSelector';
import { api } from '~/trpc/react';

// Mock tRPC
jest.mock('~/trpc/react', () => ({
  api: {
    voiceRole: {
      getPublicVoiceRoles: {
        useQuery: jest.fn()
      }
    },
    voiceRoleFavorite: {
      getUserFavorites: {
        useQuery: jest.fn()
      },
      addFavorite: {
        useMutation: jest.fn()
      },
      removeFavorite: {
        useMutation: jest.fn()
      }
    }
  }
}));

// Mock useMediaQuery
jest.mock('~/hooks/use-media-query', () => ({
  useMediaQuery: jest.fn(() => false) // 默认桌面端
}));

const mockRoles = [
  {
    id: '1',
    name: 'Alice',
    description: 'Female voice',
    genderEn: 'FEMALE',
    languageSupports: [{ language: { code: 'en' } }],
    rating: 4.5
  },
  {
    id: '2', 
    name: '<PERSON>',
    description: 'Male voice',
    genderEn: 'MALE',
    languageSupports: [{ language: { code: 'zh' } }],
    rating: 4.2
  }
];

describe('VoiceRoleSelector', () => {
  beforeEach(() => {
    // Mock API responses
    (api.voiceRole.getPublicVoiceRoles.useQuery as jest.Mock).mockReturnValue({
      data: { roles: mockRoles, total: 2 },
      isLoading: false,
      error: null
    });

    (api.voiceRoleFavorite.getUserFavorites.useQuery as jest.Mock).mockReturnValue({
      data: [],
      isLoading: false,
      error: null
    });

    (api.voiceRoleFavorite.addFavorite.useMutation as jest.Mock).mockReturnValue({
      mutateAsync: jest.fn()
    });

    (api.voiceRoleFavorite.removeFavorite.useMutation as jest.Mock).mockReturnValue({
      mutateAsync: jest.fn()
    });
  });

  it('renders selector button', () => {
    render(
      <VoiceRoleSelector
        selectedRole={null}
        onRoleChange={jest.fn()}
        roles={[]}
      />
    );

    expect(screen.getByText('选择语音角色')).toBeInTheDocument();
  });

  it('opens dialog when clicked', () => {
    render(
      <VoiceRoleSelector
        selectedRole={null}
        onRoleChange={jest.fn()}
        roles={[]}
      />
    );

    fireEvent.click(screen.getByText('选择语音角色'));
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });

  it('displays roles in list format', () => {
    render(
      <VoiceRoleSelector
        selectedRole={null}
        onRoleChange={jest.fn()}
        roles={[]}
      />
    );

    fireEvent.click(screen.getByText('选择语音角色'));
    
    expect(screen.getByText('Alice')).toBeInTheDocument();
    expect(screen.getByText('Bob')).toBeInTheDocument();
  });

  it('filters by gender', () => {
    const mockQuery = api.voiceRole.getPublicVoiceRoles.useQuery as jest.Mock;
    
    render(
      <VoiceRoleSelector
        selectedRole={null}
        onRoleChange={jest.fn()}
        roles={[]}
      />
    );

    fireEvent.click(screen.getByText('选择语音角色'));
    fireEvent.click(screen.getByText('女性'));

    // 验证API被调用时包含性别筛选
    expect(mockQuery).toHaveBeenCalledWith(
      expect.objectContaining({
        gender: 'FEMALE'
      }),
      expect.any(Object)
    );
  });
});
