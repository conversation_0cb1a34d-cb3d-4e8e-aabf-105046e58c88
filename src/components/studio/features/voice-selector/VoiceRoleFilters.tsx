'use client';

import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Button } from '~/components/ui/button';
import { User, Users, Baby } from 'lucide-react';
import type { VoiceRoleFiltersProps } from './types';

export const VoiceRoleFilters: React.FC<VoiceRoleFiltersProps> = ({
  selectedLanguage,
  selectedGender,
  onLanguageChange,
  onGenderChange,
  availableLanguages
}) => {
  const genderOptions = [
    { value: 'ALL' as const, label: '全部', icon: Users },
    { value: 'Male' as const, label: '男性', icon: User },
    { value: 'Female' as const, label: '女性', icon: User },
    { value: 'Child' as const, label: '童声', icon: Baby }
  ];

  return (
    <div className="space-y-4">
      {/* 筛选器行 */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* 语言筛选 */}
        <div className="flex-1">
          <Select value={selectedLanguage} onValueChange={onLanguageChange}>
            <SelectTrigger>
              <SelectValue placeholder="选择语言" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有语言</SelectItem>
              {availableLanguages.map((lang) => (
                <SelectItem key={lang.code} value={lang.code}>
                  {lang.name}
                  {lang.roleCount && ` (${lang.roleCount})`}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 性别筛选 */}
        <div className="flex gap-1 bg-muted p-1 rounded-lg">
          {genderOptions.map((option) => {
            const Icon = option.icon;
            return (
              <Button
                key={option.value}
                variant={selectedGender === option.value ? "default" : "ghost"}
                size="sm"
                onClick={() => onGenderChange(option.value)}
                className="flex items-center gap-1.5 px-3"
              >
                <Icon className="h-4 w-4" />
                <span className="hidden sm:inline">{option.label}</span>
              </Button>
            );
          })}
        </div>
      </div>
    </div>
  );
};
