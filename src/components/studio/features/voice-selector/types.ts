// 语音角色选择器相关类型定义

import type { VoiceRole } from '../../types';

export interface VoiceRoleSelectorState {
  // 筛选条件
  selectedLanguage: string;
  selectedGender: 'Male' | 'Female' | 'Child' | 'ALL';

  // 当前标签页
  activeTab: 'system' | 'favorites';

  // 角色数据
  systemRoles: VoiceRole[];
  favoriteRoles: VoiceRole[];

  // UI状态
  isOpen: boolean;
  isLoading: boolean;
  selectedRole: VoiceRole | null;
}

export interface VoiceRoleSelectorProps {
  selectedRole: VoiceRole | null;
  onRoleChange: (role: VoiceRole | null) => void;
  roles: VoiceRole[];
  disabled?: boolean;
  placeholder?: string;
}

export interface VoiceRoleFiltersProps {
  selectedLanguage: string;
  selectedGender: 'Male' | 'Female' | 'Child' | 'ALL';
  onLanguageChange: (language: string) => void;
  onGenderChange: (gender: 'Male' | 'Female' | 'Child' | 'ALL') => void;
  availableLanguages: Array<{ code: string; name: string; nativeName: string }>;
}

export interface VoiceRoleCardProps {
  role: VoiceRole;
  isSelected: boolean;
  isFavorited: boolean;
  onSelect: (role: VoiceRole) => void;
  onToggleFavorite: (role: VoiceRole) => void;
  onPreview: (role: VoiceRole) => void;
  isPlaying?: boolean;
}

export interface VoiceRoleGridProps {
  roles: VoiceRole[];
  selectedRole: VoiceRole | null;
  favoriteRoles: VoiceRole[];
  onRoleSelect: (role: VoiceRole) => void;
  onToggleFavorite: (role: VoiceRole) => void;
  onPreview: (role: VoiceRole) => void;
  isLoading?: boolean;
  playingRoleId?: string;
}

export interface VoiceRoleTabsProps {
  activeTab: 'system' | 'favorites';
  onTabChange: (tab: 'system' | 'favorites') => void;
  systemRolesCount: number;
  favoriteRolesCount: number;
}

export interface VoiceRolePreviewProps {
  role: VoiceRole | null;
  isPlaying: boolean;
  onPlay: () => void;
  onStop: () => void;
  volume: number;
  onVolumeChange: (volume: number) => void;
}

// API相关类型
export interface GetVoiceRolesParams {
  gender?: 'Male' | 'Female' | 'Child';
  language?: string;
  page?: number;
  limit?: number;
}

export interface VoiceRoleFavorite {
  id: string;
  userId: string;
  roleId: string;
  createdAt: Date;
  role: VoiceRole;
}

// 语言选项类型
export interface LanguageOption {
  code: string;
  name: string;
  nativeName: string;
  roleCount?: number;
}

// 性别选项类型
export interface GenderOption {
  value: 'Male' | 'Female' | 'Child' | 'ALL';
  label: string;
  icon: string;
}
