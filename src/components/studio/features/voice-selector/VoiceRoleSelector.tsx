'use client';

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '~/components/ui/dialog';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '~/components/ui/sheet';
import { Button } from '~/components/ui/button';
import { ChevronDown, Mic } from 'lucide-react';
import { api } from '~/trpc/react';
import { useMediaQuery } from '~/hooks/use-media-query';
import type { VoiceRoleSelectorProps, VoiceRoleSelectorState } from './types';
import type { VoiceRole } from '../../types';
import { VoiceRoleFilters } from './VoiceRoleFilters';
import { VoiceRoleTabs } from './VoiceRoleTabs';
import { VoiceRoleGrid } from './VoiceRoleGrid';
import { VoiceRolePreview } from './VoiceRolePreview';

export const VoiceRoleSelector: React.FC<VoiceRoleSelectorProps> = ({
  selectedRole,
  onRoleChange,
  roles,
  disabled = false,
  placeholder = "选择语音角色"
}) => {
  const isMobile = useMediaQuery('(max-width: 768px)');

  const [state, setState] = useState<VoiceRoleSelectorState>({
    selectedLanguage: 'all',
    selectedGender: 'ALL',
    activeTab: 'system',
    systemRoles: [],
    favoriteRoles: [],
    isOpen: false,
    isLoading: false,
    selectedRole: selectedRole
  });

  // 试听状态
  const [previewState, setPreviewState] = useState({
    playingRoleId: null as string | null,
    isPlaying: false,
    volume: 80,
    previewRole: null as VoiceRole | null
  });

  // 获取语言统计数据
  const { data: languageStats, isLoading: languagesLoading } = api.tts.getLanguageStats.useQuery();

  // 获取语音角色数据 - 根据选择的语言获取
  const { data: rolesData, isLoading: rolesLoading, error: rolesError } = api.tts.getRolesByLanguage.useQuery({
    languageCode: state.selectedLanguage,
    limit: 100
  }, {
    enabled: !!state.selectedLanguage && state.selectedLanguage !== 'all',
    staleTime: 2 * 60 * 1000, // 2分钟缓存
  });

  // 获取用户收藏数据
  const { data: favoritesData, isLoading: favoritesLoading, error: favoritesError } = api.voiceRoleFavorite.getUserFavorites.useQuery(undefined, {
    staleTime: 2 * 60 * 1000, // 2分钟缓存
  });

  // 收藏/取消收藏 mutations
  const addFavoriteMutation = api.voiceRoleFavorite.addFavorite.useMutation();
  const removeFavoriteMutation = api.voiceRoleFavorite.removeFavorite.useMutation();

  // 更新系统角色数据
  useEffect(() => {
    if (rolesData && Array.isArray(rolesData)) {
      // 添加gender字段映射
      const mappedRoles = rolesData.map(role => ({
        ...role,
        gender: role.genderEn, // 映射genderEn到gender字段
      }));

      setState(prev => ({
        ...prev,
        systemRoles: mappedRoles,
        isLoading: rolesLoading
      }));
    } else if (rolesError) {
      console.error('Failed to load voice roles:', rolesError);
      setState(prev => ({
        ...prev,
        isLoading: false
      }));
    }
  }, [rolesData, rolesLoading, rolesError]);

  // 更新收藏角色数据
  useEffect(() => {
    if (favoritesData) {
      setState(prev => ({
        ...prev,
        favoriteRoles: favoritesData.map((fav: any) => fav.role),
        isLoading: favoritesLoading
      }));
    } else if (favoritesError) {
      console.error('Failed to load favorites:', favoritesError);
      // 收藏加载失败不影响主要功能，只记录错误
    }
  }, [favoritesData, favoritesLoading, favoritesError]);

  const handleOpenChange = useCallback((open: boolean) => {
    setState(prev => ({ ...prev, isOpen: open }));
  }, []);

  const handleRoleSelect = useCallback((role: VoiceRole) => {
    setState(prev => ({ ...prev, selectedRole: role, isOpen: false }));
    onRoleChange(role);
  }, [onRoleChange]);

  const handleLanguageChange = useCallback((language: string) => {
    setState(prev => ({ ...prev, selectedLanguage: language }));
  }, []);

  const handleGenderChange = useCallback((gender: 'Male' | 'Female' | 'Child' | 'ALL') => {
    setState(prev => ({ ...prev, selectedGender: gender }));
  }, []);

  const handleTabChange = useCallback((tab: 'system' | 'favorites') => {
    setState(prev => ({ ...prev, activeTab: tab }));
  }, []);

  const handleToggleFavorite = useCallback(async (role: VoiceRole) => {
    try {
      const isFavorited = state.favoriteRoles.some(favRole => favRole.id === role.id);

      if (isFavorited) {
        await removeFavoriteMutation.mutateAsync({ roleId: role.id });
        setState(prev => ({
          ...prev,
          favoriteRoles: prev.favoriteRoles.filter(favRole => favRole.id !== role.id)
        }));
      } else {
        await addFavoriteMutation.mutateAsync({ roleId: role.id });
        setState(prev => ({
          ...prev,
          favoriteRoles: [...prev.favoriteRoles, role]
        }));
      }
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    }
  }, [state.favoriteRoles, addFavoriteMutation, removeFavoriteMutation]);

  const handlePreview = useCallback((role: VoiceRole) => {
    if (previewState.playingRoleId === role.id && previewState.isPlaying) {
      // 如果正在播放同一个角色，则停止
      setPreviewState(prev => ({
        ...prev,
        isPlaying: false,
        playingRoleId: null
      }));
    } else {
      // 开始播放新角色
      setPreviewState(prev => ({
        ...prev,
        playingRoleId: role.id,
        isPlaying: true,
        previewRole: role
      }));
    }
  }, [previewState.playingRoleId, previewState.isPlaying]);

  const handlePreviewPlay = useCallback(() => {
    setPreviewState(prev => ({ ...prev, isPlaying: true }));
  }, []);

  const handlePreviewStop = useCallback(() => {
    setPreviewState(prev => ({
      ...prev,
      isPlaying: false,
      playingRoleId: null
    }));
  }, []);

  const handleVolumeChange = useCallback((volume: number) => {
    setPreviewState(prev => ({ ...prev, volume }));
  }, []);

  // 获取当前显示的角色列表（包含性别筛选）
  const currentRoles = useMemo(() => {
    let roles = state.activeTab === 'system' ? state.systemRoles : state.favoriteRoles;

    // 应用性别筛选
    if (state.selectedGender !== 'ALL') {
      roles = roles.filter(role => role.gender === state.selectedGender || role.genderEn === state.selectedGender);
    }

    return roles;
  }, [state.systemRoles, state.favoriteRoles, state.activeTab, state.selectedGender]);

  // 获取可用语言列表
  const availableLanguages = useMemo(() => {
    if (!languageStats || !Array.isArray(languageStats)) return [];

    // 只返回有角色的语言
    return languageStats
      .filter((lang: any) => lang.roleCount > 0)
      .map((lang: any) => ({
        code: lang.code,
        name: lang.name,
        nativeName: lang.nativeName
      }));
  }, [languageStats]);

  return (
    <>
      {/* 触发按钮 */}
      <Button
        variant="outline"
        onClick={() => handleOpenChange(true)}
        disabled={disabled}
        className="w-full justify-between"
      >
        <div className="flex items-center gap-2">
          <Mic className="h-4 w-4" />
          <span className="truncate">
            {selectedRole ? selectedRole.name : placeholder}
          </span>
        </div>
        <ChevronDown className="h-4 w-4 opacity-50" />
      </Button>

      {/* 选择器弹窗 - 响应式 */}
      {isMobile ? (
        <Sheet open={state.isOpen} onOpenChange={handleOpenChange}>
          <SheetContent side="bottom" className="h-[90vh] overflow-hidden">
            <SheetHeader>
              <SheetTitle className="flex items-center gap-2">
                <Mic className="h-5 w-5" />
                选择语音角色
              </SheetTitle>
            </SheetHeader>

            <div className="flex flex-col space-y-4 overflow-hidden mt-4">
              {/* 筛选器 */}
              <VoiceRoleFilters
                selectedLanguage={state.selectedLanguage}
                selectedGender={state.selectedGender}
                onLanguageChange={handleLanguageChange}
                onGenderChange={handleGenderChange}
                availableLanguages={availableLanguages}
              />

              {/* 标签页 */}
              <VoiceRoleTabs
                activeTab={state.activeTab}
                onTabChange={handleTabChange}
                systemRolesCount={state.systemRoles.length}
                favoriteRolesCount={state.favoriteRoles.length}
              />

              {/* 角色列表 */}
              <VoiceRoleGrid
                roles={currentRoles}
                selectedRole={state.selectedRole}
                favoriteRoles={state.favoriteRoles}
                onRoleSelect={handleRoleSelect}
                onToggleFavorite={handleToggleFavorite}
                onPreview={handlePreview}
                isLoading={state.isLoading}
                playingRoleId={previewState.playingRoleId}
              />

              {/* 试听组件 */}
              {previewState.previewRole && (
                <VoiceRolePreview
                  role={previewState.previewRole}
                  isPlaying={previewState.isPlaying}
                  onPlay={handlePreviewPlay}
                  onStop={handlePreviewStop}
                  volume={previewState.volume}
                  onVolumeChange={handleVolumeChange}
                />
              )}
            </div>
          </SheetContent>
        </Sheet>
      ) : (
        <Dialog open={state.isOpen} onOpenChange={handleOpenChange}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Mic className="h-5 w-5" />
                选择语音角色
              </DialogTitle>
            </DialogHeader>

            <div className="flex flex-col space-y-4 overflow-hidden">
              {/* 筛选器 */}
              <VoiceRoleFilters
                selectedLanguage={state.selectedLanguage}
                selectedGender={state.selectedGender}
                onLanguageChange={handleLanguageChange}
                onGenderChange={handleGenderChange}
                availableLanguages={availableLanguages}
              />

              {/* 标签页 */}
              <VoiceRoleTabs
                activeTab={state.activeTab}
                onTabChange={handleTabChange}
                systemRolesCount={state.systemRoles.length}
                favoriteRolesCount={state.favoriteRoles.length}
              />

              {/* 角色列表 */}
              <VoiceRoleGrid
                roles={currentRoles}
                selectedRole={state.selectedRole}
                favoriteRoles={state.favoriteRoles}
                onRoleSelect={handleRoleSelect}
                onToggleFavorite={handleToggleFavorite}
                onPreview={handlePreview}
                isLoading={state.isLoading}
                playingRoleId={previewState.playingRoleId}
              />

              {/* 试听组件 */}
              {previewState.previewRole && (
                <VoiceRolePreview
                  role={previewState.previewRole}
                  isPlaying={previewState.isPlaying}
                  onPlay={handlePreviewPlay}
                  onStop={handlePreviewStop}
                  volume={previewState.volume}
                  onVolumeChange={handleVolumeChange}
                />
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};
