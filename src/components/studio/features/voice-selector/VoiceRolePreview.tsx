'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Button } from '~/components/ui/button';
import { Slider } from '~/components/ui/slider';
import { Card, CardContent } from '~/components/ui/card';
import { Play, Pause, Square, Volume2 } from 'lucide-react';
import type { VoiceRolePreviewProps } from './types';

export const VoiceRolePreview: React.FC<VoiceRolePreviewProps> = ({
  role,
  isPlaying,
  onPlay,
  onStop,
  volume,
  onVolumeChange
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // 示例音频URL - 实际应该从API获取
  const sampleAudioUrl = role ? `/api/voice-samples/${role.id}.mp3` : null;

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
      setIsLoading(false);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      onStop();
      setCurrentTime(0);
    };

    const handleLoadStart = () => {
      setIsLoading(true);
    };

    const handleCanPlay = () => {
      setIsLoading(false);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('canplay', handleCanPlay);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('canplay', handleCanPlay);
    };
  }, [onStop]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.volume = volume / 100;
  }, [volume]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.play().catch(console.error);
    } else {
      audio.pause();
    }
  }, [isPlaying]);

  const handlePlayPause = () => {
    if (isPlaying) {
      onStop();
    } else {
      onPlay();
    }
  };

  const handleSeek = (value: number[]) => {
    const audio = audioRef.current;
    if (!audio || !duration) return;

    const newTime = (value[0] / 100) * duration;
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (!role) {
    return null;
  }

  return (
    <Card className="mt-4">
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* 角色信息 */}
          <div className="flex items-center gap-3">
            <div className="flex-1">
              <h4 className="font-medium text-sm">{role.name}</h4>
              <p className="text-xs text-muted-foreground">语音预览</p>
            </div>
          </div>

          {/* 音频控制 */}
          <div className="space-y-3">
            {/* 播放控制按钮 */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePlayPause}
                disabled={isLoading || !sampleAudioUrl}
                className="h-8 w-8 p-0"
              >
                {isLoading ? (
                  <div className="h-3 w-3 animate-spin rounded-full border border-current border-t-transparent" />
                ) : isPlaying ? (
                  <Pause className="h-3 w-3" />
                ) : (
                  <Play className="h-3 w-3" />
                )}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={onStop}
                disabled={!isPlaying}
                className="h-8 w-8 p-0"
              >
                <Square className="h-3 w-3" />
              </Button>

              {/* 时间显示 */}
              <div className="flex-1 text-xs text-muted-foreground">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
            </div>

            {/* 进度条 */}
            <div className="space-y-2">
              <Slider
                value={[duration ? (currentTime / duration) * 100 : 0]}
                onValueChange={handleSeek}
                max={100}
                step={1}
                className="w-full"
                disabled={!duration}
              />
            </div>

            {/* 音量控制 */}
            <div className="flex items-center gap-2">
              <Volume2 className="h-4 w-4 text-muted-foreground" />
              <Slider
                value={[volume]}
                onValueChange={(value) => onVolumeChange(value[0])}
                max={100}
                step={1}
                className="flex-1"
              />
              <span className="text-xs text-muted-foreground w-8">
                {volume}%
              </span>
            </div>
          </div>

          {/* 隐藏的音频元素 */}
          {sampleAudioUrl && (
            <audio
              ref={audioRef}
              src={sampleAudioUrl}
              preload="metadata"
            />
          )}

          {/* 无音频提示 */}
          {!sampleAudioUrl && (
            <div className="text-center py-4">
              <p className="text-sm text-muted-foreground">
                暂无语音样本
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
