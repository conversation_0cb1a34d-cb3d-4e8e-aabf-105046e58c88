'use client';

import React from 'react';
import { Card, CardContent } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '~/components/ui/avatar';
import { Heart, Play, User, Users, UserX } from 'lucide-react';
import type { VoiceRoleCardProps } from './types';

export const VoiceRoleCard: React.FC<VoiceRoleCardProps> = ({
  role,
  isSelected,
  isFavorited,
  onSelect,
  onToggleFavorite,
  onPreview,
  isPlaying = false
}) => {
  const getGenderIcon = (gender?: string) => {
    switch (gender) {
      case 'MALE':
        return <User className="h-3 w-3" />;
      case 'FEMALE':
        return <Users className="h-3 w-3" />;
      default:
        return <User className="h-3 w-3" />;
    }
  };

  const getGenderLabel = (gender?: string) => {
    switch (gender) {
      case 'MALE':
        return '男性';
      case 'FEMALE':
        return '女性';
      default:
        return '未知';
    }
  };

  const getLanguageLabel = (languageCode: string) => {
    const languageMap: Record<string, string> = {
      'zh': '中文',
      'en': 'English',
      'ja': '日本語',
      'ko': '한국어',
      'es': 'Español',
      'fr': 'Français',
      'de': 'Deutsch'
    };
    return languageMap[languageCode] || languageCode;
  };

  // 获取主要语言
  const primaryLanguage = role.languageSupports?.[0]?.languageCode || 
                         role.supportedLanguages?.[0] || 
                         'zh';

  return (
    <Card
      className={`cursor-pointer transition-all duration-200 hover:shadow-sm ${
        isSelected
          ? 'ring-2 ring-primary shadow-sm bg-primary/5'
          : 'hover:ring-1 hover:ring-muted-foreground/20'
      }`}
      onClick={() => onSelect(role)}
    >
      <CardContent className="p-3">
        {/* 列表形式布局 */}
        <div className="flex items-center gap-3">
          {/* 头像 */}
          <Avatar className="h-10 w-10 flex-shrink-0">
            <AvatarImage src={role.avatarUrl || role.avatar} alt={role.name} />
            <AvatarFallback className="text-sm font-medium">
              {role.name.slice(0, 2).toUpperCase()}
            </AvatarFallback>
          </Avatar>

          {/* 基本信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-sm truncate" title={role.name}>
                {role.name}
              </h3>

              {/* 标签信息 */}
              <div className="flex items-center gap-1">
                {/* 性别标签 */}
                <Badge variant="outline" className="text-xs flex items-center gap-1">
                  {getGenderIcon(role.gender)}
                  {getGenderLabel(role.gender)}
                </Badge>

                {/* 语言标签 */}
                <Badge variant="secondary" className="text-xs">
                  {getLanguageLabel(primaryLanguage)}
                </Badge>

                {/* 评分标签 */}
                {role.rating && (
                  <Badge variant="outline" className="text-xs">
                    ⭐ {role.rating.toFixed(1)}
                  </Badge>
                )}
              </div>
            </div>

            {role.description && (
              <p className="text-xs text-muted-foreground truncate mt-1">
                {role.description}
              </p>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onToggleFavorite(role);
              }}
              className="h-8 w-8 p-0"
            >
              <Heart
                className={`h-4 w-4 ${
                  isFavorited
                    ? 'fill-red-500 text-red-500'
                    : 'text-muted-foreground hover:text-red-500'
                }`}
              />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onPreview(role);
              }}
              className="h-8 px-3 text-xs"
              disabled={isPlaying}
            >
              <Play className={`h-3 w-3 mr-1 ${isPlaying ? 'animate-pulse' : ''}`} />
              {isPlaying ? '播放中' : '试听'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
