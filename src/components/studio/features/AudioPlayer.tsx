'use client';

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '~/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Slider } from '~/components/ui/slider';
import { Progress } from '~/components/ui/progress';
import { Badge } from '~/components/ui/badge';
import { Separator } from '~/components/ui/separator';
import {
  Play,
  Pause,
  Square,
  Download,
  Volume2,
  VolumeX,
  Loader2,
  SkipBack,
  SkipForward,
  RotateCcw,
  Music,
  Clock
} from 'lucide-react';
import type { AudioPlayerProps } from '../types';

export const AudioPlayer: React.FC<AudioPlayerProps> = ({
  audioState,
  onTogglePlayPause,
  onStop,
  onVolumeChange,
  onSeek
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);

  const formatTime = useCallback((seconds: number) => {
    if (!isFinite(seconds) || isNaN(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const handleProgressChange = useCallback((value: number[]) => {
    const newTime = (value[0] / 100) * audioState.duration;
    onSeek(newTime);
  }, [audioState.duration, onSeek]);

  const handleVolumeChange = useCallback((value: number[]) => {
    onVolumeChange(value[0] / 100);
  }, [onVolumeChange]);

  const handleSkipBackward = useCallback(() => {
    const newTime = Math.max(0, audioState.currentTime - 10);
    onSeek(newTime);
  }, [audioState.currentTime, onSeek]);

  const handleSkipForward = useCallback(() => {
    const newTime = Math.min(audioState.duration, audioState.currentTime + 10);
    onSeek(newTime);
  }, [audioState.currentTime, audioState.duration, onSeek]);

  const handleDownload = useCallback(() => {
    if (audioState.url) {
      const a = document.createElement('a');
      a.href = audioState.url;
      a.download = `generated-audio-${Date.now()}.wav`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  }, [audioState.url]);

  const toggleMute = useCallback(() => {
    onVolumeChange(audioState.volume > 0 ? 0 : 1);
  }, [audioState.volume, onVolumeChange]);

  const progressPercentage = audioState.duration > 0
    ? (audioState.currentTime / audioState.duration) * 100
    : 0;

  // 如果没有音频URL，显示空状态
  if (!audioState.url) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <div className="flex flex-col items-center space-y-4 text-center">
            <div className="rounded-full bg-muted p-4">
              <Music className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h3 className="font-medium">No audio generated yet</h3>
              <p className="text-sm text-muted-foreground">
                Generate speech to see audio controls
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <CardTitle className="text-base flex items-center gap-2">
              <Music className="h-4 w-4" />
              Audio Player
            </CardTitle>
            <CardDescription>
              {audioState.isLoading ? 'Generating audio...' : 'Generated audio ready to play'}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {formatTime(audioState.duration)}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDownload}
              disabled={audioState.isLoading}
              className="h-8 w-8 p-0"
            >
              <Download className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 主要播放控制 */}
        <div className="flex items-center justify-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSkipBackward}
            disabled={audioState.isLoading}
            className="h-10 w-10 p-0"
          >
            <SkipBack className="h-4 w-4" />
          </Button>

          <Button
            onClick={onTogglePlayPause}
            disabled={audioState.isLoading}
            size="lg"
            className="h-12 w-12 rounded-full p-0"
          >
            {audioState.isLoading ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : audioState.isPlaying ? (
              <Pause className="h-5 w-5" />
            ) : (
              <Play className="h-5 w-5" />
            )}
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleSkipForward}
            disabled={audioState.isLoading}
            className="h-10 w-10 p-0"
          >
            <SkipForward className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={onStop}
            disabled={audioState.isLoading}
            className="h-10 w-10 p-0"
          >
            <Square className="h-4 w-4" />
          </Button>
        </div>

        {/* 进度条和时间 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {formatTime(audioState.currentTime)}
            </span>
            <span>{formatTime(audioState.duration)}</span>
          </div>

          <Slider
            value={[progressPercentage]}
            onValueChange={handleProgressChange}
            max={100}
            step={0.1}
            className="w-full"
            disabled={audioState.isLoading || audioState.duration === 0}
          />

          <Progress
            value={progressPercentage}
            className="h-1"
          />
        </div>

        <Separator />

        {/* 音量控制 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleMute}
                className="h-8 w-8 p-0"
              >
                {audioState.volume === 0 ? (
                  <VolumeX className="h-4 w-4" />
                ) : (
                  <Volume2 className="h-4 w-4" />
                )}
              </Button>
              <span className="text-sm text-muted-foreground">
                Volume
              </span>
            </div>
            <Badge variant="outline" className="text-xs">
              {Math.round(audioState.volume * 100)}%
            </Badge>
          </div>

          <Slider
            value={[audioState.volume * 100]}
            onValueChange={handleVolumeChange}
            max={100}
            step={1}
            className="w-full"
          />
        </div>

        {/* 多说话者结果 */}
        {audioState.multiSpeakerResults && audioState.multiSpeakerResults.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Multi-Speaker Results</h4>
                <Badge variant="secondary" className="text-xs">
                  {audioState.multiSpeakerResults.length} segments
                </Badge>
              </div>
              <div className="grid gap-2 max-h-32 overflow-y-auto">
                {audioState.multiSpeakerResults.map((result, index) => (
                  <div
                    key={result.segmentId}
                    className="flex items-center justify-between p-2 rounded-lg border bg-muted/50"
                  >
                    <div className="flex-1 min-w-0">
                      <p className="text-xs font-medium truncate">
                        Speaker {result.speakerId}
                      </p>
                      <p className="text-xs text-muted-foreground truncate">
                        {result.text}
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const a = document.createElement('a');
                        a.href = result.audioUrl;
                        a.download = `segment-${index + 1}.wav`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                      }}
                      className="h-6 w-6 p-0 ml-2"
                    >
                      <Download className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};
