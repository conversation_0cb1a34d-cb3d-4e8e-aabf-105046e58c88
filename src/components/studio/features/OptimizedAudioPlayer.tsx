'use client';

import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { Button } from '~/components/ui/button';
import { Slider } from '~/components/ui/slider';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { 
  Play, 
  Pause, 
  Download, 
  Volume2, 
  VolumeX,
  SkipBack,
  SkipForward,
  Loader2,
  BarChart3
} from 'lucide-react';
import type { AudioState } from '../types';
import { 
  audioCache, 
  useThrottle, 
  useWebWorker,
  usePerformanceProfiler,
  useComponentCache 
} from '../utils/PerformanceOptimizations';

interface OptimizedAudioPlayerProps {
  audioState: AudioState;
  onPlayPause: () => void;
  onSeek: (time: number) => void;
  onVolumeChange: (volume: number) => void;
  onDownload: () => void;
}

export const OptimizedAudioPlayer: React.FC<OptimizedAudioPlayerProps> = ({
  audioState,
  onPlayPause,
  onSeek,
  onVolumeChange,
  onDownload,
}) => {
  usePerformanceProfiler('OptimizedAudioPlayer');
  
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [waveformData, setWaveformData] = useState<number[]>([]);
  const [isVisualizationEnabled, setIsVisualizationEnabled] = useState(false);
  
  // Web Worker for audio processing
  const { postMessage, onMessage, isReady } = useWebWorker('/workers/audioProcessor.js');
  
  // 节流优化的事件处理
  const throttledSeek = useThrottle(onSeek, 100);
  const throttledVolumeChange = useThrottle(onVolumeChange, 50);
  
  // 缓存音频元素
  const cachedAudio = useComponentCache(
    'audio-element',
    () => {
      if (audioState.url) {
        const cached = audioCache.get(audioState.url);
        if (cached) return cached;
        
        const audio = new Audio(audioState.url);
        audioCache.set(audioState.url, audio);
        return audio;
      }
      return null;
    },
    [audioState.url]
  );

  // 格式化时间 - 使用useMemo优化
  const formatTime = useCallback((seconds: number) => {
    if (isNaN(seconds)) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const currentTimeFormatted = useMemo(() => 
    formatTime(audioState.currentTime), 
    [audioState.currentTime, formatTime]
  );
  
  const durationFormatted = useMemo(() => 
    formatTime(audioState.duration), 
    [audioState.duration, formatTime]
  );

  // 音频可视化
  useEffect(() => {
    if (isVisualizationEnabled && audioState.url && isReady) {
      // 请求生成波形数据
      fetch(audioState.url)
        .then(response => response.arrayBuffer())
        .then(buffer => {
          const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
          return audioContext.decodeAudioData(buffer);
        })
        .then(audioBuffer => {
          const channelData = audioBuffer.getChannelData(0);
          postMessage({
            type: 'calculateWaveform',
            data: {
              audioBuffer: Array.from(channelData),
              samples: 200
            }
          });
        })
        .catch(error => {
          console.error('Failed to process audio for visualization:', error);
        });
    }
  }, [audioState.url, isVisualizationEnabled, isReady, postMessage]);

  // 监听Web Worker消息
  useEffect(() => {
    onMessage((event) => {
      const { type, data } = event.data;
      
      switch (type) {
        case 'waveformCalculated':
          setWaveformData(data);
          break;
        case 'error':
          console.error('Audio processing error:', data.message);
          break;
      }
    });
  }, [onMessage]);

  // 绘制波形
  useEffect(() => {
    if (waveformData.length > 0 && canvasRef.current) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      const { width, height } = canvas;
      ctx.clearRect(0, 0, width, height);

      // 绘制波形
      ctx.fillStyle = '#3B82F6';
      const barWidth = width / waveformData.length;
      
      waveformData.forEach((value, index) => {
        const barHeight = value * height * 0.8;
        const x = index * barWidth;
        const y = (height - barHeight) / 2;
        
        ctx.fillRect(x, y, barWidth - 1, barHeight);
      });

      // 绘制进度指示器
      if (audioState.duration > 0) {
        const progress = audioState.currentTime / audioState.duration;
        const progressX = progress * width;
        
        ctx.fillStyle = '#EF4444';
        ctx.fillRect(progressX - 1, 0, 2, height);
      }
    }
  }, [waveformData, audioState.currentTime, audioState.duration]);

  // 处理进度条点击
  const handleProgressClick = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const progress = clickX / rect.width;
    const newTime = progress * audioState.duration;
    throttledSeek(newTime);
  }, [audioState.duration, throttledSeek]);

  // 处理音量变化
  const handleVolumeChange = useCallback((value: number[]) => {
    throttledVolumeChange(value[0] || 0);
  }, [throttledVolumeChange]);

  // 处理进度变化
  const handleProgressChange = useCallback((value: number[]) => {
    const newTime = (value[0] || 0) * audioState.duration / 100;
    throttledSeek(newTime);
  }, [audioState.duration, throttledSeek]);

  // 键盘快捷键
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.code) {
        case 'Space':
          e.preventDefault();
          onPlayPause();
          break;
        case 'ArrowLeft':
          e.preventDefault();
          throttledSeek(Math.max(0, audioState.currentTime - 10));
          break;
        case 'ArrowRight':
          e.preventDefault();
          throttledSeek(Math.min(audioState.duration, audioState.currentTime + 10));
          break;
        case 'ArrowUp':
          e.preventDefault();
          throttledVolumeChange(Math.min(1, audioState.volume + 0.1));
          break;
        case 'ArrowDown':
          e.preventDefault();
          throttledVolumeChange(Math.max(0, audioState.volume - 0.1));
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [audioState.currentTime, audioState.duration, audioState.volume, onPlayPause, throttledSeek, throttledVolumeChange]);

  if (audioState.isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span>Generating audio...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!audioState.url) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <p className="text-gray-500 mb-2">No audio generated yet</p>
            <p className="text-sm text-gray-400">Generate speech to see audio controls</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const progressPercentage = audioState.duration > 0 
    ? (audioState.currentTime / audioState.duration) * 100 
    : 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Audio Player</span>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisualizationEnabled(!isVisualizationEnabled)}
            >
              <BarChart3 className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onDownload}>
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 可视化画布 */}
        {isVisualizationEnabled && (
          <div className="relative">
            <canvas
              ref={canvasRef}
              width={400}
              height={80}
              className="w-full h-20 bg-gray-50 rounded cursor-pointer"
              onClick={handleProgressClick}
            />
          </div>
        )}

        {/* 进度条 */}
        <div className="space-y-2">
          <Slider
            value={[progressPercentage]}
            onValueChange={handleProgressChange}
            max={100}
            step={0.1}
            className="w-full"
          />
          <div className="flex justify-between text-sm text-gray-500">
            <span>{currentTimeFormatted}</span>
            <span>{durationFormatted}</span>
          </div>
        </div>

        {/* 控制按钮 */}
        <div className="flex items-center justify-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => throttledSeek(Math.max(0, audioState.currentTime - 10))}
          >
            <SkipBack className="h-4 w-4" />
          </Button>
          
          <Button onClick={onPlayPause} size="lg">
            {audioState.isPlaying ? (
              <Pause className="h-5 w-5" />
            ) : (
              <Play className="h-5 w-5" />
            )}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => throttledSeek(Math.min(audioState.duration, audioState.currentTime + 10))}
          >
            <SkipForward className="h-4 w-4" />
          </Button>
        </div>

        {/* 音量控制 */}
        <div className="flex items-center space-x-2">
          {audioState.volume === 0 ? (
            <VolumeX className="h-4 w-4" />
          ) : (
            <Volume2 className="h-4 w-4" />
          )}
          <Slider
            value={[audioState.volume * 100]}
            onValueChange={(value) => handleVolumeChange([value[0]! / 100])}
            max={100}
            step={1}
            className="flex-1"
          />
          <span className="text-sm text-gray-500 w-8">
            {Math.round(audioState.volume * 100)}%
          </span>
        </div>

        {/* 多说话者结果 */}
        {audioState.multiSpeakerResults && audioState.multiSpeakerResults.length > 0 && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">Multi-Speaker Dialogue</h4>
            <p className="text-sm text-gray-600">
              {audioState.multiSpeakerResults.length} segments
            </p>
          </div>
        )}

        {/* 键盘快捷键提示 */}
        <div className="text-xs text-gray-400 text-center">
          Space: Play/Pause • ←/→: Seek • ↑/↓: Volume
        </div>
      </CardContent>
    </Card>
  );
};
