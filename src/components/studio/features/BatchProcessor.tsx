'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Textarea } from '~/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Progress } from '~/components/ui/progress';
import { Badge } from '~/components/ui/badge';
import { ScrollArea } from '~/components/ui/scroll-area';
import { 
  Upload, 
  FileText, 
  Play, 
  Download, 
  Trash2, 
  CheckCircle, 
  XCircle,
  Clock,
  Loader2,
  Plus,
  FileUp
} from 'lucide-react';
import type { VoiceRole } from '../types';

interface BatchItem {
  id: string;
  text: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  audioUrl?: string;
  error?: string;
  progress?: number;
}

interface BatchProcessorProps {
  roles: VoiceRole[];
  selectedRole: VoiceRole | null;
  onGenerate: (text: string) => Promise<{ audioUrl: string }>;
  isGenerating: boolean;
}

export const BatchProcessor: React.FC<BatchProcessorProps> = ({
  roles,
  selectedRole,
  onGenerate,
  isGenerating,
}) => {
  const [batchItems, setBatchItems] = useState<BatchItem[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [newItemText, setNewItemText] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 添加单个文本项
  const addTextItem = useCallback(() => {
    if (!newItemText.trim()) return;

    const newItem: BatchItem = {
      id: Date.now().toString(),
      text: newItemText.trim(),
      status: 'pending',
    };

    setBatchItems(prev => [...prev, newItem]);
    setNewItemText('');
  }, [newItemText]);

  // 处理文件上传
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      if (!content) return;

      // 按行分割文本，过滤空行
      const lines = content
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);

      const newItems: BatchItem[] = lines.map((line, index) => ({
        id: `${Date.now()}-${index}`,
        text: line,
        status: 'pending',
      }));

      setBatchItems(prev => [...prev, ...newItems]);
    };

    reader.readAsText(file);
    // 重置文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  // 删除项目
  const removeItem = useCallback((id: string) => {
    setBatchItems(prev => prev.filter(item => item.id !== id));
  }, []);

  // 清空所有项目
  const clearAll = useCallback(() => {
    setBatchItems([]);
    setProcessingProgress(0);
  }, []);

  // 批量处理
  const processBatch = useCallback(async () => {
    if (!selectedRole || batchItems.length === 0) return;

    setIsProcessing(true);
    setProcessingProgress(0);

    const pendingItems = batchItems.filter(item => item.status === 'pending' || item.status === 'failed');
    
    for (let i = 0; i < pendingItems.length; i++) {
      const item = pendingItems[i]!;
      
      // 更新状态为处理中
      setBatchItems(prev => prev.map(prevItem => 
        prevItem.id === item.id 
          ? { ...prevItem, status: 'processing', progress: 0 }
          : prevItem
      ));

      try {
        // 模拟进度更新
        for (let progress = 0; progress <= 100; progress += 20) {
          setBatchItems(prev => prev.map(prevItem => 
            prevItem.id === item.id 
              ? { ...prevItem, progress }
              : prevItem
          ));
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        // 生成语音
        const result = await onGenerate(item.text);
        
        // 更新为完成状态
        setBatchItems(prev => prev.map(prevItem => 
          prevItem.id === item.id 
            ? { 
                ...prevItem, 
                status: 'completed', 
                audioUrl: result.audioUrl,
                progress: 100 
              }
            : prevItem
        ));
      } catch (error) {
        // 更新为失败状态
        setBatchItems(prev => prev.map(prevItem => 
          prevItem.id === item.id 
            ? { 
                ...prevItem, 
                status: 'failed', 
                error: error instanceof Error ? error.message : 'Generation failed',
                progress: 0
              }
            : prevItem
        ));
      }

      // 更新总进度
      setProcessingProgress(((i + 1) / pendingItems.length) * 100);
    }

    setIsProcessing(false);
  }, [selectedRole, batchItems, onGenerate]);

  // 下载单个音频
  const downloadAudio = useCallback(async (audioUrl: string, filename: string) => {
    try {
      const response = await fetch(audioUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${filename}.wav`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Failed to download audio:', error);
    }
  }, []);

  // 批量下载
  const downloadAll = useCallback(async () => {
    const completedItems = batchItems.filter(item => item.status === 'completed' && item.audioUrl);
    
    for (let i = 0; i < completedItems.length; i++) {
      const item = completedItems[i]!;
      await downloadAudio(item.audioUrl!, `batch-${i + 1}`);
      // 添加延迟避免浏览器阻止多个下载
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }, [batchItems, downloadAudio]);

  const getStatusIcon = (status: BatchItem['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-gray-400" />;
      case 'processing':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusColor = (status: BatchItem['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
    }
  };

  const completedCount = batchItems.filter(item => item.status === 'completed').length;
  const failedCount = batchItems.filter(item => item.status === 'failed').length;
  const pendingCount = batchItems.filter(item => item.status === 'pending').length;

  return (
    <div className="space-y-6">
      {/* 输入区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Batch Input
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 文件上传 */}
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Upload Text File
            </label>
            <div className="flex items-center space-x-2">
              <input
                ref={fileInputRef}
                type="file"
                accept=".txt,.csv"
                onChange={handleFileUpload}
                className="hidden"
              />
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                className="flex items-center"
              >
                <FileUp className="h-4 w-4 mr-2" />
                Choose File
              </Button>
              <span className="text-sm text-gray-500">
                Supports .txt and .csv files (one text per line)
              </span>
            </div>
          </div>

          {/* 手动添加 */}
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Add Text Manually
            </label>
            <div className="flex space-x-2">
              <Textarea
                value={newItemText}
                onChange={(e) => setNewItemText(e.target.value)}
                placeholder="Enter text to generate..."
                className="flex-1"
                rows={2}
              />
              <Button onClick={addTextItem} disabled={!newItemText.trim()}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 批处理控制 */}
      {batchItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <Play className="h-5 w-5 mr-2" />
                Batch Processing ({batchItems.length} items)
              </span>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearAll}
                  disabled={isProcessing}
                >
                  Clear All
                </Button>
                {completedCount > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadAll}
                    disabled={isProcessing}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download All
                  </Button>
                )}
                <Button
                  onClick={processBatch}
                  disabled={isProcessing || !selectedRole || pendingCount === 0}
                  size="sm"
                >
                  {isProcessing ? 'Processing...' : 'Start Processing'}
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* 统计信息 */}
            <div className="flex items-center space-x-4 mb-4">
              <Badge variant="outline">
                {pendingCount} Pending
              </Badge>
              <Badge className="bg-green-100 text-green-800">
                {completedCount} Completed
              </Badge>
              {failedCount > 0 && (
                <Badge className="bg-red-100 text-red-800">
                  {failedCount} Failed
                </Badge>
              )}
            </div>

            {/* 总进度 */}
            {isProcessing && (
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Overall Progress</span>
                  <span className="text-sm text-gray-500">{Math.round(processingProgress)}%</span>
                </div>
                <Progress value={processingProgress} className="w-full" />
              </div>
            )}

            {/* 项目列表 */}
            <ScrollArea className="h-[300px]">
              <div className="space-y-2">
                {batchItems.map((item, index) => (
                  <div
                    key={item.id}
                    className="p-3 border rounded-lg flex items-center justify-between"
                  >
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        {getStatusIcon(item.status)}
                        <Badge className={getStatusColor(item.status)}>
                          {item.status}
                        </Badge>
                        <span className="text-sm text-gray-500">#{index + 1}</span>
                      </div>
                      <p className="text-sm text-gray-900 truncate mb-1">
                        {item.text}
                      </p>
                      {item.status === 'processing' && item.progress !== undefined && (
                        <Progress value={item.progress} className="w-full h-1" />
                      )}
                      {item.error && (
                        <p className="text-xs text-red-600 mt-1">{item.error}</p>
                      )}
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      {item.audioUrl && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => downloadAudio(item.audioUrl!, `batch-${index + 1}`)}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeItem(item.id)}
                        disabled={isProcessing}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
