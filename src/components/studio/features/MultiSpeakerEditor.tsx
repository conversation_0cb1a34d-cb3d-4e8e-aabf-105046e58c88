'use client';

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Textarea } from '~/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { ScrollArea } from '~/components/ui/scroll-area';
import { 
  Plus, 
  Trash2, 
  Play, 
  Users, 
  Mic,
  ArrowUp,
  ArrowDown,
  Copy
} from 'lucide-react';
import type { VoiceRole } from '../types';

interface Speaker {
  id: string;
  name: string;
  roleId: string;
  role?: VoiceRole;
  color: string;
}

interface TextSegment {
  id: string;
  speakerId: string;
  text: string;
  order: number;
}

interface MultiSpeakerEditorProps {
  roles: VoiceRole[];
  onGenerate: (segments: TextSegment[], speakers: Speaker[]) => Promise<void>;
  isGenerating: boolean;
}

const SPEAKER_COLORS = [
  '#3B82F6', // Blue
  '#EF4444', // Red
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#8B5CF6', // Purple
  '#EC4899', // Pink
  '#06B6D4', // Cyan
  '#84CC16', // Lime
];

export const MultiSpeakerEditor: React.FC<MultiSpeakerEditorProps> = ({
  roles,
  onGenerate,
  isGenerating,
}) => {
  const [speakers, setSpeakers] = useState<Speaker[]>([
    {
      id: '1',
      name: 'Speaker 1',
      roleId: '',
      color: SPEAKER_COLORS[0]!,
    },
    {
      id: '2',
      name: 'Speaker 2',
      roleId: '',
      color: SPEAKER_COLORS[1]!,
    },
  ]);

  const [segments, setSegments] = useState<TextSegment[]>([
    {
      id: '1',
      speakerId: '1',
      text: '',
      order: 0,
    },
  ]);

  const addSpeaker = useCallback(() => {
    const newSpeaker: Speaker = {
      id: Date.now().toString(),
      name: `Speaker ${speakers.length + 1}`,
      roleId: '',
      color: SPEAKER_COLORS[speakers.length % SPEAKER_COLORS.length]!,
    };
    setSpeakers(prev => [...prev, newSpeaker]);
  }, [speakers.length]);

  const updateSpeaker = useCallback((id: string, updates: Partial<Speaker>) => {
    setSpeakers(prev => prev.map(speaker => 
      speaker.id === id ? { ...speaker, ...updates } : speaker
    ));
  }, []);

  const removeSpeaker = useCallback((id: string) => {
    if (speakers.length <= 1) return;
    
    setSpeakers(prev => prev.filter(speaker => speaker.id !== id));
    // Remove segments for this speaker
    setSegments(prev => prev.filter(segment => segment.speakerId !== id));
  }, [speakers.length]);

  const addSegment = useCallback(() => {
    const newSegment: TextSegment = {
      id: Date.now().toString(),
      speakerId: speakers[0]?.id || '',
      text: '',
      order: segments.length,
    };
    setSegments(prev => [...prev, newSegment]);
  }, [segments.length, speakers]);

  const updateSegment = useCallback((id: string, updates: Partial<TextSegment>) => {
    setSegments(prev => prev.map(segment => 
      segment.id === id ? { ...segment, ...updates } : segment
    ));
  }, []);

  const removeSegment = useCallback((id: string) => {
    if (segments.length <= 1) return;
    
    setSegments(prev => {
      const filtered = prev.filter(segment => segment.id !== id);
      // Reorder segments
      return filtered.map((segment, index) => ({ ...segment, order: index }));
    });
  }, [segments.length]);

  const moveSegment = useCallback((id: string, direction: 'up' | 'down') => {
    setSegments(prev => {
      const index = prev.findIndex(segment => segment.id === id);
      if (index === -1) return prev;
      
      const newIndex = direction === 'up' ? index - 1 : index + 1;
      if (newIndex < 0 || newIndex >= prev.length) return prev;
      
      const newSegments = [...prev];
      [newSegments[index], newSegments[newIndex]] = [newSegments[newIndex]!, newSegments[index]!];
      
      // Update order
      return newSegments.map((segment, idx) => ({ ...segment, order: idx }));
    });
  }, []);

  const duplicateSegment = useCallback((id: string) => {
    const segment = segments.find(s => s.id === id);
    if (!segment) return;
    
    const newSegment: TextSegment = {
      id: Date.now().toString(),
      speakerId: segment.speakerId,
      text: segment.text,
      order: segment.order + 1,
    };
    
    setSegments(prev => {
      const newSegments = [...prev];
      newSegments.splice(segment.order + 1, 0, newSegment);
      // Reorder all segments
      return newSegments.map((seg, index) => ({ ...seg, order: index }));
    });
  }, [segments]);

  const getSpeakerById = useCallback((id: string) => {
    return speakers.find(speaker => speaker.id === id);
  }, [speakers]);

  const handleGenerate = useCallback(async () => {
    // Validate that all speakers have roles assigned
    const speakersWithRoles = speakers.map(speaker => ({
      ...speaker,
      role: roles.find(role => role.id === speaker.roleId),
    }));

    const invalidSpeakers = speakersWithRoles.filter(speaker => !speaker.role);
    if (invalidSpeakers.length > 0) {
      alert('Please assign voice roles to all speakers before generating.');
      return;
    }

    // Validate that all segments have text
    const emptySegments = segments.filter(segment => !segment.text.trim());
    if (emptySegments.length > 0) {
      alert('Please add text to all segments before generating.');
      return;
    }

    await onGenerate(segments, speakersWithRoles);
  }, [segments, speakers, roles, onGenerate]);

  return (
    <div className="space-y-6">
      {/* Speakers Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Speakers ({speakers.length})
            </span>
            <Button onClick={addSpeaker} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Speaker
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {speakers.map((speaker) => (
              <div key={speaker.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: speaker.color }}
                    />
                    <Input
                      value={speaker.name}
                      onChange={(e) => updateSpeaker(speaker.id, { name: e.target.value })}
                      className="font-medium"
                    />
                  </div>
                  {speakers.length > 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSpeaker(speaker.id)}
                      className="text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
                <Select
                  value={speaker.roleId}
                  onValueChange={(value) => updateSpeaker(speaker.id, { roleId: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select voice role" />
                  </SelectTrigger>
                  <SelectContent>
                    {roles.map((role) => (
                      <SelectItem key={role.id} value={role.id}>
                        {role.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Dialogue Editor */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Mic className="h-5 w-5 mr-2" />
              Dialogue ({segments.length} segments)
            </span>
            <Button onClick={addSegment} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Segment
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px]">
            <div className="space-y-4">
              {segments
                .sort((a, b) => a.order - b.order)
                .map((segment, index) => {
                  const speaker = getSpeakerById(segment.speakerId);
                  return (
                    <div key={segment.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <Badge 
                            style={{ 
                              backgroundColor: speaker?.color,
                              color: 'white'
                            }}
                          >
                            {speaker?.name || 'Unknown Speaker'}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            Segment {index + 1}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => moveSegment(segment.id, 'up')}
                            disabled={index === 0}
                          >
                            <ArrowUp className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => moveSegment(segment.id, 'down')}
                            disabled={index === segments.length - 1}
                          >
                            <ArrowDown className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => duplicateSegment(segment.id)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          {segments.length > 1 && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeSegment(segment.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                      
                      <div className="space-y-3">
                        <Select
                          value={segment.speakerId}
                          onValueChange={(value) => updateSegment(segment.id, { speakerId: value })}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select speaker" />
                          </SelectTrigger>
                          <SelectContent>
                            {speakers.map((speaker) => (
                              <SelectItem key={speaker.id} value={speaker.id}>
                                <div className="flex items-center space-x-2">
                                  <div 
                                    className="w-3 h-3 rounded-full"
                                    style={{ backgroundColor: speaker.color }}
                                  />
                                  <span>{speaker.name}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        
                        <Textarea
                          value={segment.text}
                          onChange={(e) => updateSegment(segment.id, { text: e.target.value })}
                          placeholder="Enter dialogue text..."
                          className="min-h-[80px]"
                        />
                      </div>
                    </div>
                  );
                })}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Generate Button */}
      <div className="flex justify-center">
        <Button 
          onClick={handleGenerate}
          disabled={isGenerating || segments.some(s => !s.text.trim()) || speakers.some(s => !s.roleId)}
          size="lg"
          className="px-8"
        >
          <Play className="h-4 w-4 mr-2" />
          {isGenerating ? 'Generating Dialogue...' : 'Generate Dialogue'}
        </Button>
      </div>
    </div>
  );
};
