'use client';

import React, { createContext, useContext, useState, useRef, useEffect, useCallback } from 'react';
import { api } from '~/trpc/react';
import { useProjectManager } from '../hooks/useProjectManager';
import type {
  StudioContextType,
  StudioState,
  AudioState,
  VoiceRole,
  ModelMapping
} from '../types';

const StudioContext = createContext<StudioContextType | null>(null);

export const useStudio = () => {
  const context = useContext(StudioContext);
  if (!context) {
    throw new Error('useStudio must be used within a StudioProvider');
  }
  return context;
};

interface StudioProviderProps {
  children: React.ReactNode;
}

export const StudioProvider: React.FC<StudioProviderProps> = ({ children }) => {
  // 项目管理
  const projectManager = useProjectManager();

  // 基础状态
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [selectedMode, setSelectedMode] = useState<'single' | 'multi' | 'batch'>('single');
  const [styleInstructions, setStyleInstructions] = useState('Read aloud in a warm and friendly tone:');
  const [inputText, setInputText] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState('en'); // Default to English
  const [selectedRole, setSelectedRole] = useState<VoiceRole | null>(null);
  const [selectedModel, setSelectedModel] = useState<ModelMapping | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [audioState, setAudioState] = useState<AudioState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    url: null,
    volume: 1,
    isLoading: false
  });

  // 音频引用
  const audioRef = useRef<HTMLAudioElement>(null);

  // API查询
  const { data: languages } = api.language.getAll.useQuery();
  const { data: roles } = api.tts.getRolesByLanguage.useQuery(
    { languageCode: selectedLanguage, limit: 50 },
    { enabled: !!selectedLanguage }
  );

  // 生成语音
  const generateVoice = api.tts.generateVoiceSample.useMutation({
    onMutate: () => {
      setError(null);
      setIsGenerating(true);
      setAudioState(prev => ({ ...prev, isLoading: true }));
    },
    onSuccess: (data) => {
      setAudioState(prev => ({ ...prev, url: data.audioUrl, isLoading: false }));
      setIsGenerating(false);
      setError(null);

      // 添加到项目历史记录
      if (projectManager.currentProject) {
        projectManager.addToHistory(
          'voice_generated',
          {
            inputText,
            styleInstructions,
            selectedLanguage,
            selectedRole: selectedRole?.name,
            selectedModel: selectedModel?.name,
          },
          data.audioUrl
        );
      }
    },
    onError: (error) => {
      console.error('生成失败:', error);
      setError(error.message || 'Failed to generate voice. Please try again.');
      setAudioState(prev => ({ ...prev, isLoading: false }));
      setIsGenerating(false);
    }
  });

  // 处理生成
  const handleGenerate = useCallback(async () => {
    if (!selectedRole || !inputText.trim()) return;
    
    setIsGenerating(true);
    setAudioState(prev => ({ ...prev, isLoading: true }));
    
    generateVoice.mutate({
      roleId: selectedRole.id,
      text: inputText,
      language: selectedLanguage,
      style: styleInstructions,
      modelMappingId: selectedModel?.id
    });
  }, [selectedRole, inputText, selectedLanguage, styleInstructions, selectedModel, generateVoice]);

  // 音频控制
  const togglePlayPause = useCallback(() => {
    if (!audioRef.current || !audioState.url) return;
    
    if (audioState.isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
  }, [audioState.isPlaying, audioState.url]);

  const stopAudio = useCallback(() => {
    if (!audioRef.current) return;
    audioRef.current.pause();
    audioRef.current.currentTime = 0;
  }, []);

  const seekAudio = useCallback((time: number) => {
    if (!audioRef.current) return;
    audioRef.current.currentTime = time;
    setAudioState(prev => ({ ...prev, currentTime: time }));
  }, []);

  const changeVolume = useCallback((volume: number) => {
    if (!audioRef.current) return;
    audioRef.current.volume = volume;
    setAudioState(prev => ({ ...prev, volume }));
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const retryGeneration = useCallback(() => {
    if (selectedRole && inputText.trim()) {
      handleGenerate();
    }
  }, [selectedRole, inputText]);

  // 多说话者生成
  const generateMultiSpeaker = useCallback(async (segments: any[], speakers: any[]) => {
    if (!segments.length || !speakers.length) return;

    try {
      setIsGenerating(true);
      setError(null);
      setAudioState(prev => ({ ...prev, isLoading: true }));

      // 为每个段落生成语音
      const audioPromises = segments.map(async (segment) => {
        const speaker = speakers.find(s => s.id === segment.speakerId);
        if (!speaker?.role) throw new Error(`No role found for speaker ${speaker?.name}`);

        const result = await generateVoice.mutateAsync({
          text: segment.text,
          roleId: speaker.role.id,
          modelMappingId: selectedModel?.id || '',
        });

        return {
          segmentId: segment.id,
          speakerId: segment.speakerId,
          audioUrl: result.audioUrl,
          text: segment.text,
          order: segment.order,
        };
      });

      const audioResults = await Promise.all(audioPromises);

      // 记录到项目历史
      if (projectManager.currentProject) {
        await projectManager.addToHistory(
          'multi_speaker_generated',
          {
            segments: segments.map(s => ({
              text: s.text,
              speaker: speakers.find(sp => sp.id === s.speakerId)?.name,
            })),
            speakers: speakers.map(s => ({
              name: s.name,
              role: s.role?.name,
            })),
          },
          audioResults[0]?.audioUrl // 使用第一个音频作为代表
        );
      }

      // 设置第一个音频为当前播放
      if (audioResults.length > 0) {
        setAudioState(prev => ({
          ...prev,
          url: audioResults[0]!.audioUrl,
          isLoading: false,
          multiSpeakerResults: audioResults,
        }));
      }

      setIsGenerating(false);
      return audioResults;
    } catch (error) {
      console.error('Multi-speaker generation failed:', error);
      setError(error instanceof Error ? error.message : 'Multi-speaker generation failed');
      setAudioState(prev => ({ ...prev, isLoading: false }));
      setIsGenerating(false);
      throw error;
    }
  }, [generateVoice, selectedModel, projectManager]);

  // 音频事件处理
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handlePlay = () => setAudioState(prev => ({ ...prev, isPlaying: true }));
    const handlePause = () => setAudioState(prev => ({ ...prev, isPlaying: false }));
    const handleTimeUpdate = () => {
      setAudioState(prev => ({ 
        ...prev, 
        currentTime: audio.currentTime,
        duration: audio.duration || 0
      }));
    };
    const handleLoadStart = () => setAudioState(prev => ({ ...prev, isLoading: true }));
    const handleCanPlay = () => setAudioState(prev => ({ ...prev, isLoading: false }));

    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('loadedmetadata', handleTimeUpdate);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('canplay', handleCanPlay);

    return () => {
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('loadedmetadata', handleTimeUpdate);
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('canplay', handleCanPlay);
    };
  }, [audioState.url]);

  // 音量控制
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = audioState.volume;
    }
  }, [audioState.volume]);

  const contextValue: StudioContextType = {
    // 状态
    sidebarCollapsed,
    selectedMode,
    styleInstructions,
    inputText,
    selectedLanguage,
    selectedRole,
    selectedModel,
    isGenerating,
    audioState,
    error,

    // 操作
    setSidebarCollapsed,
    setSelectedMode,
    setStyleInstructions,
    setInputText,
    setSelectedLanguage,
    setSelectedRole,
    setSelectedModel,
    setIsGenerating,
    setAudioState,
    handleGenerate,
    togglePlayPause,
    stopAudio,
    seekAudio,
    changeVolume,
    clearError,
    retryGeneration,
    generateMultiSpeaker,
    generateVoice,

    // 项目管理
    ...projectManager
  };

  return (
    <StudioContext.Provider value={contextValue}>
      {children}
      {/* 隐藏的音频元素 */}
      {audioState.url && (
        <audio ref={audioRef} src={audioState.url} preload="metadata" />
      )}
    </StudioContext.Provider>
  );
};
