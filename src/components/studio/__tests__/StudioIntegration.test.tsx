import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { StudioProvider } from '../context/StudioContext';
import { StudioLayout } from '../layout/StudioLayout';
import { api } from '~/trpc/react';

// Mock tRPC with more comprehensive data
vi.mock('~/trpc/react', () => ({
  api: {
    tts: {
      generateVoiceSample: {
        useMutation: vi.fn(() => ({
          mutateAsync: vi.fn().mockResolvedValue({
            audioUrl: 'https://example.com/audio.wav',
          }),
          isLoading: false,
        })),
      },
    },
    language: {
      getAll: {
        useQuery: vi.fn(() => ({
          data: [
            { id: '1', name: 'English', code: 'en' },
            { id: '2', name: 'Chinese', code: 'zh' },
          ],
          isLoading: false,
        })),
      },
    },
    voiceRole: {
      getByLanguage: {
        useQuery: vi.fn(() => ({
          data: [
            { 
              id: '1', 
              name: 'Test Voice', 
              languageId: '1',
              language: { name: 'English', code: 'en' }
            },
          ],
          isLoading: false,
        })),
      },
    },
    modelMapping: {
      getByRole: {
        useQuery: vi.fn(() => ({
          data: [
            { 
              id: '1', 
              model: { 
                name: 'Test Model',
                provider: { name: 'Test Provider' }
              }
            },
          ],
          isLoading: false,
        })),
      },
    },
    project: {
      getAll: {
        useQuery: vi.fn(() => ({
          data: [],
          isLoading: false,
        })),
      },
      create: {
        useMutation: vi.fn(() => ({
          mutateAsync: vi.fn().mockResolvedValue({
            id: 'new-project-id',
            name: 'New Project',
          }),
        })),
      },
      update: {
        useMutation: vi.fn(() => ({
          mutateAsync: vi.fn(),
        })),
      },
      delete: {
        useMutation: vi.fn(() => ({
          mutateAsync: vi.fn(),
        })),
      },
      addHistory: {
        useMutation: vi.fn(() => ({
          mutateAsync: vi.fn(),
        })),
      },
    },
  },
}));

// Mock keyboard shortcuts hook
vi.mock('../hooks/useKeyboardShortcuts', () => ({
  useKeyboardShortcuts: vi.fn(),
}));

describe('Studio Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the complete studio interface', () => {
    render(
      <StudioProvider>
        <StudioLayout />
      </StudioProvider>
    );

    // Check for main sections
    expect(screen.getByText(/generation mode/i)).toBeInTheDocument();
    expect(screen.getByText(/single speaker/i)).toBeInTheDocument();
    expect(screen.getByText(/multi-speaker/i)).toBeInTheDocument();
    expect(screen.getByText(/batch processing/i)).toBeInTheDocument();
  });

  it('should switch between different modes', async () => {
    render(
      <StudioProvider>
        <StudioLayout />
      </StudioProvider>
    );

    // Start in single mode
    expect(screen.getByPlaceholderText(/enter text to generate/i)).toBeInTheDocument();

    // Switch to multi-speaker mode
    const multiRadio = screen.getByLabelText(/multi-speaker/i);
    fireEvent.click(multiRadio);

    await waitFor(() => {
      expect(screen.getByText(/speakers \(2\)/i)).toBeInTheDocument();
      expect(screen.getByText(/dialogue \(1 segments\)/i)).toBeInTheDocument();
    });

    // Switch to batch mode
    const batchRadio = screen.getByLabelText(/batch processing/i);
    fireEvent.click(batchRadio);

    await waitFor(() => {
      expect(screen.getByText(/batch input/i)).toBeInTheDocument();
      expect(screen.getByText(/choose file/i)).toBeInTheDocument();
    });
  });

  it('should handle single speaker generation flow', async () => {
    const mockMutateAsync = vi.fn().mockResolvedValue({
      audioUrl: 'https://example.com/audio.wav',
    });

    vi.mocked(api.tts.generateVoiceSample.useMutation).mockReturnValue({
      mutateAsync: mockMutateAsync,
      isLoading: false,
    } as any);

    render(
      <StudioProvider>
        <StudioLayout />
      </StudioProvider>
    );

    // Enter text
    const textArea = screen.getByPlaceholderText(/enter text to generate/i);
    fireEvent.change(textArea, { target: { value: 'Hello world' } });

    // Find and click generate button (it might be in the audio player section)
    const generateButton = screen.getByRole('button', { name: /generate/i });
    fireEvent.click(generateButton);

    // Should show loading state
    await waitFor(() => {
      expect(screen.getByText(/generating/i)).toBeInTheDocument();
    });

    // Should complete and show audio controls
    await waitFor(() => {
      expect(mockMutateAsync).toHaveBeenCalledWith({
        text: 'Hello world',
        roleId: '',
        modelMappingId: '',
      });
    });
  });

  it('should handle multi-speaker generation flow', async () => {
    render(
      <StudioProvider>
        <StudioLayout />
      </StudioProvider>
    );

    // Switch to multi-speaker mode
    const multiRadio = screen.getByLabelText(/multi-speaker/i);
    fireEvent.click(multiRadio);

    await waitFor(() => {
      expect(screen.getByText(/speakers \(2\)/i)).toBeInTheDocument();
    });

    // Add text to the first segment
    const segmentTextArea = screen.getByPlaceholderText(/enter dialogue text/i);
    fireEvent.change(segmentTextArea, { target: { value: 'Hello from speaker 1' } });

    // The generate button should be available
    const generateButton = screen.getByRole('button', { name: /generate dialogue/i });
    expect(generateButton).toBeInTheDocument();
  });

  it('should handle batch processing flow', async () => {
    render(
      <StudioProvider>
        <StudioLayout />
      </StudioProvider>
    );

    // Switch to batch mode
    const batchRadio = screen.getByLabelText(/batch processing/i);
    fireEvent.click(batchRadio);

    await waitFor(() => {
      expect(screen.getByText(/batch input/i)).toBeInTheDocument();
    });

    // Add text manually
    const batchTextArea = screen.getByPlaceholderText(/enter text to generate/i);
    fireEvent.change(batchTextArea, { target: { value: 'Batch text item' } });

    const addButton = screen.getByRole('button', { name: /\+/i });
    fireEvent.click(addButton);

    // Should show the batch item
    await waitFor(() => {
      expect(screen.getByText('Batch text item')).toBeInTheDocument();
    });
  });

  it('should handle error states gracefully', async () => {
    const mockMutateAsync = vi.fn().mockRejectedValue(new Error('API Error'));

    vi.mocked(api.tts.generateVoiceSample.useMutation).mockReturnValue({
      mutateAsync: mockMutateAsync,
      isLoading: false,
    } as any);

    render(
      <StudioProvider>
        <StudioLayout />
      </StudioProvider>
    );

    // Enter text and try to generate
    const textArea = screen.getByPlaceholderText(/enter text to generate/i);
    fireEvent.change(textArea, { target: { value: 'Test text' } });

    const generateButton = screen.getByRole('button', { name: /generate/i });
    fireEvent.click(generateButton);

    // Should show error
    await waitFor(() => {
      expect(screen.getByText(/api error/i)).toBeInTheDocument();
    });
  });

  it('should handle keyboard shortcuts', () => {
    const mockUseKeyboardShortcuts = vi.mocked(
      require('../hooks/useKeyboardShortcuts').useKeyboardShortcuts
    );

    render(
      <StudioProvider>
        <StudioLayout />
      </StudioProvider>
    );

    // Verify keyboard shortcuts hook was called
    expect(mockUseKeyboardShortcuts).toHaveBeenCalled();
  });

  it('should persist state across mode switches', async () => {
    render(
      <StudioProvider>
        <StudioLayout />
      </StudioProvider>
    );

    // Enter text in single mode
    const textArea = screen.getByPlaceholderText(/enter text to generate/i);
    fireEvent.change(textArea, { target: { value: 'Persistent text' } });

    // Switch to multi mode and back
    const multiRadio = screen.getByLabelText(/multi-speaker/i);
    fireEvent.click(multiRadio);

    const singleRadio = screen.getByLabelText(/single speaker/i);
    fireEvent.click(singleRadio);

    // Text should still be there
    await waitFor(() => {
      const restoredTextArea = screen.getByPlaceholderText(/enter text to generate/i);
      expect(restoredTextArea).toHaveValue('Persistent text');
    });
  });
});
