import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AudioPlayer } from '../features/AudioPlayer';

// Mock HTML Audio API
const mockAudio = {
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  load: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  currentTime: 0,
  duration: 100,
  volume: 1,
  paused: true,
  ended: false,
};

Object.defineProperty(window, 'Audio', {
  writable: true,
  value: vi.fn().mockImplementation(() => mockAudio),
});

// Mock URL.createObjectURL
Object.defineProperty(window.URL, 'createObjectURL', {
  writable: true,
  value: vi.fn().mockReturnValue('blob:mock-url'),
});

Object.defineProperty(window.URL, 'revokeObjectURL', {
  writable: true,
  value: vi.fn(),
});

describe('AudioPlayer', () => {
  const defaultProps = {
    audioState: {
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      url: null,
      volume: 1,
      isLoading: false,
    },
    onPlayPause: vi.fn(),
    onSeek: vi.fn(),
    onVolumeChange: vi.fn(),
    onDownload: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render with no audio', () => {
    render(<AudioPlayer {...defaultProps} />);
    
    expect(screen.getByText('No audio generated yet')).toBeInTheDocument();
    expect(screen.getByText('Generate speech to see audio controls')).toBeInTheDocument();
  });

  it('should render loading state', () => {
    const props = {
      ...defaultProps,
      audioState: {
        ...defaultProps.audioState,
        isLoading: true,
      },
    };

    render(<AudioPlayer {...props} />);
    
    expect(screen.getByText('Generating audio...')).toBeInTheDocument();
  });

  it('should render audio controls when audio is available', () => {
    const props = {
      ...defaultProps,
      audioState: {
        ...defaultProps.audioState,
        url: 'test-audio-url',
        duration: 120,
      },
    };

    render(<AudioPlayer {...props} />);
    
    expect(screen.getByRole('button', { name: /play/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /download/i })).toBeInTheDocument();
    expect(screen.getByText('02:00')).toBeInTheDocument(); // Duration
  });

  it('should call onPlayPause when play button is clicked', () => {
    const onPlayPause = vi.fn();
    const props = {
      ...defaultProps,
      audioState: {
        ...defaultProps.audioState,
        url: 'test-audio-url',
      },
      onPlayPause,
    };

    render(<AudioPlayer {...props} />);
    
    fireEvent.click(screen.getByRole('button', { name: /play/i }));
    expect(onPlayPause).toHaveBeenCalledTimes(1);
  });

  it('should show pause button when playing', () => {
    const props = {
      ...defaultProps,
      audioState: {
        ...defaultProps.audioState,
        url: 'test-audio-url',
        isPlaying: true,
      },
    };

    render(<AudioPlayer {...props} />);
    
    expect(screen.getByRole('button', { name: /pause/i })).toBeInTheDocument();
  });

  it('should call onDownload when download button is clicked', () => {
    const onDownload = vi.fn();
    const props = {
      ...defaultProps,
      audioState: {
        ...defaultProps.audioState,
        url: 'test-audio-url',
      },
      onDownload,
    };

    render(<AudioPlayer {...props} />);
    
    fireEvent.click(screen.getByRole('button', { name: /download/i }));
    expect(onDownload).toHaveBeenCalledTimes(1);
  });

  it('should display current time and duration correctly', () => {
    const props = {
      ...defaultProps,
      audioState: {
        ...defaultProps.audioState,
        url: 'test-audio-url',
        currentTime: 30,
        duration: 120,
      },
    };

    render(<AudioPlayer {...props} />);
    
    expect(screen.getByText('00:30')).toBeInTheDocument(); // Current time
    expect(screen.getByText('02:00')).toBeInTheDocument(); // Duration
  });

  it('should handle volume changes', () => {
    const onVolumeChange = vi.fn();
    const props = {
      ...defaultProps,
      audioState: {
        ...defaultProps.audioState,
        url: 'test-audio-url',
        volume: 0.5,
      },
      onVolumeChange,
    };

    render(<AudioPlayer {...props} />);
    
    const volumeSlider = screen.getByRole('slider');
    fireEvent.change(volumeSlider, { target: { value: '0.8' } });
    
    expect(onVolumeChange).toHaveBeenCalledWith(0.8);
  });

  it('should handle seek changes', () => {
    const onSeek = vi.fn();
    const props = {
      ...defaultProps,
      audioState: {
        ...defaultProps.audioState,
        url: 'test-audio-url',
        duration: 120,
        currentTime: 30,
      },
      onSeek,
    };

    render(<AudioPlayer {...props} />);
    
    const progressSlider = screen.getByDisplayValue('25'); // 30/120 * 100 = 25%
    fireEvent.change(progressSlider, { target: { value: '50' } });
    
    expect(onSeek).toHaveBeenCalledWith(60); // 50% of 120 seconds
  });

  it('should format time correctly', () => {
    const props = {
      ...defaultProps,
      audioState: {
        ...defaultProps.audioState,
        url: 'test-audio-url',
        currentTime: 3661, // 1 hour, 1 minute, 1 second
        duration: 7322, // 2 hours, 2 minutes, 2 seconds
      },
    };

    render(<AudioPlayer {...props} />);
    
    expect(screen.getByText('1:01:01')).toBeInTheDocument(); // Current time
    expect(screen.getByText('2:02:02')).toBeInTheDocument(); // Duration
  });

  it('should handle multi-speaker results', () => {
    const props = {
      ...defaultProps,
      audioState: {
        ...defaultProps.audioState,
        url: 'test-audio-url',
        multiSpeakerResults: [
          {
            segmentId: '1',
            speakerId: '1',
            audioUrl: 'audio1.wav',
            text: 'Hello',
            order: 0,
          },
          {
            segmentId: '2',
            speakerId: '2',
            audioUrl: 'audio2.wav',
            text: 'World',
            order: 1,
          },
        ],
      },
    };

    render(<AudioPlayer {...props} />);
    
    expect(screen.getByText('Multi-Speaker Dialogue')).toBeInTheDocument();
    expect(screen.getByText('2 segments')).toBeInTheDocument();
  });
});
