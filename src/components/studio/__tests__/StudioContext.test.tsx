import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { StudioProvider, useStudio } from '../context/StudioContext';
import { api } from '~/trpc/react';

// Mock tRPC
vi.mock('~/trpc/react', () => ({
  api: {
    tts: {
      generateVoiceSample: {
        useMutation: vi.fn(() => ({
          mutateAsync: vi.fn(),
          isLoading: false,
        })),
      },
    },
    language: {
      getAll: {
        useQuery: vi.fn(() => ({
          data: [
            { id: '1', name: 'English', code: 'en' },
            { id: '2', name: 'Chinese', code: 'zh' },
          ],
          isLoading: false,
        })),
      },
    },
    voiceRole: {
      getByLanguage: {
        useQuery: vi.fn(() => ({
          data: [
            { id: '1', name: 'Test Voice', languageId: '1' },
          ],
          isLoading: false,
        })),
      },
    },
    modelMapping: {
      getByRole: {
        useQuery: vi.fn(() => ({
          data: [
            { id: '1', model: { name: 'Test Model' } },
          ],
          isLoading: false,
        })),
      },
    },
    project: {
      getAll: {
        useQuery: vi.fn(() => ({
          data: [],
          isLoading: false,
        })),
      },
      create: {
        useMutation: vi.fn(() => ({
          mutateAsync: vi.fn(),
        })),
      },
      update: {
        useMutation: vi.fn(() => ({
          mutateAsync: vi.fn(),
        })),
      },
      delete: {
        useMutation: vi.fn(() => ({
          mutateAsync: vi.fn(),
        })),
      },
    },
  },
}));

// Test component to access context
const TestComponent = () => {
  const {
    selectedLanguage,
    selectedRole,
    selectedMode,
    inputText,
    setSelectedLanguage,
    setSelectedRole,
    setSelectedMode,
    setInputText,
    handleGenerate,
    isGenerating,
    error,
  } = useStudio();

  return (
    <div>
      <div data-testid="selected-language">{selectedLanguage}</div>
      <div data-testid="selected-mode">{selectedMode}</div>
      <div data-testid="input-text">{inputText}</div>
      <div data-testid="is-generating">{isGenerating.toString()}</div>
      <div data-testid="error">{error || 'no-error'}</div>
      
      <button onClick={() => setSelectedLanguage('zh')}>
        Change Language
      </button>
      <button onClick={() => setSelectedMode('multi')}>
        Change Mode
      </button>
      <button onClick={() => setInputText('Test text')}>
        Set Text
      </button>
      <button onClick={handleGenerate}>
        Generate
      </button>
    </div>
  );
};

describe('StudioContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should provide initial state', () => {
    render(
      <StudioProvider>
        <TestComponent />
      </StudioProvider>
    );

    expect(screen.getByTestId('selected-language')).toHaveTextContent('en');
    expect(screen.getByTestId('selected-mode')).toHaveTextContent('single');
    expect(screen.getByTestId('input-text')).toHaveTextContent('');
    expect(screen.getByTestId('is-generating')).toHaveTextContent('false');
    expect(screen.getByTestId('error')).toHaveTextContent('no-error');
  });

  it('should update language when setSelectedLanguage is called', () => {
    render(
      <StudioProvider>
        <TestComponent />
      </StudioProvider>
    );

    fireEvent.click(screen.getByText('Change Language'));
    expect(screen.getByTestId('selected-language')).toHaveTextContent('zh');
  });

  it('should update mode when setSelectedMode is called', () => {
    render(
      <StudioProvider>
        <TestComponent />
      </StudioProvider>
    );

    fireEvent.click(screen.getByText('Change Mode'));
    expect(screen.getByTestId('selected-mode')).toHaveTextContent('multi');
  });

  it('should update input text when setInputText is called', () => {
    render(
      <StudioProvider>
        <TestComponent />
      </StudioProvider>
    );

    fireEvent.click(screen.getByText('Set Text'));
    expect(screen.getByTestId('input-text')).toHaveTextContent('Test text');
  });

  it('should handle generation process', async () => {
    const mockMutateAsync = vi.fn().mockResolvedValue({
      audioUrl: 'test-audio-url',
    });

    vi.mocked(api.tts.generateVoiceSample.useMutation).mockReturnValue({
      mutateAsync: mockMutateAsync,
      isLoading: false,
    } as any);

    render(
      <StudioProvider>
        <TestComponent />
      </StudioProvider>
    );

    // Set up required data
    fireEvent.click(screen.getByText('Set Text'));
    
    // Trigger generation
    fireEvent.click(screen.getByText('Generate'));

    // Should show generating state
    await waitFor(() => {
      expect(screen.getByTestId('is-generating')).toHaveTextContent('true');
    });

    // Wait for completion
    await waitFor(() => {
      expect(screen.getByTestId('is-generating')).toHaveTextContent('false');
    });
  });

  it('should handle generation error', async () => {
    const mockMutateAsync = vi.fn().mockRejectedValue(new Error('Generation failed'));

    vi.mocked(api.tts.generateVoiceSample.useMutation).mockReturnValue({
      mutateAsync: mockMutateAsync,
      isLoading: false,
    } as any);

    render(
      <StudioProvider>
        <TestComponent />
      </StudioProvider>
    );

    // Set up required data
    fireEvent.click(screen.getByText('Set Text'));
    
    // Trigger generation
    fireEvent.click(screen.getByText('Generate'));

    // Wait for error
    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent('Generation failed');
    });
  });

  it('should not generate without required data', async () => {
    const mockMutateAsync = vi.fn();

    vi.mocked(api.tts.generateVoiceSample.useMutation).mockReturnValue({
      mutateAsync: mockMutateAsync,
      isLoading: false,
    } as any);

    render(
      <StudioProvider>
        <TestComponent />
      </StudioProvider>
    );

    // Try to generate without text
    fireEvent.click(screen.getByText('Generate'));

    // Should not call the API
    expect(mockMutateAsync).not.toHaveBeenCalled();
  });
});
