import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TextEditor } from '../features/TextEditor';

describe('TextEditor', () => {
  const defaultProps = {
    styleInstructions: '',
    inputText: '',
    onStyleInstructionsChange: vi.fn(),
    onInputTextChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render style instructions textarea', () => {
    render(<TextEditor {...defaultProps} />);
    
    expect(screen.getByPlaceholderText(/style instructions/i)).toBeInTheDocument();
  });

  it('should render main text textarea', () => {
    render(<TextEditor {...defaultProps} />);
    
    expect(screen.getByPlaceholderText(/enter text to generate/i)).toBeInTheDocument();
  });

  it('should display current values', () => {
    const props = {
      ...defaultProps,
      styleInstructions: 'Test style instructions',
      inputText: 'Test input text',
    };

    render(<TextEditor {...props} />);
    
    expect(screen.getByDisplayValue('Test style instructions')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test input text')).toBeInTheDocument();
  });

  it('should call onStyleInstructionsChange when style instructions change', () => {
    const onStyleInstructionsChange = vi.fn();
    const props = {
      ...defaultProps,
      onStyleInstructionsChange,
    };

    render(<TextEditor {...props} />);
    
    const styleTextarea = screen.getByPlaceholderText(/style instructions/i);
    fireEvent.change(styleTextarea, { target: { value: 'New style instructions' } });
    
    expect(onStyleInstructionsChange).toHaveBeenCalledWith('New style instructions');
  });

  it('should call onInputTextChange when input text changes', () => {
    const onInputTextChange = vi.fn();
    const props = {
      ...defaultProps,
      onInputTextChange,
    };

    render(<TextEditor {...props} />);
    
    const inputTextarea = screen.getByPlaceholderText(/enter text to generate/i);
    fireEvent.change(inputTextarea, { target: { value: 'New input text' } });
    
    expect(onInputTextChange).toHaveBeenCalledWith('New input text');
  });

  it('should show character count for input text', () => {
    const props = {
      ...defaultProps,
      inputText: 'Hello world',
    };

    render(<TextEditor {...props} />);
    
    expect(screen.getByText('11 characters')).toBeInTheDocument();
  });

  it('should show word count for input text', () => {
    const props = {
      ...defaultProps,
      inputText: 'Hello world test',
    };

    render(<TextEditor {...props} />);
    
    expect(screen.getByText('3 words')).toBeInTheDocument();
  });

  it('should handle empty text correctly', () => {
    render(<TextEditor {...defaultProps} />);
    
    expect(screen.getByText('0 characters')).toBeInTheDocument();
    expect(screen.getByText('0 words')).toBeInTheDocument();
  });

  it('should handle multiline text correctly', () => {
    const props = {
      ...defaultProps,
      inputText: 'Line 1\nLine 2\nLine 3',
    };

    render(<TextEditor {...props} />);
    
    expect(screen.getByText('19 characters')).toBeInTheDocument();
    expect(screen.getByText('6 words')).toBeInTheDocument();
  });

  it('should auto-resize textarea based on content', async () => {
    const props = {
      ...defaultProps,
      inputText: 'Short text',
    };

    const { rerender } = render(<TextEditor {...props} />);
    
    const textarea = screen.getByPlaceholderText(/enter text to generate/i);
    const initialHeight = textarea.style.height;

    // Update with longer text
    rerender(<TextEditor {...props} inputText="This is a much longer text that should cause the textarea to expand to accommodate the additional content and provide a better user experience" />);
    
    await waitFor(() => {
      expect(textarea.style.height).not.toBe(initialHeight);
    });
  });

  it('should handle keyboard shortcuts', () => {
    const onInputTextChange = vi.fn();
    const props = {
      ...defaultProps,
      onInputTextChange,
    };

    render(<TextEditor {...props} />);
    
    const textarea = screen.getByPlaceholderText(/enter text to generate/i);
    
    // Test Ctrl+A (select all)
    fireEvent.keyDown(textarea, { key: 'a', ctrlKey: true });
    
    // Test Ctrl+Z (undo) - this would be handled by the browser
    fireEvent.keyDown(textarea, { key: 'z', ctrlKey: true });
    
    // Test Tab key
    fireEvent.keyDown(textarea, { key: 'Tab' });
  });

  it('should maintain focus when content changes', () => {
    const props = {
      ...defaultProps,
      inputText: 'Initial text',
    };

    const { rerender } = render(<TextEditor {...props} />);
    
    const textarea = screen.getByPlaceholderText(/enter text to generate/i);
    textarea.focus();
    
    expect(document.activeElement).toBe(textarea);
    
    // Update content
    rerender(<TextEditor {...props} inputText="Updated text" />);
    
    // Focus should be maintained
    expect(document.activeElement).toBe(textarea);
  });

  it('should handle paste events', () => {
    const onInputTextChange = vi.fn();
    const props = {
      ...defaultProps,
      onInputTextChange,
    };

    render(<TextEditor {...props} />);
    
    const textarea = screen.getByPlaceholderText(/enter text to generate/i);
    
    // Simulate paste event
    fireEvent.paste(textarea, {
      clipboardData: {
        getData: () => 'Pasted content',
      },
    });
    
    // The actual paste handling would be done by the browser
    // We're just testing that the event can be fired
  });

  it('should show style instructions section', () => {
    render(<TextEditor {...defaultProps} />);
    
    expect(screen.getByText(/style instructions/i)).toBeInTheDocument();
  });

  it('should show main text section', () => {
    render(<TextEditor {...defaultProps} />);
    
    expect(screen.getByText(/text to generate/i)).toBeInTheDocument();
  });
});
