# Studio Component Tests

This directory contains comprehensive unit and integration tests for the Studio page components.

## Test Structure

### Unit Tests
- **StudioContext.test.tsx** - Tests the main context provider and state management
- **AudioPlayer.test.tsx** - Tests audio playback functionality and controls
- **TextEditor.test.tsx** - Tests text input and editing features

### Integration Tests
- **StudioIntegration.test.tsx** - Tests the complete studio workflow and component interactions

## Running Tests

### Install Dependencies
```bash
npm install
```

### Run All Tests
```bash
npm run test
```

### Run Tests in Watch Mode
```bash
npm run test -- --watch
```

### Run Tests with UI
```bash
npm run test:ui
```

### Run Tests with Coverage
```bash
npm run test:coverage
```

### Run Specific Test File
```bash
npm run test -- StudioContext.test.tsx
```

## Test Coverage

The tests cover:

### Core Functionality
- ✅ State management (Context API)
- ✅ Audio playback and controls
- ✅ Text editing and validation
- ✅ Mode switching (single/multi/batch)
- ✅ API integration (mocked)
- ✅ Error handling
- ✅ User interactions

### User Workflows
- ✅ Single speaker generation
- ✅ Multi-speaker dialogue creation
- ✅ Batch processing
- ✅ Project management
- ✅ History tracking
- ✅ Keyboard shortcuts

### Edge Cases
- ✅ Empty states
- ✅ Loading states
- ✅ Error states
- ✅ Network failures
- ✅ Invalid inputs
- ✅ Browser compatibility

## Mocking Strategy

### External Dependencies
- **tRPC API calls** - Mocked with predictable responses
- **Audio API** - Mocked HTML5 Audio element
- **File operations** - Mocked File API and URL.createObjectURL
- **Local storage** - Mocked browser storage APIs

### Component Dependencies
- **Keyboard shortcuts** - Mocked hook implementation
- **Project management** - Mocked with in-memory state

## Test Environment

- **Framework**: Vitest
- **Testing Library**: React Testing Library
- **Environment**: jsdom
- **Mocking**: Vitest vi functions

## Best Practices

### Writing Tests
1. Use descriptive test names that explain the expected behavior
2. Follow the Arrange-Act-Assert pattern
3. Mock external dependencies at the module level
4. Test user interactions, not implementation details
5. Use `waitFor` for async operations

### Maintaining Tests
1. Update tests when component APIs change
2. Add tests for new features
3. Remove tests for deprecated functionality
4. Keep mocks up to date with real APIs

## Common Issues

### Test Failures
- **Async operations**: Use `waitFor` for state changes
- **Mock issues**: Ensure mocks are cleared between tests
- **DOM cleanup**: React Testing Library handles this automatically

### Performance
- **Large test suites**: Use `test.concurrent` for independent tests
- **Slow tests**: Check for unnecessary `waitFor` calls
- **Memory leaks**: Ensure proper cleanup in `beforeEach`/`afterEach`

## Contributing

When adding new components or features:

1. Write tests for new functionality
2. Update existing tests if behavior changes
3. Maintain test coverage above 80%
4. Follow the existing test patterns and naming conventions

## Debugging Tests

### VS Code
1. Install the Vitest extension
2. Use the test explorer to run individual tests
3. Set breakpoints in test files

### Command Line
```bash
# Run tests in debug mode
npm run test -- --inspect-brk

# Run specific test with verbose output
npm run test -- --reporter=verbose StudioContext.test.tsx
```

### Browser DevTools
```bash
# Open tests in browser with UI
npm run test:ui
```

This will open a web interface where you can inspect test results, coverage, and debug failing tests.
