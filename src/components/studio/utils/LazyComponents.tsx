'use client';

import React, { Suspense, lazy, useCallback, useEffect } from 'react';
import { Loader2 } from 'lucide-react';

// 懒加载组件
const LazyMultiSpeakerEditor = lazy(() => 
  import('../features/MultiSpeakerEditor').then(module => ({
    default: module.MultiSpeakerEditor
  }))
);

const LazyBatchProcessor = lazy(() => 
  import('../features/BatchProcessor').then(module => ({
    default: module.BatchProcessor
  }))
);

const LazyHistoryPanel = lazy(() => 
  import('../ui/HistoryPanel').then(module => ({
    default: module.HistoryPanel
  }))
);

const LazyProjectDialog = lazy(() => 
  import('../ui/ProjectDialog').then(module => ({
    default: module.ProjectDialog
  }))
);

// 加载中组件
const LoadingSpinner = ({ message = 'Loading...' }: { message?: string }) => (
  <div className="flex items-center justify-center p-8">
    <div className="flex items-center space-x-2">
      <Loader2 className="h-5 w-5 animate-spin" />
      <span className="text-sm text-gray-600">{message}</span>
    </div>
  </div>
);

// 错误边界组件
class LazyComponentErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy component loading error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="flex items-center justify-center p-8">
          <div className="text-center">
            <p className="text-red-600 mb-2">Failed to load component</p>
            <button 
              onClick={() => this.setState({ hasError: false })}
              className="text-sm text-blue-600 hover:underline"
            >
              Try again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// 包装器组件
const withLazyLoading = <P extends object>(
  LazyComponent: React.LazyExoticComponent<React.ComponentType<P>>,
  loadingMessage?: string,
  fallback?: React.ReactNode
) => {
  return React.forwardRef<any, P>((props, ref) => (
    <LazyComponentErrorBoundary fallback={fallback}>
      <Suspense fallback={<LoadingSpinner message={loadingMessage} />}>
        <LazyComponent {...props} ref={ref} />
      </Suspense>
    </LazyComponentErrorBoundary>
  ));
};

// 导出懒加载组件
export const MultiSpeakerEditor = withLazyLoading(
  LazyMultiSpeakerEditor,
  'Loading multi-speaker editor...'
);

export const BatchProcessor = withLazyLoading(
  LazyBatchProcessor,
  'Loading batch processor...'
);

export const HistoryPanel = withLazyLoading(
  LazyHistoryPanel,
  'Loading history...'
);

export const ProjectDialog = withLazyLoading(
  LazyProjectDialog,
  'Loading project dialog...'
);

// 预加载函数
export const preloadComponents = {
  multiSpeaker: () => import('../features/MultiSpeakerEditor'),
  batch: () => import('../features/BatchProcessor'),
  history: () => import('../ui/HistoryPanel'),
  project: () => import('../ui/ProjectDialog'),
};

// 预加载钩子
export const usePreloadComponents = () => {
  const preloadMultiSpeaker = useCallback(() => {
    preloadComponents.multiSpeaker();
  }, []);

  const preloadBatch = useCallback(() => {
    preloadComponents.batch();
  }, []);

  const preloadHistory = useCallback(() => {
    preloadComponents.history();
  }, []);

  const preloadProject = useCallback(() => {
    preloadComponents.project();
  }, []);

  return {
    preloadMultiSpeaker,
    preloadBatch,
    preloadHistory,
    preloadProject,
  };
};

// 智能预加载钩子 - 基于用户行为预加载
export const useSmartPreload = () => {
  const { preloadMultiSpeaker, preloadBatch, preloadHistory, preloadProject } = usePreloadComponents();

  useEffect(() => {
    // 延迟预加载非关键组件
    const timer = setTimeout(() => {
      // 预加载历史面板（用户可能会查看）
      preloadHistory();
      
      // 预加载项目对话框（用户可能会创建项目）
      preloadProject();
    }, 2000);

    return () => clearTimeout(timer);
  }, [preloadHistory, preloadProject]);

  // 鼠标悬停时预加载
  const handleModeHover = useCallback((mode: 'multi' | 'batch') => {
    if (mode === 'multi') {
      preloadMultiSpeaker();
    } else if (mode === 'batch') {
      preloadBatch();
    }
  }, [preloadMultiSpeaker, preloadBatch]);

  return { handleModeHover };
};

// 性能监控钩子
export const usePerformanceMonitor = () => {
  useEffect(() => {
    // 监控组件加载时间
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'measure') {
          console.log(`Component load time: ${entry.name} - ${entry.duration}ms`);
        }
      }
    });

    observer.observe({ entryTypes: ['measure'] });

    return () => observer.disconnect();
  }, []);

  const measureComponentLoad = useCallback((componentName: string) => {
    performance.mark(`${componentName}-start`);
    
    return () => {
      performance.mark(`${componentName}-end`);
      performance.measure(
        `${componentName}-load`,
        `${componentName}-start`,
        `${componentName}-end`
      );
    };
  }, []);

  return { measureComponentLoad };
};
