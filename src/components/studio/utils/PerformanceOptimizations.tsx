'use client';

import React, { useMemo, useCallback, useRef, useEffect, useState } from 'react';

// 音频缓存管理
class AudioCache {
  private cache = new Map<string, HTMLAudioElement>();
  private maxSize = 10; // 最大缓存数量

  get(url: string): HTMLAudioElement | null {
    return this.cache.get(url) || null;
  }

  set(url: string, audio: HTMLAudioElement): void {
    // 如果缓存已满，删除最旧的项目
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        const oldAudio = this.cache.get(firstKey);
        if (oldAudio) {
          oldAudio.pause();
          oldAudio.src = '';
        }
        this.cache.delete(firstKey);
      }
    }
    this.cache.set(url, audio);
  }

  clear(): void {
    this.cache.forEach(audio => {
      audio.pause();
      audio.src = '';
    });
    this.cache.clear();
  }

  remove(url: string): void {
    const audio = this.cache.get(url);
    if (audio) {
      audio.pause();
      audio.src = '';
      this.cache.delete(url);
    }
  }
}

// 全局音频缓存实例
export const audioCache = new AudioCache();

// 防抖钩子
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// 节流钩子
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef(Date.now());

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
};

// 虚拟化列表钩子
export const useVirtualization = (
  items: any[],
  itemHeight: number,
  containerHeight: number
) => {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );

    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight,
    };
  }, [items, itemHeight, containerHeight, scrollTop]);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  return {
    visibleItems,
    handleScroll,
  };
};

// 内存使用监控
export const useMemoryMonitor = () => {
  const [memoryInfo, setMemoryInfo] = useState<{
    used: number;
    total: number;
    percentage: number;
  } | null>(null);

  useEffect(() => {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const used = memory.usedJSHeapSize;
        const total = memory.totalJSHeapSize;
        const percentage = (used / total) * 100;

        setMemoryInfo({ used, total, percentage });

        // 警告高内存使用
        if (percentage > 80) {
          console.warn('High memory usage detected:', percentage.toFixed(2) + '%');
        }
      }
    };

    updateMemoryInfo();
    const interval = setInterval(updateMemoryInfo, 5000);

    return () => clearInterval(interval);
  }, []);

  return memoryInfo;
};

// 图片懒加载钩子
export const useLazyImage = (src: string) => {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry?.isIntersecting) {
          const img = new Image();
          img.onload = () => {
            setImageSrc(src);
            setIsLoading(false);
          };
          img.onerror = () => {
            setError('Failed to load image');
            setIsLoading(false);
          };
          img.src = src;
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [src]);

  return { imageSrc, isLoading, error, imgRef };
};

// 组件缓存钩子
export const useComponentCache = <T>(
  key: string,
  factory: () => T,
  deps: React.DependencyList
): T => {
  const cache = useRef(new Map<string, T>());

  return useMemo(() => {
    const cacheKey = `${key}-${JSON.stringify(deps)}`;
    
    if (cache.current.has(cacheKey)) {
      return cache.current.get(cacheKey)!;
    }

    const result = factory();
    cache.current.set(cacheKey, result);

    // 清理旧缓存
    if (cache.current.size > 50) {
      const keys = Array.from(cache.current.keys());
      const keysToDelete = keys.slice(0, 25);
      keysToDelete.forEach(k => cache.current.delete(k));
    }

    return result;
  }, deps);
};

// 批量状态更新钩子
export const useBatchedUpdates = <T>(initialState: T) => {
  const [state, setState] = useState(initialState);
  const pendingUpdates = useRef<Partial<T>[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const batchUpdate = useCallback((update: Partial<T>) => {
    pendingUpdates.current.push(update);

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setState(prevState => {
        let newState = { ...prevState };
        pendingUpdates.current.forEach(update => {
          newState = { ...newState, ...update };
        });
        pendingUpdates.current = [];
        return newState;
      });
    }, 16); // 一帧的时间
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return [state, batchUpdate] as const;
};

// Web Worker 钩子
export const useWebWorker = (workerScript: string) => {
  const workerRef = useRef<Worker | null>(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    try {
      workerRef.current = new Worker(workerScript);
      workerRef.current.onmessage = () => setIsReady(true);
      workerRef.current.postMessage({ type: 'init' });
    } catch (error) {
      console.error('Failed to create worker:', error);
    }

    return () => {
      if (workerRef.current) {
        workerRef.current.terminate();
      }
    };
  }, [workerScript]);

  const postMessage = useCallback((message: any) => {
    if (workerRef.current && isReady) {
      workerRef.current.postMessage(message);
    }
  }, [isReady]);

  const onMessage = useCallback((callback: (event: MessageEvent) => void) => {
    if (workerRef.current) {
      workerRef.current.onmessage = callback;
    }
  }, []);

  return { postMessage, onMessage, isReady };
};

// 性能分析钩子
export const usePerformanceProfiler = (componentName: string) => {
  const renderCount = useRef(0);
  const startTime = useRef(Date.now());

  useEffect(() => {
    renderCount.current += 1;
  });

  useEffect(() => {
    const endTime = Date.now();
    const duration = endTime - startTime.current;
    
    console.log(`${componentName} - Renders: ${renderCount.current}, Mount time: ${duration}ms`);
    
    return () => {
      console.log(`${componentName} - Unmounted after ${renderCount.current} renders`);
    };
  }, [componentName]);

  return { renderCount: renderCount.current };
};
