'use client';

import React from 'react';
import { Button } from '~/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '~/components/ui/avatar';
import { Separator } from '~/components/ui/separator';
import { useSession, signOut } from 'next-auth/react';
import {
  User,
  Settings,
  LogOut,
  Mail,
  Calendar,
  Shield,
  Bell,
  Palette,
  Globe,
  Key,
  Download,
  Trash2
} from 'lucide-react';

export const UserSettingsPanel: React.FC = () => {
  const { data: session } = useSession();

  if (!session?.user) {
    return (
      <div className="p-6 text-center">
        <User className="h-12 w-12 mx-auto text-gray-400 mb-4" />
        <p className="text-gray-500 dark:text-gray-400 mb-4">
          Please sign in to access user settings
        </p>
        <Button>Sign In</Button>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* 用户信息头部 */}
      <div className="flex items-center space-x-4">
        <Avatar className="h-16 w-16 ring-2 ring-blue-200 dark:ring-blue-700">
          <AvatarImage src={session.user.image || ''} alt={session.user.name || ''} />
          <AvatarFallback className="bg-gradient-to-br from-blue-500 to-indigo-600 text-white text-lg font-semibold">
            {session.user.name?.charAt(0)?.toUpperCase() || session.user.email?.charAt(0)?.toUpperCase() || 'U'}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
            {session.user.name || 'User'}
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 truncate flex items-center">
            <Mail className="h-3 w-3 mr-1" />
            {session.user.email}
          </p>
          <p className="text-xs text-gray-400 dark:text-gray-500 flex items-center mt-1">
            <Calendar className="h-3 w-3 mr-1" />
            Member since {new Date().getFullYear()}
          </p>
        </div>
      </div>

      <Separator />

      {/* 账户设置 */}
      <div className="space-y-3">
        <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center">
          <User className="h-4 w-4 mr-2" />
          Account Settings
        </h4>
        <div className="space-y-2">
          <Button variant="ghost" className="w-full justify-start text-sm">
            <User className="h-4 w-4 mr-3" />
            Edit Profile
          </Button>
          <Button variant="ghost" className="w-full justify-start text-sm">
            <Shield className="h-4 w-4 mr-3" />
            Privacy & Security
          </Button>
          <Button variant="ghost" className="w-full justify-start text-sm">
            <Key className="h-4 w-4 mr-3" />
            API Keys
          </Button>
        </div>
      </div>

      <Separator />

      {/* 应用设置 */}
      <div className="space-y-3">
        <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center">
          <Settings className="h-4 w-4 mr-2" />
          App Settings
        </h4>
        <div className="space-y-2">
          <Button variant="ghost" className="w-full justify-start text-sm">
            <Bell className="h-4 w-4 mr-3" />
            Notifications
          </Button>
          <Button variant="ghost" className="w-full justify-start text-sm">
            <Palette className="h-4 w-4 mr-3" />
            Appearance
          </Button>
          <Button variant="ghost" className="w-full justify-start text-sm">
            <Globe className="h-4 w-4 mr-3" />
            Language & Region
          </Button>
        </div>
      </div>

      <Separator />

      {/* 数据管理 */}
      <div className="space-y-3">
        <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
          Data Management
        </h4>
        <div className="space-y-2">
          <Button variant="ghost" className="w-full justify-start text-sm">
            <Download className="h-4 w-4 mr-3" />
            Export Data
          </Button>
          <Button variant="ghost" className="w-full justify-start text-sm text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20">
            <Trash2 className="h-4 w-4 mr-3" />
            Delete Account
          </Button>
        </div>
      </div>

      <Separator />

      {/* 退出登录 */}
      <div className="pt-2">
        <Button 
          variant="outline" 
          className="w-full text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
          onClick={() => signOut()}
        >
          <LogOut className="h-4 w-4 mr-2" />
          Sign Out
        </Button>
      </div>
    </div>
  );
};
