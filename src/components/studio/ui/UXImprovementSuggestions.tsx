'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import { 
  Lightbulb, 
  CheckCircle, 
  AlertTriangle, 
  TrendingUp,
  Users,
  Clock,
  MousePointer,
  Smartphone,
  Monitor,
  Accessibility,
  Zap,
  Heart
} from 'lucide-react';

interface UXSuggestion {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  category: 'accessibility' | 'performance' | 'usability' | 'mobile' | 'engagement';
  impact: number; // 1-10
  effort: number; // 1-10
  implemented: boolean;
}

interface UXMetrics {
  taskCompletionRate: number;
  averageTaskTime: number;
  errorRate: number;
  userSatisfaction: number;
  mobileUsability: number;
  accessibilityScore: number;
}

export const UXImprovementSuggestions: React.FC = () => {
  const [suggestions, setSuggestions] = useState<UXSuggestion[]>([]);
  const [metrics, setMetrics] = useState<UXMetrics>({
    taskCompletionRate: 85,
    averageTaskTime: 120,
    errorRate: 5,
    userSatisfaction: 4.2,
    mobileUsability: 78,
    accessibilityScore: 82,
  });

  // 生成UX改进建议
  useEffect(() => {
    const generateSuggestions = (): UXSuggestion[] => {
      const baseSuggestions: UXSuggestion[] = [
        {
          id: '1',
          title: 'Add keyboard shortcuts help',
          description: 'Provide a visible keyboard shortcuts guide to improve power user experience',
          priority: 'medium',
          category: 'usability',
          impact: 7,
          effort: 3,
          implemented: false,
        },
        {
          id: '2',
          title: 'Improve error messages',
          description: 'Make error messages more specific and actionable with suggested solutions',
          priority: 'high',
          category: 'usability',
          impact: 8,
          effort: 4,
          implemented: false,
        },
        {
          id: '3',
          title: 'Add progress indicators',
          description: 'Show progress for long-running operations like audio generation',
          priority: 'high',
          category: 'engagement',
          impact: 9,
          effort: 5,
          implemented: true,
        },
        {
          id: '4',
          title: 'Optimize for mobile',
          description: 'Improve touch targets and layout for mobile devices',
          priority: 'high',
          category: 'mobile',
          impact: 8,
          effort: 7,
          implemented: false,
        },
        {
          id: '5',
          title: 'Add ARIA labels',
          description: 'Improve screen reader support with proper ARIA labels',
          priority: 'medium',
          category: 'accessibility',
          impact: 6,
          effort: 4,
          implemented: false,
        },
        {
          id: '6',
          title: 'Implement auto-save',
          description: 'Automatically save user work to prevent data loss',
          priority: 'high',
          category: 'usability',
          impact: 9,
          effort: 6,
          implemented: true,
        },
        {
          id: '7',
          title: 'Add undo/redo functionality',
          description: 'Allow users to undo and redo their actions',
          priority: 'medium',
          category: 'usability',
          impact: 7,
          effort: 8,
          implemented: false,
        },
        {
          id: '8',
          title: 'Optimize loading times',
          description: 'Implement lazy loading and code splitting to improve performance',
          priority: 'high',
          category: 'performance',
          impact: 8,
          effort: 6,
          implemented: true,
        },
        {
          id: '9',
          title: 'Add tooltips and help text',
          description: 'Provide contextual help for complex features',
          priority: 'medium',
          category: 'usability',
          impact: 6,
          effort: 3,
          implemented: false,
        },
        {
          id: '10',
          title: 'Improve color contrast',
          description: 'Ensure all text meets WCAG AA contrast requirements',
          priority: 'medium',
          category: 'accessibility',
          impact: 5,
          effort: 2,
          implemented: false,
        },
      ];

      // 根据当前指标调整建议优先级
      return baseSuggestions.map(suggestion => {
        let adjustedPriority = suggestion.priority;
        
        if (suggestion.category === 'accessibility' && metrics.accessibilityScore < 80) {
          adjustedPriority = 'high';
        }
        if (suggestion.category === 'mobile' && metrics.mobileUsability < 80) {
          adjustedPriority = 'high';
        }
        if (suggestion.category === 'performance' && metrics.averageTaskTime > 180) {
          adjustedPriority = 'high';
        }
        
        return { ...suggestion, priority: adjustedPriority };
      });
    };

    setSuggestions(generateSuggestions());
  }, [metrics]);

  const toggleImplemented = (id: string) => {
    setSuggestions(prev => prev.map(suggestion => 
      suggestion.id === id 
        ? { ...suggestion, implemented: !suggestion.implemented }
        : suggestion
    ));
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'accessibility': return <Accessibility className="h-4 w-4" />;
      case 'performance': return <Zap className="h-4 w-4" />;
      case 'usability': return <MousePointer className="h-4 w-4" />;
      case 'mobile': return <Smartphone className="h-4 w-4" />;
      case 'engagement': return <Heart className="h-4 w-4" />;
      default: return <Lightbulb className="h-4 w-4" />;
    }
  };

  const calculateROI = (impact: number, effort: number) => {
    return (impact / effort) * 10;
  };

  const implementedCount = suggestions.filter(s => s.implemented).length;
  const totalCount = suggestions.length;
  const implementationProgress = totalCount > 0 ? (implementedCount / totalCount) * 100 : 0;

  const highPrioritySuggestions = suggestions.filter(s => s.priority === 'high' && !s.implemented);
  const quickWins = suggestions.filter(s => s.effort <= 3 && s.impact >= 6 && !s.implemented);

  return (
    <div className="space-y-6">
      {/* UX指标概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            UX Metrics Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Task Completion Rate</span>
                <span className="text-sm">{metrics.taskCompletionRate}%</span>
              </div>
              <Progress value={metrics.taskCompletionRate} />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">User Satisfaction</span>
                <span className="text-sm">{metrics.userSatisfaction}/5</span>
              </div>
              <Progress value={(metrics.userSatisfaction / 5) * 100} />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Mobile Usability</span>
                <span className="text-sm">{metrics.mobileUsability}%</span>
              </div>
              <Progress value={metrics.mobileUsability} />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Accessibility Score</span>
                <span className="text-sm">{metrics.accessibilityScore}%</span>
              </div>
              <Progress value={metrics.accessibilityScore} />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Error Rate</span>
                <span className="text-sm">{metrics.errorRate}%</span>
              </div>
              <Progress value={100 - metrics.errorRate} />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Avg Task Time</span>
                <span className="text-sm">{metrics.averageTaskTime}s</span>
              </div>
              <Progress value={Math.max(0, 100 - (metrics.averageTaskTime / 300) * 100)} />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 实施进度 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2" />
              Implementation Progress
            </span>
            <Badge className="bg-blue-100 text-blue-800">
              {implementedCount}/{totalCount} completed
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Progress value={implementationProgress} className="mb-2" />
          <p className="text-sm text-gray-600">
            {implementationProgress.toFixed(1)}% of UX improvements implemented
          </p>
        </CardContent>
      </Card>

      {/* 高优先级建议 */}
      {highPrioritySuggestions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-red-600">
              <AlertTriangle className="h-5 w-5 mr-2" />
              High Priority Improvements
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {highPrioritySuggestions.map((suggestion) => (
                <div key={suggestion.id} className="p-3 border border-red-200 rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        {getCategoryIcon(suggestion.category)}
                        <h4 className="font-medium">{suggestion.title}</h4>
                        <Badge className={getPriorityColor(suggestion.priority)}>
                          {suggestion.priority}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{suggestion.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>Impact: {suggestion.impact}/10</span>
                        <span>Effort: {suggestion.effort}/10</span>
                        <span>ROI: {calculateROI(suggestion.impact, suggestion.effort).toFixed(1)}</span>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleImplemented(suggestion.id)}
                    >
                      Mark Done
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 快速胜利 */}
      {quickWins.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-green-600">
              <Zap className="h-5 w-5 mr-2" />
              Quick Wins (Low Effort, High Impact)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {quickWins.map((suggestion) => (
                <div key={suggestion.id} className="p-3 border border-green-200 rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        {getCategoryIcon(suggestion.category)}
                        <h4 className="font-medium">{suggestion.title}</h4>
                        <Badge className="bg-green-100 text-green-800">Quick Win</Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{suggestion.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>Impact: {suggestion.impact}/10</span>
                        <span>Effort: {suggestion.effort}/10</span>
                        <span>ROI: {calculateROI(suggestion.impact, suggestion.effort).toFixed(1)}</span>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleImplemented(suggestion.id)}
                    >
                      Implement
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 所有建议 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Lightbulb className="h-5 w-5 mr-2" />
            All UX Improvement Suggestions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {suggestions
              .sort((a, b) => {
                // 按优先级和ROI排序
                const priorityOrder = { high: 3, medium: 2, low: 1 };
                const aPriority = priorityOrder[a.priority];
                const bPriority = priorityOrder[b.priority];
                
                if (aPriority !== bPriority) return bPriority - aPriority;
                
                const aROI = calculateROI(a.impact, a.effort);
                const bROI = calculateROI(b.impact, b.effort);
                return bROI - aROI;
              })
              .map((suggestion) => (
                <div 
                  key={suggestion.id} 
                  className={`p-3 border rounded-lg ${suggestion.implemented ? 'bg-gray-50 opacity-75' : ''}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        {suggestion.implemented ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          getCategoryIcon(suggestion.category)
                        )}
                        <h4 className={`font-medium ${suggestion.implemented ? 'line-through' : ''}`}>
                          {suggestion.title}
                        </h4>
                        <Badge className={getPriorityColor(suggestion.priority)}>
                          {suggestion.priority}
                        </Badge>
                        {suggestion.implemented && (
                          <Badge className="bg-green-100 text-green-800">
                            Implemented
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{suggestion.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>Impact: {suggestion.impact}/10</span>
                        <span>Effort: {suggestion.effort}/10</span>
                        <span>ROI: {calculateROI(suggestion.impact, suggestion.effort).toFixed(1)}</span>
                        <span className="capitalize">{suggestion.category}</span>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleImplemented(suggestion.id)}
                    >
                      {suggestion.implemented ? 'Undo' : 'Mark Done'}
                    </Button>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
