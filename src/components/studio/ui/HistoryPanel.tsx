'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Badge } from '~/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { 
  History, 
  Play, 
  Download, 
  Search, 
  Clock, 
  Mic,
  FileText,
  Settings,
  Volume2
} from 'lucide-react';
import { api } from '~/trpc/react';

interface HistoryItem {
  id: string;
  action: string;
  changes: any;
  audioUrl?: string;
  generatedAt: Date;
}

interface HistoryPanelProps {
  projectId?: string;
  onPlayAudio?: (url: string) => void;
}

export const HistoryPanel: React.FC<HistoryPanelProps> = ({
  projectId,
  onPlayAudio,
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  // 获取历史记录
  const { data: history, isLoading } = api.project.getHistory.useQuery(
    { projectId: projectId!, limit: 50 },
    { enabled: !!projectId }
  );

  // 过滤历史记录
  const filteredHistory = history?.filter(item => {
    if (!searchQuery) return true;
    
    const searchLower = searchQuery.toLowerCase();
    return (
      item.action.toLowerCase().includes(searchLower) ||
      (item.changes?.inputText && item.changes.inputText.toLowerCase().includes(searchLower)) ||
      (item.changes?.selectedRole && item.changes.selectedRole.toLowerCase().includes(searchLower))
    );
  }) || [];

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'voice_generated':
        return <Mic className="h-4 w-4" />;
      case 'project_created':
        return <FileText className="h-4 w-4" />;
      case 'settings_changed':
        return <Settings className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getActionLabel = (action: string) => {
    switch (action) {
      case 'voice_generated':
        return 'Voice Generated';
      case 'project_created':
        return 'Project Created';
      case 'settings_changed':
        return 'Settings Changed';
      default:
        return action.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  const handleDownload = async (audioUrl: string, filename?: string) => {
    try {
      const response = await fetch(audioUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename || `voice-${Date.now()}.wav`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Failed to download audio:', error);
    }
  };

  if (!projectId) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <History className="h-5 w-5 mr-2" />
            History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <History className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>Select or create a project to view history</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <History className="h-5 w-5 mr-2" />
          History
        </CardTitle>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search history..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px]">
          {isLoading ? (
            <div className="text-center py-8 text-gray-500">
              Loading history...
            </div>
          ) : filteredHistory.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchQuery ? 'No history found matching your search.' : 'No history yet.'}
            </div>
          ) : (
            <div className="space-y-3">
              {filteredHistory.map((item) => (
                <div
                  key={item.id}
                  className="p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        {getActionIcon(item.action)}
                        <span className="font-medium text-sm">
                          {getActionLabel(item.action)}
                        </span>
                        <span className="text-xs text-gray-500">
                          {formatDate(item.generatedAt)}
                        </span>
                      </div>
                      
                      {item.changes?.inputText && (
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          "{item.changes.inputText}"
                        </p>
                      )}
                      
                      {item.changes && (
                        <div className="flex flex-wrap gap-1 mb-2">
                          {item.changes.selectedRole && (
                            <Badge variant="secondary" className="text-xs">
                              {item.changes.selectedRole}
                            </Badge>
                          )}
                          {item.changes.selectedModel && (
                            <Badge variant="outline" className="text-xs">
                              {item.changes.selectedModel}
                            </Badge>
                          )}
                          {item.changes.selectedLanguage && (
                            <Badge variant="outline" className="text-xs">
                              {item.changes.selectedLanguage}
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                    
                    {item.audioUrl && (
                      <div className="flex items-center space-x-1 ml-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onPlayAudio?.(item.audioUrl!)}
                          className="h-6 w-6 p-0"
                        >
                          <Volume2 className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDownload(item.audioUrl!)}
                          className="h-6 w-6 p-0"
                        >
                          <Download className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
};
