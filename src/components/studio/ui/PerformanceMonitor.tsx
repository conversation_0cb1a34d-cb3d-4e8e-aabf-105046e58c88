'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import { 
  Activity, 
  Cpu, 
  HardDrive, 
  Wifi, 
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  X
} from 'lucide-react';
import { useMemoryMonitor } from '../utils/PerformanceOptimizations';

interface PerformanceMetrics {
  renderTime: number;
  componentCount: number;
  memoryUsage: number;
  networkRequests: number;
  cacheHitRate: number;
  errorCount: number;
}

interface PerformanceMonitorProps {
  isVisible: boolean;
  onClose: () => void;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  isVisible,
  onClose,
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    componentCount: 0,
    memoryUsage: 0,
    networkRequests: 0,
    cacheHitRate: 0,
    errorCount: 0,
  });

  const [performanceHistory, setPerformanceHistory] = useState<number[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const memoryInfo = useMemoryMonitor();

  // 性能数据收集
  useEffect(() => {
    if (!isVisible) return;

    const collectMetrics = () => {
      // 收集渲染性能
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const renderTime = navigation ? navigation.loadEventEnd - navigation.navigationStart : 0;

      // 收集组件数量
      const componentCount = document.querySelectorAll('[data-component]').length;

      // 收集网络请求
      const networkEntries = performance.getEntriesByType('resource');
      const networkRequests = networkEntries.length;

      // 模拟缓存命中率
      const cacheHitRate = Math.random() * 100;

      // 收集错误数量
      const errorCount = (window as any).__errorCount || 0;

      const newMetrics: PerformanceMetrics = {
        renderTime,
        componentCount,
        memoryUsage: memoryInfo?.percentage || 0,
        networkRequests,
        cacheHitRate,
        errorCount,
      };

      setMetrics(newMetrics);

      // 更新性能历史
      if (isRecording) {
        setPerformanceHistory(prev => {
          const newHistory = [...prev, renderTime];
          return newHistory.slice(-20); // 保留最近20个数据点
        });
      }
    };

    collectMetrics();
    const interval = setInterval(collectMetrics, 1000);

    return () => clearInterval(interval);
  }, [isVisible, memoryInfo, isRecording]);

  // 性能等级评估
  const getPerformanceGrade = (metrics: PerformanceMetrics) => {
    let score = 100;
    
    if (metrics.renderTime > 3000) score -= 30;
    else if (metrics.renderTime > 1000) score -= 15;
    
    if (metrics.memoryUsage > 80) score -= 25;
    else if (metrics.memoryUsage > 60) score -= 10;
    
    if (metrics.cacheHitRate < 70) score -= 20;
    else if (metrics.cacheHitRate < 85) score -= 10;
    
    if (metrics.errorCount > 0) score -= metrics.errorCount * 5;

    if (score >= 90) return { grade: 'A', color: 'green', status: 'Excellent' };
    if (score >= 80) return { grade: 'B', color: 'blue', status: 'Good' };
    if (score >= 70) return { grade: 'C', color: 'yellow', status: 'Fair' };
    if (score >= 60) return { grade: 'D', color: 'orange', status: 'Poor' };
    return { grade: 'F', color: 'red', status: 'Critical' };
  };

  const performanceGrade = getPerformanceGrade(metrics);

  // 性能建议
  const getPerformanceSuggestions = (metrics: PerformanceMetrics) => {
    const suggestions = [];

    if (metrics.renderTime > 3000) {
      suggestions.push('Consider code splitting and lazy loading');
    }
    if (metrics.memoryUsage > 80) {
      suggestions.push('High memory usage detected - check for memory leaks');
    }
    if (metrics.cacheHitRate < 70) {
      suggestions.push('Low cache hit rate - optimize caching strategy');
    }
    if (metrics.componentCount > 100) {
      suggestions.push('High component count - consider virtualization');
    }
    if (metrics.errorCount > 0) {
      suggestions.push('Errors detected - check console for details');
    }

    return suggestions;
  };

  const suggestions = getPerformanceSuggestions(metrics);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>Performance Monitor</span>
              <Badge 
                className={`bg-${performanceGrade.color}-100 text-${performanceGrade.color}-800`}
              >
                Grade {performanceGrade.grade} - {performanceGrade.status}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsRecording(!isRecording)}
              >
                {isRecording ? 'Stop Recording' : 'Start Recording'}
              </Button>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 核心指标 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">Render Time</span>
                  </div>
                  <span className="text-lg font-bold">
                    {metrics.renderTime.toFixed(0)}ms
                  </span>
                </div>
                <Progress 
                  value={Math.min(100, (metrics.renderTime / 5000) * 100)} 
                  className="mt-2"
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <HardDrive className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium">Memory Usage</span>
                  </div>
                  <span className="text-lg font-bold">
                    {metrics.memoryUsage.toFixed(1)}%
                  </span>
                </div>
                <Progress 
                  value={metrics.memoryUsage} 
                  className="mt-2"
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Cpu className="h-4 w-4 text-purple-500" />
                    <span className="text-sm font-medium">Components</span>
                  </div>
                  <span className="text-lg font-bold">
                    {metrics.componentCount}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Wifi className="h-4 w-4 text-orange-500" />
                    <span className="text-sm font-medium">Network Requests</span>
                  </div>
                  <span className="text-lg font-bold">
                    {metrics.networkRequests}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-4 w-4 text-cyan-500" />
                    <span className="text-sm font-medium">Cache Hit Rate</span>
                  </div>
                  <span className="text-lg font-bold">
                    {metrics.cacheHitRate.toFixed(1)}%
                  </span>
                </div>
                <Progress 
                  value={metrics.cacheHitRate} 
                  className="mt-2"
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {metrics.errorCount > 0 ? (
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                    ) : (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                    <span className="text-sm font-medium">Errors</span>
                  </div>
                  <span className="text-lg font-bold">
                    {metrics.errorCount}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 性能历史图表 */}
          {performanceHistory.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Performance History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-32 flex items-end space-x-1">
                  {performanceHistory.map((value, index) => (
                    <div
                      key={index}
                      className="bg-blue-500 rounded-t"
                      style={{
                        height: `${Math.min(100, (value / 5000) * 100)}%`,
                        width: `${100 / performanceHistory.length}%`,
                      }}
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 性能建议 */}
          {suggestions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Performance Suggestions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {suggestions.map((suggestion, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5" />
                      <span className="text-sm">{suggestion}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* 详细内存信息 */}
          {memoryInfo && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Memory Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Used:</span>
                    <span className="ml-2">{(memoryInfo.used / 1024 / 1024).toFixed(2)} MB</span>
                  </div>
                  <div>
                    <span className="font-medium">Total:</span>
                    <span className="ml-2">{(memoryInfo.total / 1024 / 1024).toFixed(2)} MB</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
