'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Textarea } from '~/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Badge } from '~/components/ui/badge';
import { 
  MessageSquare, 
  Star, 
  Clock, 
  MousePointer, 
  Eye,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  X,
  Send
} from 'lucide-react';

interface UserAction {
  id: string;
  type: 'click' | 'scroll' | 'input' | 'navigation' | 'error';
  element: string;
  timestamp: number;
  duration?: number;
  value?: string;
}

interface UserSession {
  id: string;
  startTime: number;
  endTime?: number;
  actions: UserAction[];
  errors: string[];
  completedTasks: string[];
  userAgent: string;
  screenResolution: string;
}

interface FeedbackData {
  rating: number;
  category: string;
  message: string;
  timestamp: number;
  sessionId: string;
}

interface UserExperienceTrackerProps {
  isVisible: boolean;
  onClose: () => void;
}

export const UserExperienceTracker: React.FC<UserExperienceTrackerProps> = ({
  isVisible,
  onClose,
}) => {
  const [session, setSession] = useState<UserSession | null>(null);
  const [feedback, setFeedback] = useState<FeedbackData[]>([]);
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [newFeedback, setNewFeedback] = useState({
    rating: 5,
    category: 'general',
    message: '',
  });

  // 初始化用户会话
  useEffect(() => {
    if (!session) {
      const newSession: UserSession = {
        id: `session-${Date.now()}`,
        startTime: Date.now(),
        actions: [],
        errors: [],
        completedTasks: [],
        userAgent: navigator.userAgent,
        screenResolution: `${screen.width}x${screen.height}`,
      };
      setSession(newSession);
    }
  }, [session]);

  // 记录用户操作
  const recordAction = useCallback((action: Omit<UserAction, 'id' | 'timestamp'>) => {
    if (!session) return;

    const newAction: UserAction = {
      ...action,
      id: `action-${Date.now()}-${Math.random()}`,
      timestamp: Date.now(),
    };

    setSession(prev => prev ? {
      ...prev,
      actions: [...prev.actions, newAction],
    } : null);
  }, [session]);

  // 记录错误
  const recordError = useCallback((error: string) => {
    if (!session) return;

    setSession(prev => prev ? {
      ...prev,
      errors: [...prev.errors, error],
    } : null);

    recordAction({
      type: 'error',
      element: 'system',
      value: error,
    });
  }, [session, recordAction]);

  // 记录完成的任务
  const recordCompletedTask = useCallback((task: string) => {
    if (!session) return;

    setSession(prev => prev ? {
      ...prev,
      completedTasks: [...prev.completedTasks, task],
    } : null);
  }, [session]);

  // 监听用户交互
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const elementInfo = target.tagName + (target.className ? `.${target.className.split(' ')[0]}` : '');
      
      recordAction({
        type: 'click',
        element: elementInfo,
      });
    };

    const handleScroll = () => {
      recordAction({
        type: 'scroll',
        element: 'window',
        value: `${window.scrollY}`,
      });
    };

    const handleInput = (e: Event) => {
      const target = e.target as HTMLInputElement;
      recordAction({
        type: 'input',
        element: target.tagName + (target.type ? `[${target.type}]` : ''),
        value: target.value.length.toString(),
      });
    };

    // 监听错误
    const handleError = (e: ErrorEvent) => {
      recordError(`${e.message} at ${e.filename}:${e.lineno}`);
    };

    const handleUnhandledRejection = (e: PromiseRejectionEvent) => {
      recordError(`Unhandled promise rejection: ${e.reason}`);
    };

    document.addEventListener('click', handleClick);
    window.addEventListener('scroll', handleScroll);
    document.addEventListener('input', handleInput);
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      document.removeEventListener('click', handleClick);
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('input', handleInput);
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [recordAction, recordError]);

  // 提交反馈
  const submitFeedback = useCallback(() => {
    if (!session || !newFeedback.message.trim()) return;

    const feedbackData: FeedbackData = {
      ...newFeedback,
      timestamp: Date.now(),
      sessionId: session.id,
    };

    setFeedback(prev => [...prev, feedbackData]);
    setNewFeedback({ rating: 5, category: 'general', message: '' });
    setShowFeedbackForm(false);

    // 这里可以发送到后端
    console.log('Feedback submitted:', feedbackData);
  }, [session, newFeedback]);

  // 分析用户行为
  const analyzeUserBehavior = useCallback(() => {
    if (!session) return null;

    const totalActions = session.actions.length;
    const clickActions = session.actions.filter(a => a.type === 'click').length;
    const inputActions = session.actions.filter(a => a.type === 'input').length;
    const errorCount = session.errors.length;
    const sessionDuration = Date.now() - session.startTime;

    const clickRate = totalActions > 0 ? (clickActions / totalActions) * 100 : 0;
    const errorRate = totalActions > 0 ? (errorCount / totalActions) * 100 : 0;
    const engagementScore = Math.max(0, 100 - errorRate * 10 - (sessionDuration > 300000 ? 20 : 0));

    return {
      totalActions,
      clickActions,
      inputActions,
      errorCount,
      sessionDuration,
      clickRate,
      errorRate,
      engagementScore,
    };
  }, [session]);

  const behaviorAnalysis = analyzeUserBehavior();

  // 获取用户体验评分
  const getUXScore = () => {
    if (!behaviorAnalysis) return 0;
    
    let score = 100;
    
    // 错误率影响
    score -= behaviorAnalysis.errorRate * 2;
    
    // 参与度影响
    if (behaviorAnalysis.totalActions < 5) score -= 20;
    
    // 会话时长影响
    if (behaviorAnalysis.sessionDuration < 30000) score -= 15; // 少于30秒
    if (behaviorAnalysis.sessionDuration > 600000) score -= 10; // 超过10分钟
    
    return Math.max(0, Math.min(100, score));
  };

  const uxScore = getUXScore();

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Eye className="h-5 w-5" />
              <span>User Experience Tracker</span>
              <Badge className={`${uxScore >= 80 ? 'bg-green-100 text-green-800' : uxScore >= 60 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}>
                UX Score: {uxScore.toFixed(0)}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFeedbackForm(true)}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                Give Feedback
              </Button>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 会话概览 */}
          {session && behaviorAnalysis && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">Session Duration</span>
                  </div>
                  <p className="text-lg font-bold mt-1">
                    {Math.floor(behaviorAnalysis.sessionDuration / 60000)}m {Math.floor((behaviorAnalysis.sessionDuration % 60000) / 1000)}s
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <MousePointer className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium">Total Actions</span>
                  </div>
                  <p className="text-lg font-bold mt-1">
                    {behaviorAnalysis.totalActions}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-4 w-4 text-red-500" />
                    <span className="text-sm font-medium">Errors</span>
                  </div>
                  <p className="text-lg font-bold mt-1">
                    {behaviorAnalysis.errorCount}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-4 w-4 text-purple-500" />
                    <span className="text-sm font-medium">Engagement</span>
                  </div>
                  <p className="text-lg font-bold mt-1">
                    {behaviorAnalysis.engagementScore.toFixed(0)}%
                  </p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 最近的用户操作 */}
          {session && session.actions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recent Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {session.actions.slice(-10).reverse().map((action) => (
                    <div key={action.id} className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">
                          {action.type}
                        </Badge>
                        <span>{action.element}</span>
                        {action.value && (
                          <span className="text-gray-500">({action.value})</span>
                        )}
                      </div>
                      <span className="text-gray-400">
                        {new Date(action.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 错误日志 */}
          {session && session.errors.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-red-600">Error Log</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {session.errors.map((error, index) => (
                    <div key={index} className="p-2 bg-red-50 rounded text-sm text-red-700">
                      {error}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 反馈历史 */}
          {feedback.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">User Feedback</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {feedback.map((item, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${i < item.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                              />
                            ))}
                          </div>
                          <Badge variant="outline">{item.category}</Badge>
                        </div>
                        <span className="text-sm text-gray-500">
                          {new Date(item.timestamp).toLocaleString()}
                        </span>
                      </div>
                      <p className="text-sm">{item.message}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 反馈表单 */}
          {showFeedbackForm && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Submit Feedback</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Rating</label>
                  <div className="flex space-x-1">
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <button
                        key={rating}
                        onClick={() => setNewFeedback(prev => ({ ...prev, rating }))}
                        className="p-1"
                      >
                        <Star
                          className={`h-6 w-6 ${rating <= newFeedback.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                        />
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Category</label>
                  <Select
                    value={newFeedback.category}
                    onValueChange={(value) => setNewFeedback(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">General</SelectItem>
                      <SelectItem value="usability">Usability</SelectItem>
                      <SelectItem value="performance">Performance</SelectItem>
                      <SelectItem value="bug">Bug Report</SelectItem>
                      <SelectItem value="feature">Feature Request</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Message</label>
                  <Textarea
                    value={newFeedback.message}
                    onChange={(e) => setNewFeedback(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Please share your feedback..."
                    rows={4}
                  />
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowFeedbackForm(false)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={submitFeedback} disabled={!newFeedback.message.trim()}>
                    <Send className="h-4 w-4 mr-2" />
                    Submit
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
