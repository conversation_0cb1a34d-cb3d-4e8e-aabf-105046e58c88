import { TRPCError } from "@trpc/server";
import { z } from "zod";

import {
  createTRPCRouter,
  protectedProcedure,
} from "~/server/api/trpc";

// 管理员权限中间件
const adminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  if (!ctx.session?.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  
  // 从数据库获取用户角色
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });
  
  if (!user || (user.role !== "ADMIN" && user.role !== "SUPER_ADMIN")) {
    throw new TRPCError({ code: "FORBIDDEN" });
  }
  
  return next({
    ctx: {
      ...ctx,
      session: {
        ...ctx.session,
        user: {
          ...ctx.session.user,
          role: user.role,
        },
      },
    },
  });
});

export const statsRouter = createTRPCRouter({
  // 获取仪表板统计数据
  getDashboardStats: adminProcedure.query(async ({ ctx }) => {
    const [userStats, orderStats, creditPackageStats, voiceRoleStats] = await Promise.all([
      // 用户统计
      Promise.all([
        ctx.db.user.count(),
        ctx.db.user.count({ where: { role: "USER" } }),
        ctx.db.user.count({ where: { role: "ADMIN" } }),
        ctx.db.user.count({ where: { role: "SUPER_ADMIN" } }),
      ]),
      // 订单统计
      Promise.all([
        ctx.db.order.count(),
        ctx.db.order.count({ where: { status: "PAID" } }),
        ctx.db.order.count({ where: { status: "PENDING" } }),
        ctx.db.order.count({ where: { status: "FAILED" } }),
        ctx.db.order.aggregate({
          where: { status: "PAID" },
          _sum: { amount: true },
        }),
      ]),
      // 积分包统计
      Promise.all([
        ctx.db.creditPackage.count(),
        ctx.db.creditPackage.count({ where: { isActive: true } }),
        ctx.db.creditPackage.count({ where: { type: "STARTER" } }),
        ctx.db.creditPackage.count({ where: { type: "STANDARD" } }),
        ctx.db.creditPackage.count({ where: { type: "PREMIUM" } }),
        ctx.db.creditPackage.count({ where: { type: "ENTERPRISE" } }),
      ]),
      // 语音角色统计
      Promise.all([
        ctx.db.ttsRole.count(),
        ctx.db.ttsRole.count({ where: { isActive: true } }),
        ctx.db.user.count(), // 临时替换，因为TtsRole没有gender字段
        ctx.db.user.count(), // 临时替换，因为TtsRole没有gender字段
      ]),
    ]);

    const [totalUsers, regularUsers, adminUsers, superAdminUsers] = userStats;
    const [totalOrders, paidOrders, pendingOrders, failedOrders, revenueResult] = orderStats;
    const [totalCreditPackages, activeCreditPackages, starterPackages, standardPackages, premiumPackages, enterprisePackages] = creditPackageStats;
    const [totalVoiceRoles, activeVoiceRoles, maleRoles, femaleRoles] = voiceRoleStats;

    return {
      users: {
        total: totalUsers,
        regular: regularUsers,
        admin: adminUsers,
        superAdmin: superAdminUsers,
      },
      orders: {
        total: totalOrders,
        paid: paidOrders,
        pending: pendingOrders,
        failed: failedOrders,
        revenue: revenueResult._sum.amount || 0,
      },
      creditPackages: {
        total: totalCreditPackages,
        active: activeCreditPackages,
        starter: starterPackages,
        standard: standardPackages,
        premium: premiumPackages,
        enterprise: enterprisePackages,
      },
      voiceRoles: {
        total: totalVoiceRoles,
        active: activeVoiceRoles,
        male: maleRoles,
        female: femaleRoles,
      },
    };
  }),

  // 获取最近7天的趋势数据
  getTrendData: adminProcedure.query(async ({ ctx }) => {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const [orderTrends] = await Promise.all([
      // 订单趋势
      ctx.db.order.findMany({
        where: {
          createdAt: {
            gte: sevenDaysAgo,
          },
        },
        select: {
          createdAt: true,
          status: true,
          amount: true,
        },
      }),
    ]);

    // 按日期分组统计
    const dailyStats = new Map();
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateKey = date.toISOString().split('T')[0];
      dailyStats.set(dateKey, {
        date: dateKey,
        newUsers: 0,
        orders: 0,
        revenue: 0,
      });
    }

    // 暂时跳过用户注册统计，直到TypeScript类型更新
    // TODO: 在Prisma客户端类型更新后恢复用户趋势统计

    // 统计订单
    orderTrends.forEach(order => {
      const dateKey = order.createdAt.toISOString().split('T')[0];
      const stats = dailyStats.get(dateKey);
      if (stats) {
        stats.orders += 1;
        if (order.status === "PAID") {
          stats.revenue += order.amount;
        }
      }
    });

    return Array.from(dailyStats.values());
  }),

  // 获取用户角色分布
  getUserRoleDistribution: adminProcedure.query(async ({ ctx }) => {
    const [regularUsers, adminUsers, superAdminUsers] = await Promise.all([
      ctx.db.user.count({ where: { role: "USER" } }),
      ctx.db.user.count({ where: { role: "ADMIN" } }),
      ctx.db.user.count({ where: { role: "SUPER_ADMIN" } }),
    ]);

    return [
      { name: "普通用户", value: regularUsers, color: "#3B82F6" },
      { name: "管理员", value: adminUsers, color: "#10B981" },
      { name: "超级管理员", value: superAdminUsers, color: "#F59E0B" },
    ];
  }),

  // 获取订单状态分布
  getOrderStatusDistribution: adminProcedure.query(async ({ ctx }) => {
    const [paidOrders, pendingOrders, failedOrders, refundedOrders] = await Promise.all([
      ctx.db.order.count({ where: { status: "PAID" } }),
      ctx.db.order.count({ where: { status: "PENDING" } }),
      ctx.db.order.count({ where: { status: "FAILED" } }),
      ctx.db.order.count({ where: { status: "REFUNDED" } }),
    ]);

    return [
      { name: "已支付", value: paidOrders, color: "#10B981" },
      { name: "待支付", value: pendingOrders, color: "#F59E0B" },
      { name: "支付失败", value: failedOrders, color: "#EF4444" },
      { name: "已退款", value: refundedOrders, color: "#6B7280" },
    ];
  }),

  // 获取积分包类型分布
  getCreditPackageTypeDistribution: adminProcedure.query(async ({ ctx }) => {
    const [starterPackages, standardPackages, premiumPackages, enterprisePackages] = await Promise.all([
      ctx.db.creditPackage.count({ where: { type: "STARTER" } }),
      ctx.db.creditPackage.count({ where: { type: "STANDARD" } }),
      ctx.db.creditPackage.count({ where: { type: "PREMIUM" } }),
      ctx.db.creditPackage.count({ where: { type: "ENTERPRISE" } }),
    ]);

    return [
      { name: "入门包", value: starterPackages, color: "#3B82F6" },
      { name: "标准包", value: standardPackages, color: "#10B981" },
      { name: "高级包", value: premiumPackages, color: "#8B5CF6" },
      { name: "企业包", value: enterprisePackages, color: "#F59E0B" },
    ];
  }),
});