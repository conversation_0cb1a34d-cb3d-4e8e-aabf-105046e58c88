import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { UserRole, TransactionType } from "@prisma/client";

import {
  createTRPCRouter,
  protectedProcedure,
} from "~/server/api/trpc";

// 管理员权限中间件
const adminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  if (!ctx.session?.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  
  // 从数据库获取用户角色
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });
  
  if (!user || (user.role !== UserRole.ADMIN && user.role !== UserRole.SUPER_ADMIN)) {
    throw new TRPCError({ code: "FORBIDDEN" });
  }
  
  return next({
    ctx: {
      ...ctx,
      session: {
        ...ctx.session,
        user: {
          ...ctx.session.user,
          role: user.role,
        },
      },
    },
  });
});

export const userRouter = createTRPCRouter({
  // 获取用户列表
  getUsers: adminProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        search: z.string().optional(),
        role: z.nativeEnum(UserRole).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, search, role } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(search && {
          OR: [
            { name: { contains: search, mode: "insensitive" as const } },
            { email: { contains: search, mode: "insensitive" as const } },
          ],
        }),
        ...(role && { role }),
      };

      const [users, total] = await Promise.all([
        ctx.db.user.findMany({
          where,
          skip,
          take: limit,
          orderBy: { id: "desc" },
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            image: true,
            emailVerified: true,
            createdAt: true,
            credit: {
              select: {
                balance: true,
                totalEarned: true,
                totalSpent: true,
              },
            },

          },
        }),
        ctx.db.user.count({ where }),
      ]);

      return {
        users,
        total,
        pages: Math.ceil(total / limit),
        currentPage: page,
      };
    }),

  // 获取单个用户详情
  getUserById: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: input.id },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          image: true,
          emailVerified: true,
          createdAt: true,
          credit: {
            select: {
              balance: true,
              totalEarned: true,
              totalSpent: true,
            },
          },
          orders: {
            take: 10,
            orderBy: { createdAt: "desc" },
            select: {
              id: true,
              status: true,
              amount: true,
              createdAt: true,
              package: {
                select: {
                  name: true,
                  credits: true,
                },
              },
            },
          },
          transactions: {
            take: 10,
            orderBy: { createdAt: "desc" },
            select: {
              id: true,
              type: true,
              amount: true,
              description: true,
              createdAt: true,
            },
          },
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      return user;
    }),

  // 更新用户信息
  updateUser: adminProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1).optional(),
        email: z.string().email().optional(),
        role: z.nativeEnum(UserRole).optional(),
        creditAdjustment: z.number().optional(),
        creditDescription: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, creditAdjustment, creditDescription, ...userData } = input;

      // 检查用户是否存在
      const existingUser = await ctx.db.user.findUnique({
        where: { id },
        select: {
            id: true,
            role: true,
            credit: {
              select: {
                id: true,
                balance: true,
                totalEarned: true,
                totalSpent: true,
              },
            },
          },
      });

      if (!existingUser) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      // 如果有积分调整，使用事务处理
      if (creditAdjustment !== undefined && creditAdjustment !== 0) {
        return await ctx.db.$transaction(async (tx) => {
          // 更新用户信息
          const updatedUser = await tx.user.update({
            where: { id },
            data: userData,
          });

          // 确保用户有积分记录
          let userCredit = existingUser.credit;
          if (!userCredit) {
            userCredit = await tx.userCredit.create({
              data: {
                userId: id,
                balance: 0,
                totalEarned: 0,
                totalSpent: 0,
              },
            });
          }

          // 更新积分余额
          const newBalance = userCredit.balance + creditAdjustment;
          if (newBalance < 0) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Insufficient credit balance",
            });
          }

          await tx.userCredit.update({
            where: { userId: id },
            data: {
              balance: newBalance,
              totalEarned: creditAdjustment > 0 ? userCredit.totalEarned + creditAdjustment : userCredit.totalEarned,
              totalSpent: creditAdjustment < 0 ? userCredit.totalSpent + Math.abs(creditAdjustment) : userCredit.totalSpent,
            },
          });

          // 创建积分交易记录
          await tx.creditTransaction.create({
            data: {
              userId: id,
              creditId: userCredit.id,
              type: creditAdjustment > 0 ? TransactionType.BONUS : TransactionType.CONSUME,
              amount: creditAdjustment,
              description: creditDescription || `Admin adjustment: ${creditAdjustment > 0 ? '+' : ''}${creditAdjustment} credits`,
            },
          });

          return updatedUser;
        });
      }

      // 只更新用户信息
      return await ctx.db.user.update({
        where: { id },
        data: userData,
      });
    }),

  // 删除用户
  deleteUser: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: input.id },
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      // 防止删除超级管理员
      if (user.role === UserRole.SUPER_ADMIN) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Cannot delete super admin user",
        });
      }

      return await ctx.db.user.delete({
        where: { id: input.id },
      });
    }),

  // 调整用户积分
  adjustCredit: adminProcedure
    .input(
      z.object({
        userId: z.string(),
        amount: z.number(),
        description: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { userId, amount, description } = input;

      // 检查用户是否存在
      const existingUser = await ctx.db.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          credit: {
            select: {
              id: true,
              balance: true,
              totalEarned: true,
              totalSpent: true,
            },
          },
        },
      });

      if (!existingUser) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      return await ctx.db.$transaction(async (tx) => {
        // 确保用户有积分记录
        let userCredit = existingUser.credit;
        if (!userCredit) {
          userCredit = await tx.userCredit.create({
            data: {
              userId,
              balance: 0,
              totalEarned: 0,
              totalSpent: 0,
            },
          });
        }

        // 更新积分余额
        const newBalance = userCredit.balance + amount;
        if (newBalance < 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Insufficient credit balance",
          });
        }

        await tx.userCredit.update({
          where: { userId },
          data: {
            balance: newBalance,
            totalEarned: amount > 0 ? userCredit.totalEarned + amount : userCredit.totalEarned,
            totalSpent: amount < 0 ? userCredit.totalSpent + Math.abs(amount) : userCredit.totalSpent,
          },
        });

        // 创建积分交易记录
        await tx.creditTransaction.create({
          data: {
            userId,
            creditId: userCredit.id,
            type: amount > 0 ? TransactionType.BONUS : TransactionType.CONSUME,
            amount,
            description: description || `Admin adjustment: ${amount > 0 ? '+' : ''}${amount} credits`,
          },
        });

        return { success: true, newBalance };
      });
    }),

  // 获取用户统计
  getUserStats: adminProcedure.query(async ({ ctx }) => {
    const [totalUsers, activeUsers, adminUsers] = await Promise.all([
      ctx.db.user.count(),
      ctx.db.user.count({
        where: {
          sessions: {
            some: {
              expires: {
                gt: new Date(),
              },
            },
          },
        },
      }),
      ctx.db.user.count({
        where: {
          role: {
            in: [UserRole.ADMIN, UserRole.SUPER_ADMIN],
          },
        },
      }),
    ]);

    return {
      totalUsers,
      activeUsers,
      adminUsers,
      regularUsers: totalUsers - adminUsers,
    };
  }),
});