import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { z } from "zod";
import { r2 } from "~/lib/r2";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { env } from "~/env";

export const r2Router = createTRPCRouter({
  getPresignedUrl: protectedProcedure
    .input(z.object({ fileName: z.string(), fileType: z.string() }))
    .mutation(async ({ input }) => {
      const { fileName, fileType } = input;

      const signedUrl = await getSignedUrl(
        r2,
        new PutObjectCommand({
          Bucket: env.R2_BUCKET_NAME,
          Key: fileName,
          ContentType: fileType,
        }),
        { expiresIn: 60 * 5 }, // 5 minutes
      );

      return { url: signedUrl };
    }),
});