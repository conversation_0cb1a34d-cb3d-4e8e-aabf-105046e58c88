import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { ModelType, TransactionType } from "@prisma/client";

import {
  createTRPCRouter,
  protectedProcedure,
} from "~/server/api/trpc";

// 管理员权限中间件
const adminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  if (!ctx.session?.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });
  
  if (!user || (user.role !== "ADMIN" && user.role !== "SUPER_ADMIN")) {
    throw new TRPCError({ code: "FORBIDDEN" });
  }
  
  return next({ ctx });
});

export const tokenUsageRouter = createTRPCRouter({
  // 记录token使用情况
  recordUsage: protectedProcedure
    .input(
      z.object({
        modelType: z.nativeEnum(ModelType),
        modelName: z.string(),
        inputTokens: z.number().min(0).default(0),
        outputTokens: z.number().min(0).default(0),
        requestCount: z.number().min(1).default(1),
        endpoint: z.string().optional(),
        sessionId: z.string().optional(),
        metadata: z.any().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      
      // 暂时禁用模型定价
      const pricing = null;

      if (!pricing) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: `Pricing not found for model ${input.modelName}`,
        });
      }

      // 计算成本
      const totalTokens = input.inputTokens + input.outputTokens;
      const costUsd = 0; // 暂时禁用成本计算

      // 获取积分汇率设置
      const creditRateSetting = await ctx.db.systemSettings.findUnique({
        where: { key: "CREDIT_USD_RATE" },
      });
      
      const creditRate = creditRateSetting ? parseFloat(creditRateSetting.value) : 0.001;
      const creditsNeeded = Math.ceil(costUsd / creditRate);

      // 检查是否启用自动扣除积分
      const autoDeductSetting = await ctx.db.systemSettings.findUnique({
        where: { key: "AUTO_DEDUCT_CREDITS" },
      });
      
      const autoDeduct = autoDeductSetting?.value === "true";

      return await ctx.db.$transaction(async (tx) => {
        // 记录token使用
        const tokenUsage = await tx.tokenUsage.create({
          data: {
            userId,
            modelType: input.modelType,
            modelName: input.modelName,
            inputTokens: input.inputTokens,
            outputTokens: input.outputTokens,
            totalTokens,
            requestCount: input.requestCount,
            costUsd,
            creditsDeducted: autoDeduct ? creditsNeeded : 0,
            endpoint: input.endpoint,
            sessionId: input.sessionId,
            metadata: input.metadata,
          },
        });

        // 如果启用自动扣除积分
        if (autoDeduct && creditsNeeded > 0) {
          // 确保用户有积分记录
          let userCredit = await tx.userCredit.findUnique({
            where: { userId },
          });

          if (!userCredit) {
            userCredit = await tx.userCredit.create({
              data: {
                userId,
                balance: 0,
                totalEarned: 0,
                totalSpent: 0,
              },
            });
          }

          // 检查积分余额
          if (userCredit.balance < creditsNeeded) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: `Insufficient credits. Required: ${creditsNeeded}, Available: ${userCredit.balance}`,
            });
          }

          // 扣除积分
          await tx.userCredit.update({
            where: { userId },
            data: {
              balance: userCredit.balance - creditsNeeded,
              totalSpent: userCredit.totalSpent + creditsNeeded,
            },
          });

          // 创建积分交易记录
          await tx.creditTransaction.create({
            data: {
              userId,
              creditId: userCredit.id,
              type: TransactionType.CONSUME,
              amount: -creditsNeeded,
              description: `API usage: ${input.modelName} (${totalTokens} tokens)`,
            },
          });
        }

        return {
          ...tokenUsage,
          creditsNeeded,
          autoDeducted: autoDeduct,
        };
      });
    }),

  // 获取用户token使用统计
  getUserUsageStats: protectedProcedure
    .input(
      z.object({
        userId: z.string().optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        modelType: z.nativeEnum(ModelType).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const userId = input.userId || ctx.session.user.id;
      
      // 非管理员只能查看自己的统计
      if (userId !== ctx.session.user.id) {
        const user = await ctx.db.user.findUnique({
          where: { id: ctx.session.user.id },
          select: { role: true },
        });
        
        if (!user || (user.role !== "ADMIN" && user.role !== "SUPER_ADMIN")) {
          throw new TRPCError({ code: "FORBIDDEN" });
        }
      }

      const where = {
        userId,
        ...(input.startDate && input.endDate && {
          createdAt: {
            gte: input.startDate,
            lte: input.endDate,
          },
        }),
        ...(input.modelType && { modelType: input.modelType }),
      };

      const [usages, totalStats] = await Promise.all([
        ctx.db.tokenUsage.findMany({
          where,
          orderBy: { createdAt: "desc" },
          take: 100,
          select: {
            id: true,
            modelType: true,
            modelName: true,
            inputTokens: true,
            outputTokens: true,
            totalTokens: true,
            requestCount: true,
            costUsd: true,
            creditsDeducted: true,
            endpoint: true,
            createdAt: true,
          },
        }),
        ctx.db.tokenUsage.aggregate({
          where,
          _sum: {
            inputTokens: true,
            outputTokens: true,
            totalTokens: true,
            requestCount: true,
            costUsd: true,
            creditsDeducted: true,
          },
        }),
      ]);

      return {
        usages,
        totalStats: {
          totalInputTokens: totalStats._sum.inputTokens || 0,
          totalOutputTokens: totalStats._sum.outputTokens || 0,
          totalTokens: totalStats._sum.totalTokens || 0,
          totalRequests: totalStats._sum.requestCount || 0,
          totalCostUsd: totalStats._sum.costUsd || 0,
          totalCreditsDeducted: totalStats._sum.creditsDeducted || 0,
        },
      };
    }),

  // 获取系统级token使用统计（管理员）
  getSystemUsageStats: adminProcedure
    .input(
      z.object({
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        modelType: z.nativeEnum(ModelType).optional(),
        groupBy: z.enum(["day", "week", "month"]).default("day"),
      })
    )
    .query(async ({ ctx, input }) => {
      const where = {
        ...(input.startDate && input.endDate && {
          createdAt: {
            gte: input.startDate,
            lte: input.endDate,
          },
        }),
        ...(input.modelType && { modelType: input.modelType }),
      };

      // 按模型类型统计
      const modelStats = await ctx.db.tokenUsage.groupBy({
        by: ["modelType", "modelName"],
        where,
        _sum: {
          inputTokens: true,
          outputTokens: true,
          totalTokens: true,
          requestCount: true,
          costUsd: true,
          creditsDeducted: true,
        },
        _count: {
          id: true,
        },
      });

      // 总体统计
      const totalStats = await ctx.db.tokenUsage.aggregate({
        where,
        _sum: {
          inputTokens: true,
          outputTokens: true,
          totalTokens: true,
          requestCount: true,
          costUsd: true,
          creditsDeducted: true,
        },
        _count: {
          id: true,
        },
      });

      // 用户排行
      const topUsers = await ctx.db.tokenUsage.groupBy({
        by: ["userId"],
        where,
        _sum: {
          totalTokens: true,
          costUsd: true,
          creditsDeducted: true,
        },
        orderBy: {
          _sum: {
            totalTokens: "desc",
          },
        },
        take: 10,
      });

      // 获取用户信息
      const userIds = topUsers.map(u => u.userId);
      const users = await ctx.db.user.findMany({
        where: { id: { in: userIds } },
        select: { id: true, name: true, email: true },
      });

      const topUsersWithInfo = topUsers.map(usage => ({
        ...usage,
        user: users.find(u => u.id === usage.userId),
      }));

      return {
        modelStats,
        totalStats: {
          totalUsages: totalStats._count.id || 0,
          totalInputTokens: totalStats._sum.inputTokens || 0,
          totalOutputTokens: totalStats._sum.outputTokens || 0,
          totalTokens: totalStats._sum.totalTokens || 0,
          totalRequests: totalStats._sum.requestCount || 0,
          totalCostUsd: totalStats._sum.costUsd || 0,
          totalCreditsDeducted: totalStats._sum.creditsDeducted || 0,
        },
        topUsers: topUsersWithInfo,
      };
    }),
});