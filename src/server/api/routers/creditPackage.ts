import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { UserRole, CreditPackageType } from "@prisma/client";

import {
  createTRPCRouter,
  protectedProcedure,
} from "~/server/api/trpc";

// 管理员权限中间件
const adminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  if (!ctx.session?.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  
  // 从数据库获取用户角色
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });
  
  if (!user || (user.role !== UserRole.ADMIN && user.role !== UserRole.SUPER_ADMIN)) {
    throw new TRPCError({ code: "FORBIDDEN" });
  }
  
  return next({
    ctx: {
      ...ctx,
      session: {
        ...ctx.session,
        user: {
          ...ctx.session.user,
          role: user.role,
        },
      },
    },
  });
});

export const creditPackageRouter = createTRPCRouter({
  // 获取积分包列表
  getPackages: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        type: z.nativeEnum(CreditPackageType).optional(),
        isActive: z.boolean().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, type, isActive } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(type && { type }),
        ...(isActive !== undefined && { isActive }),
      };

      const [packages, total] = await Promise.all([
        ctx.db.creditPackage.findMany({
          where,
          skip,
          take: limit,
          orderBy: { id: "desc" },
          include: {
            _count: {
              select: {
                orders: true,
              },
            },
          },
        }),
        ctx.db.creditPackage.count({ where }),
      ]);

      return {
        packages,
        total,
        pages: Math.ceil(total / limit),
        currentPage: page,
      };
    }),

  // 获取单个积分包详情
  getPackageById: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const creditPackage = await ctx.db.creditPackage.findUnique({
        where: { id: input.id },
        include: {
          orders: {
            take: 10,
            orderBy: { id: "desc" },
            select: {
              id: true,
              status: true,
              amount: true,
              createdAt: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          _count: {
            select: {
              orders: true,
            },
          },
        },
      });

      if (!creditPackage) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Credit package not found",
        });
      }

      return creditPackage;
    }),

  // 创建积分包
  createPackage: adminProcedure
    .input(
      z.object({
        name: z.string().min(1),
        type: z.nativeEnum(CreditPackageType),
        credits: z.number().min(1),
        price: z.number().min(0),
        currency: z.string().default("USD"),
        bonusCredits: z.number().min(0).default(0),
        description: z.string().optional(),
        isActive: z.boolean().default(true),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.db.creditPackage.create({
        data: input,
      });
    }),

  // 更新积分包
  updatePackage: adminProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1).optional(),
        type: z.nativeEnum(CreditPackageType).optional(),
        credits: z.number().min(1).optional(),
        price: z.number().min(0).optional(),
        currency: z.string().optional(),
        bonusCredits: z.number().min(0).optional(),
        description: z.string().optional(),
        isActive: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      const existingPackage = await ctx.db.creditPackage.findUnique({
        where: { id },
      });

      if (!existingPackage) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Credit package not found",
        });
      }

      return ctx.db.creditPackage.update({
        where: { id },
        data,
      });
    }),

  // 删除积分包
  deletePackage: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const existingPackage = await ctx.db.creditPackage.findUnique({
        where: { id: input.id },
        include: {
          _count: {
            select: {
              orders: true,
            },
          },
        },
      });

      if (!existingPackage) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Credit package not found",
        });
      }

      // 防止删除有订单的积分包
      if (existingPackage._count.orders > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot delete credit package with existing orders",
        });
      }

      return ctx.db.creditPackage.delete({
        where: { id: input.id },
      });
    }),

  // 切换积分包状态
  togglePackageStatus: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const existingPackage = await ctx.db.creditPackage.findUnique({
        where: { id: input.id },
      });

      if (!existingPackage) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Credit package not found",
        });
      }

      return ctx.db.creditPackage.update({
        where: { id: input.id },
        data: {
          isActive: !existingPackage.isActive,
        },
      });
    }),

  // 获取积分包统计
  getPackageStats: adminProcedure.query(async ({ ctx }) => {
    const [totalPackages, activePackages, totalRevenue, totalOrders] = await Promise.all([
      ctx.db.creditPackage.count(),
      ctx.db.creditPackage.count({ where: { isActive: true } }),
      ctx.db.order.aggregate({
        where: { status: "PAID" },
        _sum: { amount: true },
      }),
      ctx.db.order.count({ where: { status: "PAID" } }),
    ]);

    return {
      totalPackages,
      activePackages,
      inactivePackages: totalPackages - activePackages,
      totalRevenue: totalRevenue._sum.amount || 0,
      totalOrders,
    };
  }),
});