import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { db } from "~/server/db";

export const languageRouter = createTRPCRouter({
  // 获取所有支持的语言列表（带角色统计）
  getAll: protectedProcedure.query(async () => {
    const languages = await db.language.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: {
            roleSupports: {
              where: { role: { isActive: true } }
            }
          }
        }
      },
      orderBy: [{ region: "asc" }, { name: "asc" }]
    });

    return languages.map(lang => ({
      ...lang,
      roleCount: lang._count.roleSupports
    }));
  }),

  // 按地区获取语言
  getByRegion: protectedProcedure
    .input(z.object({ region: z.string() }))
    .query(async ({ input }) => {
      return await db.language.findMany({
        where: { 
          isActive: true,
          region: input.region 
        },
        include: {
          _count: {
            select: {
              roleSupports: {
                where: { role: { isActive: true } }
              }
            }
          }
        },
        orderBy: { name: "asc" }
      });
    }),

  // 获取语言地区列表
  getRegions: protectedProcedure.query(async () => {
    const regions = await db.language.groupBy({
      by: ['region'],
      where: { isActive: true },
      _count: { code: true },
      orderBy: { region: 'asc' }
    });

    return regions.map(region => ({
      region: region.region,
      languageCount: region._count.code
    }));
  }),

  // 按语言获取支持的角色
  getRolesByLanguage: protectedProcedure
    .input(z.object({ 
      languageCode: z.string(),
      quality: z.enum(["standard", "premium", "experimental"]).optional(),
      limit: z.number().optional(),
      offset: z.number().optional(),
    }))
    .query(async ({ input }) => {
      return await db.ttsRole.findMany({
        where: {
          isActive: true,
          languageSupports: {
            some: {
              languageCode: input.languageCode,
              ...(input.quality && { quality: input.quality })
            }
          }
        },
        include: {
          languageSupports: {
            where: { languageCode: input.languageCode },
            include: { language: true }
          }
        },
        orderBy: { createdAt: "desc" },
        ...(input.limit && { take: input.limit }),
        ...(input.offset && { skip: input.offset }),
      });
    }),

  // 获取角色支持的语言详情
  getRoleLanguageSupports: protectedProcedure
    .input(z.object({ roleId: z.string() }))
    .query(async ({ input }) => {
      return await db.roleLanguageSupport.findMany({
        where: { roleId: input.roleId },
        include: { language: true },
        orderBy: [
          { isDefault: "desc" }, 
          { quality: "desc" },
          { language: { name: "asc" } }
        ]
      });
    }),

  // 更新角色语言支持
  updateRoleLanguageSupport: protectedProcedure
    .input(z.object({
      roleId: z.string(),
      languageCode: z.string(),
      quality: z.enum(["standard", "premium", "experimental"]).optional(),
      isDefault: z.boolean().optional(),
      sampleText: z.string().optional(),
      sampleUrl: z.string().optional(),
    }))
    .mutation(async ({ input }) => {
      const { roleId, languageCode, ...updateData } = input;
      
      return await db.roleLanguageSupport.update({
        where: {
          roleId_languageCode: {
            roleId,
            languageCode
          }
        },
        data: updateData,
        include: { language: true }
      });
    }),

  // 批量更新角色的语言支持文本
  batchUpdateRoleTexts: protectedProcedure
    .input(z.object({
      roleId: z.string(),
      updates: z.array(z.object({
        languageCode: z.string(),
        sampleText: z.string(),
      }))
    }))
    .mutation(async ({ input }) => {
      const results = [];
      
      for (const update of input.updates) {
        const result = await db.roleLanguageSupport.update({
          where: {
            roleId_languageCode: {
              roleId: input.roleId,
              languageCode: update.languageCode
            }
          },
          data: {
            sampleText: update.sampleText
          },
          include: { language: true }
        });
        results.push(result);
      }
      
      return results;
    }),

  // 搜索语言（支持模糊匹配）
  search: protectedProcedure
    .input(z.object({ 
      query: z.string(),
      region: z.string().optional()
    }))
    .query(async ({ input }) => {
      return await db.language.findMany({
        where: {
          isActive: true,
          ...(input.region && { region: input.region }),
          OR: [
            { name: { contains: input.query, mode: 'insensitive' } },
            { nativeName: { contains: input.query, mode: 'insensitive' } },
            { code: { contains: input.query, mode: 'insensitive' } }
          ]
        },
        include: {
          _count: {
            select: {
              roleSupports: {
                where: { role: { isActive: true } }
              }
            }
          }
        },
        orderBy: { name: "asc" }
      });
    }),
});
