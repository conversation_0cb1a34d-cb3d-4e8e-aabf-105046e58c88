import { z } from "zod";
import { createTRPCRouter, protectedProcedure, adminProcedure } from "~/server/api/trpc";
import { db } from "~/server/db";
import { createGeminiTTSClient } from "~/lib/gemini-tts";
import { createMinimaxTTSClient } from "~/lib/minimax-tts";
import { r2Audio, R2AudioClient } from "~/lib/r2-audio";
import { uploadAvatarToR2 } from "~/lib/r2-avatar";
import { ModelType, TransactionType } from "@prisma/client";
// import { estimateTokenCount } from "~/lib/token-utils"; // 暂时禁用

// 简单的内存缓存
interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class SimpleCache {
  private cache = new Map<string, CacheItem<any>>();

  set<T>(key: string, data: T, ttlMs: number = 300000): void { // 默认5分钟
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs
    });
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;

    const now = Date.now();
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

const cache = new SimpleCache();

// 支持的语言列表
const SUPPORTED_LANGUAGES = [
  "af", "ar", "bg", "bn", "ca", "cs", "da", "de", "el", "en", "es", "et", "fi", "fr", "gu", "hi", "hr", "hu", "id", "it", "ja", "kn", "ko", "lv", "ml", "mr", "ms", "nb", "nl", "pl", "pt", "ro", "ru", "sk", "sl", "sr", "sv", "ta", "te", "th", "tr", "uk", "ur", "vi", "yue", "zh"
];

// 语音风格选项
const VOICE_STYLES = [
  "conversational", "empathetic", "calm", "newscast"
];

export const ttsRouter = createTRPCRouter({
  // 创建新的语音角色
  create: adminProcedure
    .input(
      z.object({
        slug: z.string().min(1),
        name: z.string().min(1),
        nameEn: z.string().optional(),
        nameZh: z.string().optional(),
        genderZh: z.string().optional(),
        genderEn: z.string().optional(),
        avatarUrl: z.string().optional(),
        description: z.string().optional(),
        descriptionEn: z.string().optional(),
        descriptionZh: z.string().optional(),
        languages: z.array(z.string()),
        selectedLanguages: z.array(z.string()).optional(), // 兼容字段
        styles: z.array(z.string()),
        stylesEn: z.array(z.string()).optional(),
        stylesZh: z.array(z.string()).optional(),
        voiceName: z.string(),
        isActive: z.boolean().optional(),
        // 新增头像上传相关字段
        avatarData: z.string().optional(), // base64图片数据，用于上传
      })
    )
    .mutation(async ({ input }) => {
      let finalAvatarUrl = input.avatarUrl;
      
      // 如果提供了base64头像数据，上传到R2
      if (input.avatarData && input.avatarData.startsWith('data:image/')) {
        try {
          console.log('[Avatar Upload] 开始上传角色头像到R2', {
            slug: input.slug,
            avatarDataLength: input.avatarData.length
          });
          
          finalAvatarUrl = await uploadAvatarToR2(input.avatarData, input.slug);
          
          console.log('[Avatar Upload] 头像上传成功', {
            slug: input.slug,
            avatarUrl: finalAvatarUrl
          });
        } catch (error) {
          console.error('[Avatar Upload] 头像上传失败:', error);
          throw new Error(`头像上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }

      // 获取支持的语言列表（优先使用selectedLanguages，兼容旧版本）
      const languagesToSupport = input.selectedLanguages || input.languages;

      // 创建角色
      const role = await db.ttsRole.create({
        data: {
          slug: input.slug,
          name: input.name,
          nameEn: input.nameEn,
          nameZh: input.nameZh,
          genderZh: input.genderZh,
          genderEn: input.genderEn,
          avatarUrl: finalAvatarUrl,
          description: input.description,
          descriptionEn: input.descriptionEn,
          descriptionZh: input.descriptionZh,
          styles: input.styles,
          stylesEn: input.stylesEn || [],
          stylesZh: input.stylesZh || [],
          voiceName: input.voiceName,
          isActive: input.isActive ?? true,
        },
      });

      // 创建语言支持关联
      if (languagesToSupport.length > 0) {
        // 获取预设的多语言试听文本
        const getDefaultSampleText = (languageCode: string): string => {
          const sampleTexts: Record<string, string> = {
            'en-US': "Hello, welcome to our text-to-speech service. This is a sample of what this voice sounds like.",
            'zh-CN': "您好，欢迎使用我们的文本转语音服务。这是这个语音的示例。",
            'ja-JP': "こんにちは、私たちのテキスト読み上げサービスへようこそ。これは、この音声がどのように聞こえるかのサンプルです。",
            'ko-KR': "안녕하세요, 저희 텍스트 음성 변환 서비스에 오신 것을 환영합니다. 이것은 이 음성이 어떻게 들리는지에 대한 샘플입니다.",
            'fr-FR': "Bonjour, bienvenue dans notre service de synthèse vocale. Ceci est un échantillon de ce à quoi ressemble cette voix.",
            'de-DE': "Hallo, willkommen bei unserem Text-to-Speech-Service. Dies ist ein Beispiel dafür, wie diese Stimme klingt.",
            'es-US': "Hola, bienvenido a nuestro servicio de texto a voz. Esta es una muestra de cómo suena esta voz.",
            'it-IT': "Ciao, benvenuto nel nostro servizio di sintesi vocale. Questo è un esempio di come suona questa voce.",
            'pt-BR': "Olá, bem-vindo ao nosso serviço de conversão de texto em fala. Esta é uma amostra de como esta voz soa.",
            'ru-RU': "Привет, добро пожаловать в наш сервис преобразования текста в речь. Это пример того, как звучит этот голос.",
            'ar-EG': "مرحبا، أهلا بكم في خدمة تحويل النص إلى كلام الخاصة بنا. هذا مثال على كيفية صوت هذا الصوت.",
            'hi-IN': "नमस्ते, हमारी टेक्स्ट-टू-स्पीच सेवा में आपका स्वागत है। यह इस आवाज़ की एक नमूना है।",
            'th-TH': "สวัสดี ยินดีต้อนรับสู่บริการแปลงข้อความเป็นเสียงพูดของเรา นี่คือตัวอย่างของเสียงนี้",
            'vi-VN': "Xin chào, chào mừng bạn đến với dịch vụ chuyển văn bản thành giọng nói của chúng tôi. Đây là mẫu âm thanh này.",
            'id-ID': "Halo, selamat datang di layanan teks ke ucapan kami. Ini adalah contoh seperti apa suara ini terdengar.",
            'tr-TR': "Merhaba, metin okuma hizmetimize hoş geldiniz. Bu sesin nasıl göründüğünün bir örneğidir.",
            'pl-PL': "Cześć, witaj w naszej usłudze zamiany tekstu na mowę. To jest próbka tego, jak brzmi ten głos.",
            'nl-NL': "Hallo, welkom bij onze tekst-naar-spraak service. Dit is een voorbeeld van hoe deze stem klinkt.",
            'ro-RO': "Bună, bun venit la serviciul nostru de conversie text în vorbire. Acesta este un eșantion al vocii acesteia.",
            'uk-UA': "Привіт, ласкаво просимо до нашого сервісу перетворення тексту в мову. Це зразок цього голосу.",
            'bn-BD': "হ্যালো, আমাদের টেক্সট-টু-স্পিচ সেবায় স্বাগতম। এটি এই কণ্ঠস্বরের একটি নমুনা।",
            'en-IN': "Hello, welcome to our text-to-speech service. This is a sample of what this voice sounds like in Indian English.",
            'mr-IN': "नमस्कार, आमच्या मजकूर-ते-भाषण सेवेत आपले स्वागत आहे. हा या आवाजाचा नमुना आहे.",
            'ta-IN': "வணக்கம், எங்கள் உरை-से-पेच्च সেবা में आपका स्वागत है। यह इस आवाज़ का एक नमूना है।",
            'te-IN': "హలో, మా టెక్స్ట్-టు-స్పీచ్ సేవకు స్వాగతం. ఇది ఈ వాయిస్ ఎలా ఉంటుందో దాని నమూనా.",
            'km-KH': "សួស្តី សូមស្វាគមន៍មកកាន់សេវាកម្មប្តូរអត្ថបទទៅជាសំលេងរបស់យើង។ នេះគឺជាគំរូនៃសំលេងនេះ។",
            'ms-MY': "Hello, selamat datang ke perkhidmatan teks ke pertuturan kami. Ini adalah sampel bagaimana suara ini kedengaran.",
            'sv-SE': "Hej, välkommen till vår text-till-tal-tjänst. Det här är ett prov på hur denna röst låter."
          };
          return sampleTexts[languageCode] ?? sampleTexts['en-US']!;
        };

        await db.roleLanguageSupport.createMany({
          data: languagesToSupport.map((languageCode, index) => ({
            roleId: role.id,
            languageCode,
            isDefault: index === 0, // 第一个语言设为默认
            quality: 'standard',
            sampleText: getDefaultSampleText(languageCode), // 使用预设的多语言文本
          })),
        });
      }

      return role;
    }),

  // 获取所有语音角色
  getAll: protectedProcedure.query(async () => {
    return await db.ttsRole.findMany({
      include: {
        languageSupports: {
          include: {
            language: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }),

  // 根据ID获取语音角色
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      return await db.ttsRole.findUnique({
        where: { id: input.id },
      });
    }),

  // 按语言获取语音角色
  getRolesByLanguage: protectedProcedure
    .input(z.object({
      languageCode: z.string(),
      limit: z.number().optional().default(50),
      offset: z.number().optional().default(0),
      includeInactive: z.boolean().optional().default(false)
    }))
    .query(async ({ input }) => {
      const cacheKey = `roles-by-language-${input.languageCode}-${input.limit}-${input.offset}-${input.includeInactive}`;

      // 尝试从缓存获取
      const cached = cache.get(cacheKey);
      if (cached) {
        return cached;
      }

      const whereCondition: any = {
        languageSupports: {
          some: {
            languageCode: input.languageCode
          }
        }
      };

      // 如果不包含非活跃角色，添加活跃条件
      if (!input.includeInactive) {
        whereCondition.isActive = true;
      }

      const result = await db.ttsRole.findMany({
        where: whereCondition,
        include: {
          languageSupports: {
            where: { languageCode: input.languageCode },
            include: {
              language: true
            }
          },
          modelMappings: {
            include: {
              model: {
                include: {
                  provider: true
                }
              }
            },
            orderBy: { priority: 'asc' }
          }
        },
        orderBy: { createdAt: "desc" },
        take: input.limit,
        skip: input.offset
      });

      // 缓存结果15分钟
      cache.set(cacheKey, result, 900000);

      return result;
    }),

  // 获取所有支持的语言及其角色数量
  getLanguageStats: protectedProcedure
    .query(async () => {
      const cacheKey = 'language-stats';

      // 尝试从缓存获取
      const cached = cache.get(cacheKey);
      if (cached) {
        return cached;
      }

      const languageStats = await db.language.findMany({
        include: {
          _count: {
            select: {
              roleSupports: {
                where: {
                  role: {
                    isActive: true
                  }
                }
              }
            }
          }
        },
        orderBy: { name: 'asc' }
      });

      const result = languageStats.map(lang => ({
        code: lang.code,
        name: lang.name,
        nativeName: lang.nativeName,
        region: lang.region,
        roleCount: lang._count.roleSupports
      }));

      // 缓存结果1小时
      cache.set(cacheKey, result, 3600000);

      return result;
    }),

  // 获取语音样本
  getVoiceSamples: protectedProcedure
    .input(z.object({ roleId: z.string() }))
    .query(async ({ input }) => {
      const role = await db.ttsRole.findUnique({
        where: { id: input.roleId },
        select: {
          id: true,
          name: true,
          languageSupports: {
            include: {
              language: true,
            },
            where: {
              // 可以添加条件过滤
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
        },
      });

      if (!role) {
        return {
          roleId: input.roleId,
          roleName: "Unknown",
          samples: [],
        };
      }

      // 将 RoleLanguageSupport 转换为语音样本格式
      const samples = role.languageSupports.map((support) => ({
        id: support.id,
        languageCode: support.languageCode,
        language: support.language,
        sampleText: support.sampleText,
        sampleUrl: support.sampleUrl,
        quality: support.quality,
        isDefault: support.isDefault,
        createdAt: support.createdAt,
        updatedAt: support.updatedAt,
        roleId: support.roleId,
      }));

      return {
        roleId: input.roleId,
        roleName: role.name,
        samples,
      };
    }),

  // 更新语音角色
  update: adminProcedure
    .input(
      z.object({
        id: z.string(),
        slug: z.string().optional(),
        name: z.string().optional(),
        nameEn: z.string().optional(),
        nameZh: z.string().optional(),
        genderZh: z.string().optional(),
        genderEn: z.string().optional(),
        avatarUrl: z.string().optional(),
        description: z.string().optional(),
        descriptionEn: z.string().optional(),
        descriptionZh: z.string().optional(),
        languages: z.array(z.string()).optional(),
        selectedLanguages: z.array(z.string()).optional(), // 兼容字段
        styles: z.array(z.string()).optional(),
        stylesEn: z.array(z.string()).optional(),
        stylesZh: z.array(z.string()).optional(),
        modelName: z.string().optional(),
        voiceName: z.string().optional(),
        isActive: z.boolean().optional(),
        // 新增头像上传相关字段
        avatarData: z.string().optional(), // base64图片数据，用于上传
      })
    )
    .mutation(async ({ input }) => {
      const { id, avatarData, selectedLanguages, languages, ...updateData } = input;
      
      // 获取当前角色信息
      const role = await db.ttsRole.findUnique({
        where: { id },
        include: {
          languageSupports: true,
        },
      });

      if (!role) {
        throw new Error("Role not found");
      }

      let finalAvatarUrl = updateData.avatarUrl;
      
      // 如果提供了base64头像数据，上传到R2
      if (avatarData && avatarData.startsWith('data:image/')) {
        try {
          console.log('[Avatar Upload] 开始更新角色头像到R2', {
            roleId: id,
            avatarDataLength: avatarData.length
          });
          
          finalAvatarUrl = await uploadAvatarToR2(avatarData, role.slug);
          
          console.log('[Avatar Upload] 头像更新成功', {
            roleId: id,
            avatarUrl: finalAvatarUrl
          });
        } catch (error) {
          console.error('[Avatar Upload] 头像上传失败:', error);
          throw new Error(`头像上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }

      // 获取支持的语言列表（优先使用selectedLanguages，兼容旧版本）
      const languagesToSupport = selectedLanguages || languages;

      // 更新角色基本信息
      const updatedRole = await db.ttsRole.update({
        where: { id },
        data: {
          ...updateData,
          avatarUrl: finalAvatarUrl,
        },
      });

      // 如果更新了语言列表，需要同步更新语言支持
      if (languagesToSupport) {
        // 获取现有的语言支持记录
        const existingSupports = await db.roleLanguageSupport.findMany({
          where: { roleId: id },
        });

        const existingSupportMap = new Map(
          existingSupports.map(support => [support.languageCode, support])
        );

        // 删除不再支持的语言
        const languagesToDelete = existingSupports.filter(
          support => !languagesToSupport.includes(support.languageCode)
        );
        
        if (languagesToDelete.length > 0) {
          await db.roleLanguageSupport.deleteMany({
            where: {
              id: { in: languagesToDelete.map(s => s.id) },
            },
          });
        }

        // 添加新的语言支持，保留现有的文本和音频
        const languagesToAdd = languagesToSupport.filter(
          langCode => !existingSupportMap.has(langCode)
        );

        if (languagesToAdd.length > 0) {
          // 获取默认的试听文本
          const getDefaultSampleText = (languageCode: string): string => {
            const sampleTexts: Record<string, string> = {
              'en-US': "Hello, welcome to our text-to-speech service. This is a sample of what this voice sounds like.",
              'zh-CN': "您好，欢迎使用我们的文本转语音服务。这是这个语音的示例。",
              'ja-JP': "こんにちは、私たちのテキスト読み上げサービスへようこそ。これは、この音声がどのように聞こえるかのサンプルです。",
              'ko-KR': "안녕하세요, 저희 텍스트 음성 변환 서비스에 오신 것을 환영합니다. 이것은 이 음성이 어떻게 들리는지에 대한 샘플입니다.",
              'fr-FR': "Bonjour, bienvenue dans notre service de synthèse vocale. Ceci est un échantillon de ce à quoi ressemble cette voix.",
              'de-DE': "Hallo, willkommen bei unserem Text-to-Speech-Service. Dies ist ein Beispiel dafür, wie diese Stimme klingt.",
              'es-US': "Hola, bienvenido a nuestro servicio de texto a voz. Esta es una muestra de cómo suena esta voz.",
              'it-IT': "Ciao, benvenuto nel nostro servizio di sintesi vocale. Questo è un esempio di come suona questa voce.",
              'pt-BR': "Olá, bem-vindo ao nosso serviço de conversão de texto em fala. Esta é uma amostra de como esta voz soa.",
              'ru-RU': "Привет, добро пожаловать в наш сервис преобразования текста в речь. Это пример того, как звучит этот голос.",
              'ar-EG': "مرحبا، أهلا بكم في خدمة تحويل النص إلى كلام الخاصة بنا. هذا مثال على كيفية صوت هذا الصوت.",
              'hi-IN': "नमस्ते, हमारी टेक्स्ट-टू-स्पीच सेवा में आपका स्वागत है। यह इस आवाज़ की एक नमूना है।",
              'th-TH': "สวัสดี ยินดีต้อนรับสู่บริการแปลงข้อความเป็นเสียงพูดของเรา นี่คือตัวอย่างของเสียงนี้",
              'vi-VN': "Xin chào, chào mừng bạn đến với dịch vụ chuyển văn bản thành giọng nói của chúng tôi. Đây là mẫu âm thanh này.",
              'id-ID': "Halo, selamat datang di layanan teks ke ucapan kami. Ini adalah contoh seperti apa suara ini terdengar.",
              'tr-TR': "Merhaba, metin okuma hizmetimize hoş geldiniz. Bu sesin nasıl göründüğünün bir örneğidir.",
              'pl-PL': "Cześć, witaj w naszej usłudze zamiany tekstu na mowę. To jest próbka tego, jak brzmi ten głos.",
              'nl-NL': "Hallo, welkom bij onze tekst-naar-spraak service. Dit is een voorbeeld van hoe deze stem klinkt.",
              'ro-RO': "Bună, bun venit la serviciul nostru de conversie text în vorbire. Acesta este un eșantion al vocii acesteia.",
              'uk-UA': "Привіт, ласкаво просимо до нашого сервісу перетворення тексту в мову. Це зразок цього голосу.",
              'bn-BD': "হ্যালো, আমাদের টেক্সট-টু-স্পিচ সেবায় স্বাগতম। এটি এই কণ্ঠস্বরের একটি নমুনা।",
              'en-IN': "Hello, welcome to our text-to-speech service. This is a sample of what this voice sounds like in Indian English.",
              'mr-IN': "नमस्कार, आमच्या मजकूर-ते-भाषण सेवेत आपले स्वागत आहे. हा या आवाजाचा नमुना आहे.",
              'ta-IN': "வணக்கம், எங்கள் உரை-से-पेच्च سेवা में आपका स्वागत है। यह इस आवाज़ का एक नमूना है।",
              'te-IN': "హలో, మా టెక్స్ట్-టు-స్పీచ్ సేవకు స్వాగతం. ఇది ఈ వాయిస్ ఎలా ఉంటుందో దాని నమూనా.",
              'km-KH': "សួស្តី សូមស្វាគមន៍មកកាន់សេវាកម្មប្តូរអត្ថបទទៅជាសំលេងរបស់យើង។ នេះគឺជាគំរូនៃសំលេងនេះ។",
              'ms-MY': "Hello, selamat datang ke perkhidmatan teks ke pertuturan kami. Ini adalah sampel bagaimana suara ini kedengaran.",
              'sv-SE': "Hej, välkommen till vår text-till-tal-tjänst. Det här är ett prov på hur denna röst låter."
            };
            return sampleTexts[languageCode] ?? sampleTexts['en-US']!;
          };

          await db.roleLanguageSupport.createMany({
            data: languagesToAdd.map((languageCode, index) => ({
              roleId: id,
              languageCode,
              isDefault: index === 0 && existingSupports.length === 0, // 只有在没有现有支持时才设置默认
              quality: 'standard',
              sampleText: getDefaultSampleText(languageCode), // 使用预设的多语言文本
            })),
          });
        }
      }

      return updatedRole;
    }),

  // 删除语音角色
  delete: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      return await db.ttsRole.delete({
        where: { id: input.id },
      });
    }),

  // 批量删除语音角色
  deleteMany: adminProcedure
    .input(z.object({ ids: z.array(z.string()) }))
    .mutation(async ({ input }) => {
      return await db.ttsRole.deleteMany({
        where: {
          id: {
            in: input.ids,
          },
        },
      });
    }),

  // 切换角色状态
  toggleStatus: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      const role = await db.ttsRole.findUnique({
        where: { id: input.id },
        select: { isActive: true },
      });

      if (!role) {
        throw new Error("Role not found");
      }

      return await db.ttsRole.update({
        where: { id: input.id },
        data: { isActive: !role.isActive },
      });
    }),

  // 生成语音样本
  generateVoiceSample: protectedProcedure
    .input(
      z.object({
        roleId: z.string(),
        language: z.string(),
        text: z.string(),
        style: z.string().optional(), // 用于自然语言风格控制
        modelMappingId: z.string().optional(), // 指定使用的模型映射ID
      })
    )
    .mutation(async ({ input, ctx }) => {
      const { roleId, language, text, style, modelMappingId } = input;
      const userId = ctx.session.user.id;

      console.log('[TTS] 开始生成语音样本', {
        roleId,
        language,
        textLength: text.length,
        style,
        userId
      });

      // 1. 获取角色信息和模型映射
      const role = await ctx.db.ttsRole.findUnique({
        where: { id: roleId },
        include: {
          languageSupports: {
            where: { languageCode: language },
            include: { language: true }
          },
          modelMappings: {
            where: { isActive: true },
            include: {
              model: {
                include: { provider: true }
              }
            },
            orderBy: [
              { isDefault: 'desc' },
              { priority: 'asc' }
            ]
          }
        }
      });

      if (!role) {
        throw new Error(`语音角色不存在: ${roleId}`);
      }

      if (!role.isActive) {
        throw new Error(`语音角色已禁用: ${role.name}`);
      }

      // 检查语言支持
      const languageSupport = role.languageSupports.find(ls => ls.languageCode === language);
      if (!languageSupport) {
        throw new Error(`角色 ${role.name} 不支持语言: ${language}`);
      }

      // 2. 选择合适的模型和提供商
      let modelMapping;

      if (modelMappingId) {
        // 使用指定的模型映射
        modelMapping = role.modelMappings.find(mapping =>
          mapping.id === modelMappingId &&
          mapping.model.isActive &&
          mapping.model.provider.isActive
        );

        if (!modelMapping) {
          throw new Error(`指定的模型映射不存在或不可用: ${modelMappingId}`);
        }

        console.log('[TTS] 使用指定的模型映射', { modelMappingId });
      } else {
        // 自动选择优先级最高的模型映射
        modelMapping = role.modelMappings.find(mapping =>
          mapping.model.isActive && mapping.model.provider.isActive
        );

        if (!modelMapping) {
          throw new Error(`角色 ${role.name} 没有可用的模型映射`);
        }

        console.log('[TTS] 自动选择模型映射', { priority: modelMapping.priority });
      }

      const model = modelMapping.model;
      const provider = model.provider;

      console.log('[TTS] 选择的提供商和模型', {
        provider: provider.slug,
        model: model.name,
        voiceName: role.voiceName
      });

      // 3. 根据提供商类型调用相应的TTS客户端
      if (provider.slug === 'gemini') {
        // 使用Gemini TTS
        const geminiClient = createGeminiTTSClient(ctx.db);
        const geminiResponse = await geminiClient.synthesizeSpeech({
          text,
          voiceName: role.voiceName,
          model: model.name as "gemini-2.5-flash-preview-tts" | "gemini-2.5-pro-preview-tts",
          style
        });

        // Gemini返回Base64编码的LINEAR16格式，直接使用R2AudioClient的上传功能
        // R2AudioClient会自动检测LINEAR16格式并转换为WAV
        const uploadResult = await r2Audio.uploadAudio(
          geminiResponse.audioContent,
          `tts-${roleId}-${R2AudioClient.generateTextHash(text)}.wav`,
          'audio/wav',
          'LINEAR16'
        );

        return {
          audioUrl: uploadResult.url,
          audioSize: uploadResult.size,
          duration: geminiResponse.metadata.duration || 0,
          provider: provider.slug,
          model: model.name,
          voiceName: role.voiceName,
          language,
          format: 'wav'
        };

      } else if (provider.slug === 'minimax') {
        // 检查语音ID是否适合Minimax
        const voiceId = role.voiceName;
        // 更精确的语音ID格式检测
        // Gemini: 通常是较长的名称，如 "Sulafat", "Aoede", "Callirrhoe"
        // Minimax: 通常是较短的ID或包含特殊字符，如 "female-shaonv", "English_expressive_narrator"
        const isGeminiVoiceId = voiceId.length >= 6 &&
                               /^[A-Z][a-z]+$/.test(voiceId) &&
                               !voiceId.includes('_') &&
                               !voiceId.includes('-') &&
                               !voiceId.includes(' ');

        if (isGeminiVoiceId) {
          // 这个语音ID看起来是Gemini格式，尝试切换到Gemini
          const geminiMapping = role.modelMappings.find(mapping =>
            mapping.model.provider.slug === 'gemini' &&
            mapping.model.isActive &&
            mapping.model.provider.isActive
          );

          if (geminiMapping) {
            console.log(`[TTS] 语音ID "${voiceId}" 是Gemini格式，自动切换到Gemini提供商`);

            // 使用Gemini TTS
            const geminiClient = createGeminiTTSClient(ctx.db);
            const geminiResponse = await geminiClient.synthesizeSpeech({
              text,
              voiceName: voiceId,
              model: geminiMapping.model.name as "gemini-2.5-flash-preview-tts" | "gemini-2.5-pro-preview-tts",
              style
            });

            const uploadResult = await r2Audio.uploadAudio(
              geminiResponse.audioContent,
              `tts-${roleId}-${R2AudioClient.generateTextHash(text)}.wav`,
              'audio/wav',
              'LINEAR16'
            );

            return {
              audioUrl: uploadResult.url,
              audioSize: uploadResult.size,
              duration: geminiResponse.metadata.duration || 0,
              provider: 'gemini',
              model: geminiMapping.model.name,
              voiceName: role.voiceName,
              language,
              format: 'wav'
            };
          } else {
            throw new Error(`语音ID "${voiceId}" 似乎是Gemini格式，但没有找到可用的Gemini模型映射`);
          }
        }

        // 使用Minimax TTS
        const minimaxClient = createMinimaxTTSClient(ctx.db);
        const minimaxResponse = await minimaxClient.synthesizeSpeech({
          text,
          voice_id: voiceId,
          model: model.name,
          format: 'wav' // 直接设置为WAV格式
        });

        // Minimax直接返回WAV格式的Buffer，直接上传到R2
        const uploadResult = await r2Audio.uploadAudio(
          minimaxResponse.audioContent, // 直接传递Buffer，不转换为Base64
          `tts-${roleId}-${R2AudioClient.generateTextHash(text)}.wav`,
          'audio/wav',
          'WAV'
        );

        return {
          audioUrl: uploadResult.url,
          audioSize: uploadResult.size,
          duration: minimaxResponse.metadata.duration || 0,
          provider: provider.slug,
          model: model.name,
          voiceName: role.voiceName,
          language,
          format: 'wav'
        };

      } else {
        throw new Error(`不支持的TTS提供商: ${provider.slug}`);
      }
    }),

  // 获取支持的语言列表
  getSupportedLanguages: protectedProcedure.query(() => {
    return SUPPORTED_LANGUAGES;
  }),

  // 获取支持的语音风格
  getSupportedStyles: protectedProcedure.query(() => {
    return VOICE_STYLES;
  })
});