import { z } from "zod";
import { createTRPCRouter, adminProcedure, publicProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

export const roleModelMappingRouter = createTRPCRouter({
  // 根据模型ID获取支持的角色列表
  getRolesByModelId: publicProcedure
    .input(z.object({ 
      modelId: z.string(),
      includeInactive: z.boolean().default(false)
    }))
    .query(async ({ ctx, input }) => {
      const mappings = await ctx.db.roleModelMapping.findMany({
        where: {
          modelId: input.modelId,
          isActive: true,
          role: {
            isActive: input.includeInactive ? undefined : true
          }
        },
        include: {
          role: {
            include: {
              languageSupports: {
                include: {
                  language: true
                }
              }
            }
          }
        },
        orderBy: [
          { isDefault: "desc" },
          { priority: "desc" },
          { createdAt: "asc" }
        ]
      });

      return {
        modelId: input.modelId,
        roles: mappings.map(mapping => ({
          ...mapping.role,
          isDefault: mapping.isDefault,
          priority: mapping.priority,
          mappingId: mapping.id
        }))
      };
    }),

  // 根据角色ID获取支持的模型列表
  getModelsByRoleId: publicProcedure
    .input(z.object({ 
      roleId: z.string(),
      includeInactive: z.boolean().default(false)
    }))
    .query(async ({ ctx, input }) => {
      const mappings = await ctx.db.roleModelMapping.findMany({
        where: {
          roleId: input.roleId,
          isActive: true,
          model: {
            isActive: input.includeInactive ? undefined : true,
            modelType: "SPEECH_GENERATION"
          }
        },
        include: {
          model: {
            include: {
              provider: true,
              customPricings: {
                where: { isActive: true },
                orderBy: [
                  { isDefault: "desc" },
                  { createdAt: "desc" }
                ]
              }
            }
          }
        },
        orderBy: [
          { isDefault: "desc" },
          { priority: "desc" },
          { createdAt: "asc" }
        ]
      });

      return {
        roleId: input.roleId,
        models: mappings.map(mapping => ({
          ...mapping.model,
          isDefault: mapping.isDefault,
          priority: mapping.priority,
          mappingId: mapping.id
        }))
      };
    }),

  // 创建角色模型关联
  createMapping: adminProcedure
    .input(z.object({
      roleId: z.string(),
      modelId: z.string(),
      isDefault: z.boolean().default(false),
      priority: z.number().default(0)
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查角色是否存在
      const role = await ctx.db.ttsRole.findUnique({
        where: { id: input.roleId }
      });
      if (!role) {
        throw new TRPCError({ code: "NOT_FOUND", message: "语音角色不存在" });
      }

      // 检查模型是否存在且为TTS模型
      const model = await ctx.db.model.findUnique({
        where: { id: input.modelId }
      });
      if (!model || model.modelType !== "SPEECH_GENERATION") {
        throw new TRPCError({ code: "NOT_FOUND", message: "TTS模型不存在" });
      }

      // 检查关联是否已存在
      const existingMapping = await ctx.db.roleModelMapping.findUnique({
        where: {
          roleId_modelId: {
            roleId: input.roleId,
            modelId: input.modelId
          }
        }
      });

      if (existingMapping) {
        throw new TRPCError({ code: "CONFLICT", message: "角色模型关联已存在" });
      }

      // 如果设置为默认，需要取消该角色的其他默认关联
      if (input.isDefault) {
        await ctx.db.roleModelMapping.updateMany({
          where: {
            roleId: input.roleId,
            isDefault: true
          },
          data: {
            isDefault: false
          }
        });
      }

      return ctx.db.roleModelMapping.create({
        data: {
          roleId: input.roleId,
          modelId: input.modelId,
          isDefault: input.isDefault,
          priority: input.priority
        },
        include: {
          role: true,
          model: {
            include: {
              provider: true
            }
          }
        }
      });
    }),

  // 更新角色模型关联
  updateMapping: adminProcedure
    .input(z.object({
      id: z.string(),
      isDefault: z.boolean().optional(),
      priority: z.number().optional(),
      isActive: z.boolean().optional()
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      const mapping = await ctx.db.roleModelMapping.findUnique({
        where: { id }
      });

      if (!mapping) {
        throw new TRPCError({ code: "NOT_FOUND", message: "关联记录不存在" });
      }

      // 如果设置为默认，需要取消该角色的其他默认关联
      if (input.isDefault === true) {
        await ctx.db.roleModelMapping.updateMany({
          where: {
            roleId: mapping.roleId,
            isDefault: true,
            id: { not: id }
          },
          data: {
            isDefault: false
          }
        });
      }

      return ctx.db.roleModelMapping.update({
        where: { id },
        data: updateData,
        include: {
          role: true,
          model: {
            include: {
              provider: true
            }
          }
        }
      });
    }),

  // 删除角色模型关联
  deleteMapping: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const mapping = await ctx.db.roleModelMapping.findUnique({
        where: { id: input.id }
      });

      if (!mapping) {
        throw new TRPCError({ code: "NOT_FOUND", message: "关联记录不存在" });
      }

      return ctx.db.roleModelMapping.delete({
        where: { id: input.id }
      });
    }),

  // 批量创建角色模型关联
  createBatchMappings: adminProcedure
    .input(z.object({
      roleId: z.string(),
      modelIds: z.array(z.string()),
      defaultModelId: z.string()
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查角色是否存在
      const role = await ctx.db.ttsRole.findUnique({
        where: { id: input.roleId }
      });
      if (!role) {
        throw new TRPCError({ code: "NOT_FOUND", message: "语音角色不存在" });
      }

      // 检查所有模型是否存在且为TTS模型
      const models = await ctx.db.model.findMany({
        where: {
          id: { in: input.modelIds },
          modelType: "SPEECH_GENERATION"
        }
      });

      if (models.length !== input.modelIds.length) {
        throw new TRPCError({ code: "NOT_FOUND", message: "部分TTS模型不存在" });
      }

      // 验证默认模型在选中的模型列表中
      if (!input.modelIds.includes(input.defaultModelId)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "默认模型必须在选中的模型列表中"
        });
      }

      // 在事务中执行
      return ctx.db.$transaction(async (tx) => {
        // 1. 删除该角色的所有现有关联
        await tx.roleModelMapping.deleteMany({
          where: { roleId: input.roleId }
        });

        // 2. 创建新的关联
        const mappings = input.modelIds.map((modelId, index) => ({
          roleId: input.roleId,
          modelId,
          isDefault: modelId === input.defaultModelId,
          priority: modelId === input.defaultModelId ? 100 : 90 - index,
          isActive: true
        }));

        await tx.roleModelMapping.createMany({
          data: mappings
        });

        // 3. 返回创建的关联
        return tx.roleModelMapping.findMany({
          where: { roleId: input.roleId },
          include: {
            model: {
              include: {
                provider: true,
                customPricings: {
                  where: { isActive: true }
                }
              }
            }
          },
          orderBy: [
            { isDefault: "desc" },
            { priority: "desc" }
          ]
        });
      });
    }),

  // 获取所有角色模型关联（管理用）
  getAllMappings: adminProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      roleId: z.string().optional(),
      modelId: z.string().optional()
    }))
    .query(async ({ ctx, input }) => {
      const { page, limit, roleId, modelId } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(roleId && { roleId }),
        ...(modelId && { modelId })
      };

      const [mappings, total] = await Promise.all([
        ctx.db.roleModelMapping.findMany({
          where,
          skip,
          take: limit,
          include: {
            role: true,
            model: {
              include: {
                provider: true
              }
            }
          },
          orderBy: [
            { role: { name: "asc" } },
            { isDefault: "desc" },
            { priority: "desc" }
          ]
        }),
        ctx.db.roleModelMapping.count({ where })
      ]);

      return {
        mappings,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    })
});
