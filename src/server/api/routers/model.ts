import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, adminProcedure, protectedProcedure } from "~/server/api/trpc";
import type { ModelType, PricingType } from "@prisma/client";

export const modelRouter = createTRPCRouter({
  // 获取所有模型（用户可见）
  list: protectedProcedure
    .input(z.object({
      providerId: z.string().optional(),
      isActive: z.boolean().optional()
    }).optional())
    .query(async ({ ctx, input }) => {
      const where: any = { isActive: true };
      
      if (input?.providerId) where.providerId = input.providerId;
      if (input?.isActive !== undefined) where.isActive = input.isActive;

      return ctx.db.model.findMany({
        where,
        include: {
          provider: {
            select: { name: true, slug: true }
          },
          customPricings: {
            where: { isActive: true },
            take: 1
          },
          _count: {
            select: {
              customPricings: true,
              usages: true
            }
          }
        },
        orderBy: [
          { provider: { name: "asc" } },
          { displayName: "asc" }
        ]
      });
    }),

  // 管理员获取所有模型
  adminList: adminProcedure.query(async ({ ctx }) => {
    return ctx.db.model.findMany({
      include: {
        provider: true,
        customPricings: true,
        _count: {
          select: {
            usages: true,
            customPricings: true
          }
        }
      },
      orderBy: { createdAt: "desc" }
    });
  }),

  // 获取单个模型详情
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const model = await ctx.db.model.findUnique({
        where: { id: input.id },
        include: {
          provider: true,
          customPricings: {
            where: { isActive: true },
            orderBy: { createdAt: "desc" }
          },
          _count: {
            select: { usages: true }
          }
        }
      });

      if (!model) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Model not found"
        });
      }

      return model;
    }),

  // 创建模型
  create: adminProcedure
    .input(z.object({
      providerId: z.string(),
      name: z.string().min(1),
      displayName: z.string().optional(),
      description: z.string().optional(),
      modelType: z.enum(["TEXT_GENERATION", "SPEECH_GENERATION", "IMAGE_GENERATION", "VIDEO_GENERATION", "MUSIC_GENERATION"]),
      isActive: z.boolean().default(true),
      // 成本价格字段
      costPricingType: z.enum(["TOKEN", "CHARACTER", "IMAGE", "VIDEO", "REQUEST", "AUDIO"]).default("TOKEN"),
      costInputTokenPrice: z.number().min(0).default(0),
      costOutputTokenPrice: z.number().min(0).default(0),
      costInputTokenUnit: z.number().int().min(1).default(1000),
      costOutputTokenUnit: z.number().int().min(1).default(1000),
      costRequestPrice: z.number().min(0).default(0),
      costCharacterPrice: z.number().min(0).default(0),
      costCharacterUnit: z.number().int().min(1).default(1000),
      costImagePrice: z.number().min(0).default(0),
      costVideoPricePerSecond: z.number().min(0).default(0),
      costAudioPricePerSecond: z.number().min(0).default(0),
      costCurrency: z.string().default("USD"),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查提供商是否存在
      const provider = await ctx.db.modelProvider.findUnique({
        where: { id: input.providerId }
      });
      
      if (!provider) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Provider not found"
        });
      }

      // 检查模型名称唯一性（同一提供商内）
      const existing = await ctx.db.model.findFirst({
        where: {
          providerId: input.providerId,
          name: input.name
        }
      });

      if (existing) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Model with this name already exists for this provider"
        });
      }

      // 创建模型
      const model = await ctx.db.model.create({
        data: {
          ...input,
        },
        include: {
          provider: true
        }
      });

      return model;
    }),

  // 更新模型
  update: adminProcedure
    .input(z.object({
      id: z.string(),
      name: z.string().min(1).optional(),
      displayName: z.string().nullable().optional(),
      description: z.string().nullable().optional(),
      modelType: z.enum(["TEXT_GENERATION", "SPEECH_GENERATION", "IMAGE_GENERATION", "VIDEO_GENERATION", "MUSIC_GENERATION"]).optional(),
      isActive: z.boolean().optional(),
      // 添加成本价格字段
      costPricingType: z.enum(["TOKEN", "CHARACTER", "IMAGE", "VIDEO", "REQUEST", "AUDIO"]).optional(),
      costInputTokenPrice: z.number().min(0).optional(),
      costOutputTokenPrice: z.number().min(0).optional(),
      costInputTokenUnit: z.number().int().min(1).optional(),
      costOutputTokenUnit: z.number().int().min(1).optional(),
      costRequestPrice: z.number().min(0).optional(),
      costCharacterPrice: z.number().min(0).optional(),
      costCharacterUnit: z.number().int().min(1).optional(),
      costImagePrice: z.number().min(0).optional(),
      costVideoPricePerSecond: z.number().min(0).optional(),
      costAudioPricePerSecond: z.number().min(0).optional(),
      costCurrency: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;
      
      const existing = await ctx.db.model.findUnique({
        where: { id }
      });
      
      if (!existing) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Model not found"
        });
      }

      return ctx.db.model.update({
        where: { id },
        data: { ...data },
        include: {
          provider: true
        }
      });
    }),

  // 删除模型
  delete: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const existing = await ctx.db.model.findUnique({
        where: { id: input.id }
      });
      
      if (!existing) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Model not found"
        });
      }

      // 检查是否有相关的使用记录
      const usageCount = await ctx.db.modelUsage.count({
        where: { modelId: input.id }
      });
      
      if (usageCount > 0) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Cannot delete model with existing usage records"
        });
      }

      return ctx.db.model.delete({
        where: { id: input.id }
      });
    }),

  // 更新模型成本价格
  updateCostPricing: adminProcedure
    .input(z.object({
      id: z.string(),
      costPricingType: z.enum(["TOKEN", "CHARACTER", "IMAGE", "VIDEO", "REQUEST", "AUDIO"]),
      costInputTokenPrice: z.number().min(0),
      costOutputTokenPrice: z.number().min(0),
      costInputTokenUnit: z.number().int().min(1),
      costOutputTokenUnit: z.number().int().min(1),
      costRequestPrice: z.number().min(0),
      costCharacterPrice: z.number().min(0),
      costImagePrice: z.number().min(0),
      costVideoPricePerSecond: z.number().min(0),
      costAudioPricePerSecond: z.number().min(0),
      costCurrency: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...costData } = input;
      
      const existing = await ctx.db.model.findUnique({
        where: { id }
      });
      
      if (!existing) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Model not found"
        });
      }

      return ctx.db.model.update({
        where: { id },
        data: {
          ...costData,
        },
        include: {
          provider: true
        }
      });
    }),

  // 获取模型统计
  getStats: adminProcedure
    .input(z.object({
      modelId: z.string().optional(),
      days: z.number().int().positive().default(30)
    }).optional())
    .query(async ({ ctx, input }) => {
      const since = new Date();
      since.setDate(since.getDate() - (input?.days || 30));

      const where: any = {
        createdAt: { gte: since }
      };

      if (input?.modelId) {
        where.modelId = input.modelId;
      }

      const stats = await ctx.db.modelUsage.groupBy({
        by: ['modelId'],
        where,
        _count: {
          id: true
        },
        _sum: {
          inputTokens: true,
          outputTokens: true,
          totalTokens: true,
          costUsd: true,
          creditsDeducted: true
        }
      });

      // 获取模型信息
      const models = await ctx.db.model.findMany({
        where: {
          id: { in: stats.map((s: any) => s.modelId) }
        },
        include: {
          provider: { select: { name: true } }
        }
      });

      return stats.map((stat: any) => {
        const model = models.find((m: any) => m.id === stat.modelId);
        return {
          ...stat,
          model
        };
      });
    })
});
