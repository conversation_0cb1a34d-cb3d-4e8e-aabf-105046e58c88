import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { PricingType } from "@prisma/client";

export const customPricingRouter = createTRPCRouter({
  // 获取指定模型的所有价格配置
  listByModel: protectedProcedure
    .input(z.object({ modelId: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.db.customPricing.findMany({
        where: { modelId: input.modelId },
        orderBy: [
          { isDefault: "desc" },
          { isActive: "desc" },
          { createdAt: "desc" }
        ],
      });
    }),

  // 获取所有价格配置（管理员用）
  list: protectedProcedure
    .input(z.object({
      page: z.number().default(1),
      pageSize: z.number().default(20),
      search: z.string().optional(),
      modelId: z.string().optional(),
      pricingType: z.nativeEnum(PricingType).optional(),
      isActive: z.boolean().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { page, pageSize, search, modelId, pricingType, isActive } = input;
      const skip = (page - 1) * pageSize;

      const where: any = {};
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
        ];
      }
      
      if (modelId) {
        where.modelId = modelId;
      }
      
      if (pricingType) {
        where.pricingType = pricingType;
      }
      
      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      const [pricings, total] = await Promise.all([
        ctx.db.customPricing.findMany({
          where,
          include: {
            model: {
              include: {
                provider: true,
              },
            },
          },
          orderBy: [
            { isDefault: "desc" },
            { isActive: "desc" },
            { createdAt: "desc" }
          ],
          skip,
          take: pageSize,
        }),
        ctx.db.customPricing.count({ where }),
      ]);

      return {
        pricings,
        total,
        pages: Math.ceil(total / pageSize),
      };
    }),

  // 创建价格配置
  create: protectedProcedure
    .input(z.object({
      modelId: z.string(),
      name: z.string(),
      description: z.string().optional(),
      pricingType: z.nativeEnum(PricingType),
      inputTokenPrice: z.number().default(0),
      outputTokenPrice: z.number().default(0),
      requestPrice: z.number().default(0),
      characterPrice: z.number().default(0),
      characterUnit: z.number().int().min(1).default(1000),
      imagePrice: z.number().default(0),
      videoPricePerSecond: z.number().default(0),
      audioPricePerSecond: z.number().default(0),
      multiplier: z.number().default(1.0),
      currency: z.string().default("USD"),
      userTier: z.string().optional(),
      isActive: z.boolean().default(true),
      isDefault: z.boolean().default(false),
    }))
    .mutation(async ({ ctx, input }) => {
      // 如果设置为默认，先取消其他默认配置
      if (input.isDefault) {
        await ctx.db.customPricing.updateMany({
          where: { 
            modelId: input.modelId,
            isDefault: true 
          },
          data: { isDefault: false }
        });
      }

      return ctx.db.customPricing.create({
        data: input,
        include: {
          model: {
            include: {
              provider: true,
            },
          },
        },
      });
    }),

  // 更新价格配置
  update: protectedProcedure
    .input(z.object({
      id: z.string(),
      name: z.string().optional(),
      description: z.string().optional(),
      pricingType: z.nativeEnum(PricingType).optional(),
      inputTokenPrice: z.number().optional(),
      outputTokenPrice: z.number().optional(),
      requestPrice: z.number().optional(),
      characterPrice: z.number().optional(),
      characterUnit: z.number().int().min(1).optional(),
      imagePrice: z.number().optional(),
      videoPricePerSecond: z.number().optional(),
      audioPricePerSecond: z.number().optional(),
      multiplier: z.number().optional(),
      currency: z.string().optional(),
      userTier: z.string().optional(),
      isActive: z.boolean().optional(),
      isDefault: z.boolean().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      // 获取当前价格配置
      const currentPricing = await ctx.db.customPricing.findUnique({
        where: { id },
      });

      if (!currentPricing) {
        throw new Error("价格配置不存在");
      }

      // 如果设置为默认，先取消其他默认配置
      if (input.isDefault) {
        await ctx.db.customPricing.updateMany({
          where: { 
            modelId: currentPricing.modelId,
            isDefault: true,
            id: { not: id }
          },
          data: { isDefault: false }
        });
      }

      return ctx.db.customPricing.update({
        where: { id },
        data: updateData,
        include: {
          model: {
            include: {
              provider: true,
            },
          },
        },
      });
    }),

  // 删除价格配置
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 检查是否有相关的使用记录
      const usageCount = await ctx.db.modelUsage.count({
        where: { customPricingId: input.id }
      });

      if (usageCount > 0) {
        throw new Error("该价格配置存在使用记录，无法删除");
      }

      return ctx.db.customPricing.delete({
        where: { id: input.id },
      });
    }),

  // 获取单个价格配置详情
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.db.customPricing.findUnique({
        where: { id: input.id },
        include: {
          model: {
            include: {
              provider: true,
            },
          },
          usages: {
            take: 10,
            orderBy: { createdAt: "desc" },
          },
        },
      });
    }),

  // 复制价格配置
  duplicate: protectedProcedure
    .input(z.object({ 
      id: z.string(),
      name: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const original = await ctx.db.customPricing.findUnique({
        where: { id: input.id },
      });

      if (!original) {
        throw new Error("原价格配置不存在");
      }

      return ctx.db.customPricing.create({
        data: {
          modelId: original.modelId,
          name: input.name,
          description: original.description,
          pricingType: original.pricingType,
          inputTokenPrice: original.inputTokenPrice,
          outputTokenPrice: original.outputTokenPrice,
          requestPrice: original.requestPrice,
          characterPrice: original.characterPrice,
          imagePrice: original.imagePrice,
          videoPricePerSecond: original.videoPricePerSecond,
          audioPricePerSecond: original.audioPricePerSecond,
          imagePriceConfig: original.imagePriceConfig as any,
          videoPriceConfig: original.videoPriceConfig as any,
          audioPriceConfig: original.audioPriceConfig as any,
          multiplier: original.multiplier,
          currency: original.currency,
          userTier: original.userTier,
          validFrom: original.validFrom,
          validUntil: original.validUntil,
          isActive: original.isActive,
          isDefault: false, // 复制的配置不能是默认的
        },
        include: {
          model: {
            include: {
              provider: true,
            },
          },
        },
      });
    }),

  // 获取价格配置统计
  getStats: protectedProcedure
    .query(async ({ ctx }) => {
      const [total, active, byType] = await Promise.all([
        ctx.db.customPricing.count(),
        ctx.db.customPricing.count({ where: { isActive: true } }),
        ctx.db.customPricing.groupBy({
          by: ["pricingType"],
          _count: true,
        }),
      ]);

      return {
        total,
        active,
        inactive: total - active,
        byType: byType.reduce((acc, item) => {
          acc[item.pricingType] = item._count;
          return acc;
        }, {} as Record<string, number>),
      };
    }),
});