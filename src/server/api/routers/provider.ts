import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { MinimaxTTSClient } from "~/lib/minimax-tts";

export const providerRouter = createTRPCRouter({
  // 获取所有提供商
  getAll: protectedProcedure.query(async ({ ctx }) => {
    return ctx.db.modelProvider.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        description: true,
        apiKey: true,
        config: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        models: {
          select: {
            id: true,
            name: true,
            isActive: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }),

  // 管理员获取所有提供商列表 (别名方法)
  adminList: protectedProcedure.query(async ({ ctx }) => {
    return ctx.db.modelProvider.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        description: true,
        apiKey: true,
        config: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        models: {
          select: {
            id: true,
            name: true,
            isActive: true,
          },
        },
        _count: {
          select: {
            models: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }),

  // 简单列表 (不包含敏感信息)
  list: protectedProcedure.query(async ({ ctx }) => {
    return ctx.db.modelProvider.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        description: true,
        isActive: true,
      },
      where: {
        isActive: true,
      },
      orderBy: {
        name: "asc",
      },
    });
  }),

  // 根据ID获取提供商
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const provider = await ctx.db.modelProvider.findUnique({
        where: { id: input.id },
        select: {
          id: true,
          name: true,
          slug: true,
          description: true,
          apiKey: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          models: true,
        },
      });

      if (!provider) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Provider not found",
        });
      }

      return provider;
    }),

  // 创建提供商
  create: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1, "名称不能为空"),
        slug: z.string().min(1, "标识符不能为空"),
        description: z.string().optional(),
        apiKey: z.string().optional(),
        config: z.record(z.any()).optional(),
        isActive: z.boolean().default(true),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 检查slug是否已存在
      const existingProvider = await ctx.db.modelProvider.findUnique({
        where: { slug: input.slug },
      });

      if (existingProvider) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "标识符已存在",
        });
      }

      return ctx.db.modelProvider.create({
        data: input,
      });
    }),

  // 更新提供商
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1, "名称不能为空"),
        slug: z.string().min(1, "标识符不能为空"),
        description: z.string().optional(),
        apiKey: z.string().optional(),
        config: z.record(z.any()).optional(),
        isActive: z.boolean(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      // 检查slug是否已被其他提供商使用
      const existingProvider = await ctx.db.modelProvider.findFirst({
        where: {
          slug: input.slug,
          id: { not: id },
        },
      });

      if (existingProvider) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "标识符已被其他提供商使用",
        });
      }

      return ctx.db.modelProvider.update({
        where: { id },
        data,
      });
    }),

  // 删除提供商
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 检查是否有关联的模型
      const modelsCount = await ctx.db.model.count({
        where: { providerId: input.id },
      });

      if (modelsCount > 0) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: `无法删除提供商，还有 ${modelsCount} 个关联的模型`,
        });
      }

      return ctx.db.modelProvider.delete({
        where: { id: input.id },
      });
    }),

  // 切换提供商状态
  toggleStatus: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const provider = await ctx.db.modelProvider.findUnique({
        where: { id: input.id },
      });

      if (!provider) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Provider not found",
        });
      }

      return ctx.db.modelProvider.update({
        where: { id: input.id },
        data: { isActive: !provider.isActive },
      });
    }),

  // 同步Minimax语音
  syncMinimaxVoices: protectedProcedure
    .input(
      z.object({
        mode: z.enum(['preview', 'sync']).default('preview'), // 预览模式或同步模式
        preserveCustomizations: z.boolean().default(true), // 是否保留自定义设置
        selectedVoices: z.array(z.string()).optional(), // 选择性同步的语音ID
      }).optional()
    )
    .mutation(async ({ ctx, input }) => {
      try {
        // 检查Minimax提供商
        const provider = await ctx.db.modelProvider.findFirst({
          where: { slug: "minimax", isActive: true }
        });

        if (!provider) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Minimax提供商未找到或未激活",
          });
        }

        if (!provider.apiKey) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Minimax API Key未配置",
          });
        }

        const config = provider.config as any;
        if (!config?.groupId) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Minimax GroupID未配置",
          });
        }

        // 获取语音列表
        const client = new MinimaxTTSClient(ctx.db);
        const voices = await client.getVoiceList();

        // 过滤选择的语音（如果指定）
        const targetVoices = input?.selectedVoices
          ? voices.filter(v => input.selectedVoices!.includes(v.voice_id))
          : voices;

        // 获取现有的Minimax角色
        const existingRoles = await ctx.db.ttsRole.findMany({
          where: { slug: { startsWith: "minimax-" } },
          include: {
            languageSupports: {
              include: { language: true }
            },
            modelMappings: {
              include: { model: true }
            }
          }
        });

        // 获取Minimax模型
        const minimaxModels = await ctx.db.model.findMany({
          where: {
            provider: { slug: "minimax" },
            isActive: true,
            modelType: "SPEECH_GENERATION"
          }
        });

        if (minimaxModels.length === 0) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "未找到Minimax TTS模型",
          });
        }

        // 预览模式：只返回同步计划，不执行实际操作
        if (input?.mode === 'preview') {
          const syncPlan = {
            totalVoices: targetVoices.length,
            newVoices: [] as any[],
            existingVoices: [] as any[],
            customizedVoices: [] as any[]
          };

          for (const voice of targetVoices) {
            const slug = `minimax-${voice.voice_id.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}`;
            const existingRole = existingRoles.find(r => r.slug === slug);

            if (existingRole) {
              // 检查是否有自定义设置
              const hasCustomizations =
                existingRole.avatarUrl ||
                existingRole.description !== `${voice.name}语音` ||
                existingRole.name !== voice.name ||
                existingRole.languageSupports.length > 1;

              if (hasCustomizations) {
                syncPlan.customizedVoices.push({
                  voice_id: voice.voice_id,
                  name: voice.name,
                  currentName: existingRole.name,
                  hasAvatar: !!existingRole.avatarUrl,
                  hasCustomDescription: existingRole.description !== `${voice.name}语音`,
                  languageCount: existingRole.languageSupports.length
                });
              } else {
                syncPlan.existingVoices.push({
                  voice_id: voice.voice_id,
                  name: voice.name,
                  currentName: existingRole.name
                });
              }
            } else {
              syncPlan.newVoices.push({
                voice_id: voice.voice_id,
                name: voice.name,
                gender: voice.gender,
                language: voice.language
              });
            }
          }

          return {
            success: true,
            mode: 'preview',
            syncPlan
          };
        }

        // 执行同步操作
        let createdCount = 0;
        let updatedCount = 0;
        let skippedCount = 0;

        for (const voice of targetVoices) {
          const slug = `minimax-${voice.voice_id.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}`;
          const existingRole = existingRoles.find(r => r.slug === slug);

          // 准备角色数据
          const baseRoleData = {
            slug,
            voiceName: voice.voice_id,
            genderEn: voice.gender === 'female' ? 'Female' : voice.gender === 'male' ? 'Male' : 'Neutral',
            genderZh: voice.gender === 'female' ? '女性' : voice.gender === 'male' ? '男性' : '中性',
            styles: voice.style ? [voice.style] : [],
            stylesEn: voice.style ? [voice.style] : [],
            stylesZh: voice.style ? [voice.style] : [],
            isActive: true
          };

          if (existingRole) {
            // 检查是否有自定义设置
            const hasCustomizations =
              existingRole.avatarUrl ||
              existingRole.description !== `${voice.name}语音` ||
              existingRole.name !== voice.name;

            if (hasCustomizations && input?.preserveCustomizations) {
              // 保留自定义设置，只更新核心字段
              await ctx.db.ttsRole.update({
                where: { id: existingRole.id },
                data: {
                  voiceName: voice.voice_id, // 始终更新voiceName
                  genderEn: baseRoleData.genderEn,
                  genderZh: baseRoleData.genderZh,
                  // 保留用户自定义的name, description, avatarUrl等
                }
              });
              skippedCount++;
            } else {
              // 完全更新
              await ctx.db.ttsRole.update({
                where: { id: existingRole.id },
                data: {
                  ...baseRoleData,
                  name: voice.name,
                  nameEn: voice.name,
                  nameZh: voice.name,
                  description: voice.description || `${voice.name}语音`,
                  descriptionEn: voice.description || `${voice.name} voice`,
                  descriptionZh: voice.description || `${voice.name}语音`,
                }
              });
              updatedCount++;
            }
          } else {
            // 创建新角色
            const newRole = await ctx.db.ttsRole.create({
              data: {
                ...baseRoleData,
                name: voice.name,
                nameEn: voice.name,
                nameZh: voice.name,
                description: voice.description || `${voice.name}语音`,
                descriptionEn: voice.description || `${voice.name} voice`,
                descriptionZh: voice.description || `${voice.name}语音`,
              }
            });

            // 创建语言支持
            if (voice.language) {
              await ctx.db.roleLanguageSupport.create({
                data: {
                  roleId: newRole.id,
                  languageCode: voice.language,
                  quality: 'HIGH',
                  isDefault: true,
                  sampleText: "这是一个语音示例文本。"
                }
              });
            }

            // 创建模型关联
            for (let i = 0; i < minimaxModels.length; i++) {
              await ctx.db.roleModelMapping.create({
                data: {
                  roleId: newRole.id,
                  modelId: minimaxModels[i]!.id,
                  isDefault: i === 0,
                  priority: minimaxModels.length - i,
                  isActive: true
                }
              });
            }

            createdCount++;
          }
        }

        return {
          success: true,
          mode: 'sync',
          message: `同步完成: 创建 ${createdCount} 个，更新 ${updatedCount} 个，保留自定义 ${skippedCount} 个语音角色`,
          created: createdCount,
          updated: updatedCount,
          skipped: skippedCount,
          total: targetVoices.length,
          preserveCustomizations: input?.preserveCustomizations ?? true
        };

      } catch (error) {
        console.error('同步Minimax语音失败:', error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `同步失败: ${error instanceof Error ? error.message : '未知错误'}`,
        });
      }
    }),

  // 获取Minimax语音列表（仅用于测试预览）
  getMinimaxVoices: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        // 检查Minimax提供商
        const provider = await ctx.db.modelProvider.findFirst({
          where: { slug: "minimax", isActive: true }
        });

        if (!provider) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Minimax提供商未找到或未激活",
          });
        }

        if (!provider.apiKey) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Minimax API Key未配置",
          });
        }

        const config = provider.config as any;
        if (!config?.groupId) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Minimax GroupID未配置",
          });
        }

        // 获取语音列表
        const client = new MinimaxTTSClient(ctx.db);
        const voices = await client.getVoiceList();

        return {
          success: true,
          voices,
          total: voices.length,
          provider: {
            name: provider.name,
            hasApiKey: !!provider.apiKey,
            hasGroupId: !!config?.groupId
          }
        };

      } catch (error) {
        console.error('获取Minimax语音列表失败:', error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `获取失败: ${error instanceof Error ? error.message : '未知错误'}`,
        });
      }
    }),
});