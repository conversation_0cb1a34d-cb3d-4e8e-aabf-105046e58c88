import { TRPCError } from "@trpc/server";
import { z } from "zod";

import {
  createTRPCRouter,
  protectedProcedure,
} from "~/server/api/trpc";

// 管理员权限中间件
const adminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  if (!ctx.session?.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });
  
  if (!user || (user.role !== "ADMIN" && user.role !== "SUPER_ADMIN")) {
    throw new TRPCError({ code: "FORBIDDEN" });
  }
  
  return next({ ctx });
});

export const systemSettingsRouter = createTRPCRouter({
  // 获取所有系统设置
  getAll: adminProcedure.query(async ({ ctx }) => {
    return await ctx.db.systemSettings.findMany({
      orderBy: { key: "asc" },
    });
  }),

  // 获取单个设置
  getByKey: adminProcedure
    .input(z.object({ key: z.string() }))
    .query(async ({ ctx, input }) => {
      const setting = await ctx.db.systemSettings.findUnique({
        where: { key: input.key },
      });

      if (!setting) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Setting not found",
        });
      }

      return setting;
    }),

  // 更新设置
  update: adminProcedure
    .input(
      z.object({
        key: z.string(),
        value: z.string(),
        description: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.systemSettings.upsert({
        where: { key: input.key },
        update: {
          value: input.value,
          description: input.description,
        },
        create: {
          key: input.key,
          value: input.value,
          description: input.description,
        },
      });
    }),

  // 批量更新设置
  batchUpdate: adminProcedure
    .input(
      z.array(
        z.object({
          key: z.string(),
          value: z.string(),
          description: z.string().optional(),
        })
      )
    )
    .mutation(async ({ ctx, input }) => {
      const updates = input.map(setting =>
        ctx.db.systemSettings.upsert({
          where: { key: setting.key },
          update: {
            value: setting.value,
            description: setting.description,
          },
          create: {
            key: setting.key,
            value: setting.value,
            description: setting.description,
          },
        })
      );

      return await Promise.all(updates);
    }),

  // 删除设置
  delete: adminProcedure
    .input(z.object({ key: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const existing = await ctx.db.systemSettings.findUnique({
        where: { key: input.key },
      });

      if (!existing) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Setting not found",
        });
      }

      return await ctx.db.systemSettings.delete({
        where: { key: input.key },
      });
    }),

  // 获取积分汇率
  getCreditRate: protectedProcedure.query(async ({ ctx }) => {
    const setting = await ctx.db.systemSettings.findUnique({
      where: { key: "CREDIT_USD_RATE" },
    });

    return {
      rate: setting ? parseFloat(setting.value) : 0.001,
      description: setting?.description || "Default credit to USD rate",
    };
  }),
});