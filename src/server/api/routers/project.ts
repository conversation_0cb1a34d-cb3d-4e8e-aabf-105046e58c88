import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 项目内容结构
const ProjectContentSchema = z.object({
  inputText: z.string().default(""),
  styleInstructions: z.string().default(""),
});

// 项目设置结构
const ProjectSettingsSchema = z.object({
  selectedLanguage: z.string().optional(),
  selectedRoleId: z.string().optional(),
  selectedModelId: z.string().optional(),
  selectedMode: z.enum(['single', 'multi']).default('single'),
});

export const projectRouter = createTRPCRouter({
  // 获取用户的项目列表
  getProjects: protectedProcedure
    .input(z.object({
      status: z.enum(['DRAFT', 'ACTIVE', 'ARCHIVED']).optional(),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ ctx, input }) => {
      const projects = await ctx.db.project.findMany({
        where: {
          userId: ctx.session.user.id,
          ...(input.status && { status: input.status }),
        },
        orderBy: [
          { lastAccessedAt: 'desc' },
          { updatedAt: 'desc' },
        ],
        take: input.limit,
        skip: input.offset,
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          lastAccessedAt: true,
          createdAt: true,
          updatedAt: true,
          content: true,
          settings: true,
        },
      });

      return projects;
    }),

  // 获取单个项目详情
  getProject: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const project = await ctx.db.project.findFirst({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
        include: {
          history: {
            orderBy: { generatedAt: 'desc' },
            take: 10,
          },
        },
      });

      if (!project) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Project not found',
        });
      }

      // 更新最后访问时间
      await ctx.db.project.update({
        where: { id: input.id },
        data: { lastAccessedAt: new Date() },
      });

      return project;
    }),

  // 创建新项目
  createProject: protectedProcedure
    .input(z.object({
      name: z.string().min(1).max(100),
      description: z.string().max(500).optional(),
      content: ProjectContentSchema.optional(),
      settings: ProjectSettingsSchema.optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const project = await ctx.db.project.create({
        data: {
          name: input.name,
          description: input.description,
          userId: ctx.session.user.id,
          content: input.content ?? {},
          settings: input.settings ?? {},
          lastAccessedAt: new Date(),
        },
      });

      return project;
    }),

  // 更新项目
  updateProject: protectedProcedure
    .input(z.object({
      id: z.string(),
      name: z.string().min(1).max(100).optional(),
      description: z.string().max(500).optional(),
      content: ProjectContentSchema.optional(),
      settings: ProjectSettingsSchema.optional(),
      status: z.enum(['DRAFT', 'ACTIVE', 'ARCHIVED']).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      // 验证项目所有权
      const existingProject = await ctx.db.project.findFirst({
        where: {
          id,
          userId: ctx.session.user.id,
        },
      });

      if (!existingProject) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Project not found',
        });
      }

      const project = await ctx.db.project.update({
        where: { id },
        data: {
          ...updateData,
          lastAccessedAt: new Date(),
        },
      });

      return project;
    }),

  // 删除项目
  deleteProject: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 验证项目所有权
      const existingProject = await ctx.db.project.findFirst({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
      });

      if (!existingProject) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Project not found',
        });
      }

      await ctx.db.project.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  // 复制项目
  duplicateProject: protectedProcedure
    .input(z.object({
      id: z.string(),
      name: z.string().min(1).max(100).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 获取原项目
      const originalProject = await ctx.db.project.findFirst({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
      });

      if (!originalProject) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Project not found',
        });
      }

      // 创建副本
      const duplicatedProject = await ctx.db.project.create({
        data: {
          name: input.name ?? `${originalProject.name} (Copy)`,
          description: originalProject.description,
          userId: ctx.session.user.id,
          content: originalProject.content,
          settings: originalProject.settings,
          lastAccessedAt: new Date(),
        },
      });

      return duplicatedProject;
    }),

  // 添加项目历史记录
  addHistory: protectedProcedure
    .input(z.object({
      projectId: z.string(),
      action: z.string(),
      changes: z.record(z.any()).optional(),
      audioUrl: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 验证项目所有权
      const project = await ctx.db.project.findFirst({
        where: {
          id: input.projectId,
          userId: ctx.session.user.id,
        },
      });

      if (!project) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Project not found',
        });
      }

      const history = await ctx.db.projectHistory.create({
        data: {
          projectId: input.projectId,
          userId: ctx.session.user.id,
          action: input.action,
          changes: input.changes,
          audioUrl: input.audioUrl,
        },
      });

      return history;
    }),

  // 获取项目历史记录
  getHistory: protectedProcedure
    .input(z.object({
      projectId: z.string(),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ ctx, input }) => {
      // 验证项目所有权
      const project = await ctx.db.project.findFirst({
        where: {
          id: input.projectId,
          userId: ctx.session.user.id,
        },
      });

      if (!project) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Project not found',
        });
      }

      const history = await ctx.db.projectHistory.findMany({
        where: {
          projectId: input.projectId,
        },
        orderBy: { generatedAt: 'desc' },
        take: input.limit,
        skip: input.offset,
      });

      return history;
    }),
});
