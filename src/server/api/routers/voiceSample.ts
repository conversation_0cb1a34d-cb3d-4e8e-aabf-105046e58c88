import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { db } from "~/server/db";
import { createGeminiTTSClient } from "~/lib/gemini-tts";
import { r2Audio, R2AudioClient } from "~/lib/r2-audio";

export const voiceSampleRouter = createTRPCRouter({
  // 获取支持的TTS模型列表
  getSupportedModels: protectedProcedure
    .query(async () => {
      return {
        models: [
          {
            id: "gemini-2.5-flash-preview-tts",
            name: "Gemini 2.5 Flash TTS",
            description: "快速响应，适合实时应用",
            isDefault: true,
          },
          {
            id: "gemini-2.5-pro-preview-tts", 
            name: "Gemini 2.5 Pro TTS",
            description: "高质量音频，适合正式内容",
            isDefault: false,
          }
        ],
        defaultModel: "gemini-2.5-flash-preview-tts"
      };
    }),

  // 获取角色的语音样本
  getByRoleId: protectedProcedure
    .input(z.object({ roleId: z.string() }))
    .query(async ({ input }) => {
      const role = await db.ttsRole.findUnique({
        where: { id: input.roleId },
        select: {
          id: true,
          name: true,
          languageSupports: {
            include: {
              language: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
        },
      });

      if (!role) {
        return {
          roleId: input.roleId,
          roleName: "Unknown",
          samples: [],
        };
      }

      // 将 RoleLanguageSupport 转换为语音样本格式
      const samples = role.languageSupports.map((support) => ({
        id: support.id,
        languageCode: support.languageCode,
        language: support.language,
        sampleText: support.sampleText,
        sampleUrl: support.sampleUrl,
        quality: support.quality,
        isDefault: support.isDefault,
        createdAt: support.createdAt,
        updatedAt: support.updatedAt,
        roleId: support.roleId,
      }));

      return {
        roleId: input.roleId,
        roleName: role.name,
        samples,
      };
    }),

  // 生成语音样本
  generateSample: protectedProcedure
    .input(z.object({
      roleId: z.string(),
      language: z.string(),
      text: z.string(),
      model: z.enum(["gemini-2.5-flash-preview-tts", "gemini-2.5-pro-preview-tts"]).optional().default("gemini-2.5-flash-preview-tts"),
      quality: z.string().optional().default("standard"),
      isDefault: z.boolean().optional().default(false),
    }))
    .mutation(async ({ input }) => {
      console.log("[VoiceSample] 开始生成语音样本:", {
        roleId: input.roleId,
        language: input.language,
        text: input.text.substring(0, 100),
        model: input.model
      });

      // 检查角色是否存在
      const role = await db.ttsRole.findUnique({
        where: { id: input.roleId },
      });

      if (!role) {
        throw new Error("角色不存在");
      }

      // 检查语言是否存在
      const language = await db.language.findUnique({
        where: { code: input.language },
      });

      if (!language) {
        throw new Error("不支持的语言");
      }

      try {
        // 创建Gemini TTS客户端实例
        const geminiTTS = createGeminiTTSClient(db);
        
        // 调用Gemini TTS API生成音频
        const ttsResponse = await geminiTTS.synthesizeSpeech({
          text: input.text,
          voiceName: role.voiceName, // 使用角色的语音名称
          model: input.model,
        });

        console.log("[VoiceSample] TTS API调用成功:", {
          audioDataLength: ttsResponse.audioContent.length,
          modelUsed: ttsResponse.metadata.modelUsed
        });

        // 生成音频文件名和相关信息
        const textHash = R2AudioClient.generateTextHash(input.text);
        const fileName = R2AudioClient.generateFileName(input.roleId, textHash, "LINEAR16");
        const contentType = R2AudioClient.getContentType("LINEAR16");

        // 上传音频到R2存储
        const uploadResult = await r2Audio.uploadAudio(
          ttsResponse.audioContent,
          fileName,
          contentType,
          "LINEAR16" // Gemini TTS默认返回LINEAR16格式
        );

        console.log("[VoiceSample] 音频上传到R2成功:", {
          url: uploadResult.url,
          key: uploadResult.key,
          size: uploadResult.size
        });

        // 查找或创建 RoleLanguageSupport 记录
        let support = await db.roleLanguageSupport.findFirst({
          where: {
            roleId: input.roleId,
            languageCode: input.language,
          },
        });

        if (support) {
          // 更新现有记录
          support = await db.roleLanguageSupport.update({
            where: { id: support.id },
            data: {
              sampleText: input.text,
              sampleUrl: uploadResult.url,
              quality: input.quality,
              isDefault: input.isDefault,
            },
          });
        } else {
          // 创建新记录
          support = await db.roleLanguageSupport.create({
            data: {
              roleId: input.roleId,
              languageCode: input.language,
              sampleText: input.text,
              sampleUrl: uploadResult.url,
              quality: input.quality,
              isDefault: input.isDefault,
            },
          });
        }

        console.log("[VoiceSample] 语音样本生成完成:", {
          sampleId: support.id,
          sampleUrl: uploadResult.url
        });

        return {
          success: true,
          sample: support,
          audioUrl: uploadResult.url,
          r2Key: uploadResult.key,
          metadata: ttsResponse.metadata,
          message: "语音样本生成成功",
        };
      } catch (error) {
        console.error("[VoiceSample] 生成语音样本失败:", error);
        throw new Error(`语音生成失败: ${error instanceof Error ? error.message : "未知错误"}`);
      }
    }),

  // 更新语音样本文本（不生成音频）
  updateSampleText: protectedProcedure
    .input(z.object({
      id: z.string(),
      sampleText: z.string(),
    }))
    .mutation(async ({ input }) => {
      const support = await db.roleLanguageSupport.update({
        where: { id: input.id },
        data: {
          sampleText: input.sampleText,
        },
      });

      return {
        success: true,
        sample: support,
        message: "试听文本已保存",
      };
    }),

  // 更新语音样本
  updateSample: protectedProcedure
    .input(z.object({
      id: z.string(),
      sampleText: z.string().optional(),
      quality: z.string().optional(),
      isDefault: z.boolean().optional(),
    }))
    .mutation(async ({ input }) => {
      const support = await db.roleLanguageSupport.update({
        where: { id: input.id },
        data: {
          sampleText: input.sampleText,
          quality: input.quality,
          isDefault: input.isDefault,
        },
      });

      return {
        success: true,
        sample: support,
        message: "语音样本已更新",
      };
    }),

  // 删除语音样本
  deleteSample: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      await db.roleLanguageSupport.delete({
        where: { id: input.id },
      });

      return {
        success: true,
        message: "语音样本已删除",
      };
    }),
});
