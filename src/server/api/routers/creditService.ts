import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { TransactionType, PricingType, ModelType } from "@prisma/client";

import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "~/server/api/trpc";

// 默认积分兑换比例：1美元 = 1000积分
const DEFAULT_USD_TO_CREDITS_RATE = 1000;

// 获取当前积分兑换比例
async function getCreditExchangeRate(db: any): Promise<number> {
  const setting = await db.systemSettings.findUnique({
    where: { key: "CREDIT_EXCHANGE_RATE" },
  });
  
  if (setting) {
    const rate = parseFloat(setting.value);
    return isNaN(rate) ? DEFAULT_USD_TO_CREDITS_RATE : rate;
  }
  
  return DEFAULT_USD_TO_CREDITS_RATE;
}

// 定价类型枚举
const PricingTypeEnum = z.enum([
  "TOKEN", "REQUEST", "CHARACTER", "IMAGE", "VIDEO", "AUDIO"
]);

// 消耗积分的输入参数
const ConsumeCreditsSchema = z.object({
  modelId: z.string(),
  pricingType: PricingTypeEnum,
  usage: z.object({
    // TOKEN类型
    inputTokens: z.number().optional(),
    outputTokens: z.number().optional(),
    // 其他类型
    units: z.number().optional(), // 通用单位数量
    // 请求相关
    requestCount: z.number().optional(),
    // 字符相关  
    characterCount: z.number().optional(),
    // 媒体相关
    duration: z.number().optional(), // 视频/音频时长（秒）
    imageCount: z.number().optional(),
  }),
  metadata: z.record(z.any()).optional(), // 额外元数据
});

export const creditServiceRouter = createTRPCRouter({
  // 公开的积分查询（用于未登录用户）
  getPublicCreditInfo: publicProcedure.query(async ({ ctx }) => {
    // 未登录用户返回默认信息
    if (!ctx.session?.user) {
      return {
        balance: 0,
        isLoggedIn: false,
        exchangeRate: await getCreditExchangeRate(ctx.db),
      };
    }

    // 已登录用户返回实际积分信息
    let userCredit = await ctx.db.userCredit.findUnique({
      where: { userId: ctx.session.user.id },
    });

    // 如果用户没有积分记录，创建一个
    if (!userCredit) {
      userCredit = await ctx.db.userCredit.create({
        data: {
          userId: ctx.session.user.id,
          balance: 0,
        },
      });
    }

    return {
      balance: userCredit.balance,
      isLoggedIn: true,
      exchangeRate: await getCreditExchangeRate(ctx.db),
    };
  }),

  // 获取用户积分余额（需要登录）
  getUserCredits: protectedProcedure
    .query(async ({ ctx }) => {
      const userId = ctx.session.user.id;
      
      let userCredit = await ctx.db.userCredit.findUnique({
        where: { userId },
      });

      // 如果用户没有积分记录，创建一个
      if (!userCredit) {
        userCredit = await ctx.db.userCredit.create({
          data: {
            userId,
            balance: 0,
            totalEarned: 0,
            totalSpent: 0,
          },
        });
      }

      const exchangeRate = await getCreditExchangeRate(ctx.db);
      
      return {
        balance: userCredit.balance,
        totalEarned: userCredit.totalEarned,
        totalSpent: userCredit.totalSpent,
        usdEquivalent: userCredit.balance / exchangeRate,
        exchangeRate,
      };
    }),

  // 计算消耗积分数量（不实际扣除）
  calculateCredits: protectedProcedure
    .input(ConsumeCreditsSchema)
    .query(async ({ ctx, input }) => {
      // 暂时禁用此功能
      throw new TRPCError({
        code: "NOT_IMPLEMENTED",
        message: "积分计算功能暂时禁用",
      });

    }),

  // 消耗积分
  consumeCredits: protectedProcedure
    .input(ConsumeCreditsSchema.extend({
      description: z.string().optional(),
      orderId: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 暂时禁用此功能
      throw new TRPCError({
        code: "NOT_IMPLEMENTED",
        message: "积分消费功能暂时禁用",
      });
    })
});