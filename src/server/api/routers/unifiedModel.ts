import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";
import type { ModelType, PricingType } from "@prisma/client";

// 统一模型管理路由
export const unifiedModelRouter = createTRPCRouter({
  // 获取所有模型及其相关信息
  getAllModels: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      search: z.string().optional(),
      modelType: z.string().optional(),
      isActive: z.boolean().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { page, limit, search, modelType, isActive } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(search && {
          OR: [
            { name: { contains: search, mode: "insensitive" as const } },
            { displayName: { contains: search, mode: "insensitive" as const } },
            { description: { contains: search, mode: "insensitive" as const } },
          ],
        }),
        ...(modelType && { modelType: modelType as ModelType }),
        ...(isActive !== undefined && { isActive }),
      };

      const [models, total] = await Promise.all([
        ctx.db.model.findMany({
          where,
          skip,
          take: limit,
          include: {
            provider: true,
            customPricings: {
              where: { isActive: true },
              orderBy: { createdAt: "desc" },
              take: 1,
            },
            _count: {
              select: {
                customPricings: true,
                usages: true,
              }
            }
          },
          orderBy: { createdAt: "desc" },
        }),
        ctx.db.model.count({ where }),
      ]);

      return {
        models,
        total,
        pages: Math.ceil(total / limit),
        currentPage: page,
      };
    }),

  // 获取模型统计信息
  getModelStats: protectedProcedure
    .query(async ({ ctx }) => {
      const [
        totalModels,
        activeModels,
        totalProviders,
        activeProviders,
        totalPricings,
        activePricings,
      ] = await Promise.all([
        ctx.db.model.count(),
        ctx.db.model.count({ where: { isActive: true } }),
        ctx.db.modelProvider.count(),
        ctx.db.modelProvider.count({ where: { isActive: true } }),
        ctx.db.customPricing.count(),
        ctx.db.customPricing.count({ where: { isActive: true } }),
      ]);

      return {
        models: { total: totalModels, active: activeModels },
        providers: { total: totalProviders, active: activeProviders },
        pricings: { total: totalPricings, active: activePricings },
      };
    }),

  // 创建模型
  createModel: protectedProcedure
    .input(z.object({
      name: z.string().min(1),
      displayName: z.string().optional(),
      description: z.string().optional(),
      modelType: z.string(),
      providerId: z.string(),
      isActive: z.boolean().default(true),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查模型名称是否已存在
      const existingModel = await ctx.db.model.findFirst({
        where: { 
          name: input.name,
          providerId: input.providerId 
        },
      });

      if (existingModel) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "该提供商下已存在同名模型",
        });
      }

      return ctx.db.model.create({
        data: {
          name: input.name,
          displayName: input.displayName,
          description: input.description,
          modelType: input.modelType as ModelType,
          providerId: input.providerId,
          isActive: input.isActive,
        },
        include: {
          provider: true,
          _count: {
            select: {
              customPricings: true,
              usages: true,
            }
          }
        },
      });
    }),

  // 更新模型
  updateModel: protectedProcedure
    .input(z.object({
      id: z.string(),
      name: z.string().min(1).optional(),
      displayName: z.string().optional(),
      description: z.string().optional(),
      modelType: z.string().optional(),
      providerId: z.string().optional(),
      isActive: z.boolean().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      // 检查模型是否存在
      const existingModel = await ctx.db.model.findUnique({
        where: { id },
      });

      if (!existingModel) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "模型不存在",
        });
      }

      // 如果更新模型名称，检查是否冲突
      if (updateData.name && updateData.name !== existingModel.name) {
        const conflictModel = await ctx.db.model.findFirst({
          where: { 
            name: updateData.name,
            providerId: updateData.providerId || existingModel.providerId,
            id: { not: id }
          },
        });

        if (conflictModel) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "该提供商下已存在同名模型",
          });
        }
      }

      const updateFields: any = {};
      
      if (updateData.name) updateFields.name = updateData.name;
      if (updateData.displayName !== undefined) updateFields.displayName = updateData.displayName;
      if (updateData.description !== undefined) updateFields.description = updateData.description;
      if (updateData.modelType) updateFields.modelType = updateData.modelType as ModelType;
      if (updateData.providerId) updateFields.providerId = updateData.providerId;
      if (updateData.isActive !== undefined) updateFields.isActive = updateData.isActive;

      return ctx.db.model.update({
        where: { id },
        data: updateFields,
        include: {
          provider: true,
          _count: {
            select: {
              customPricings: true,
              usages: true,
            }
          }
        },
      });
    }),

  // 删除模型
  deleteModel: protectedProcedure
    .input(z.object({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查模型是否存在
      const existingModel = await ctx.db.model.findUnique({
        where: { id: input.id },
        include: {
          customPricings: true,
          usages: true,
        },
      });

      if (!existingModel) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "模型不存在",
        });
      }

      // 检查是否有关联的定价或使用记录
      if (existingModel.customPricings.length > 0 || existingModel.usages.length > 0) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "无法删除有关联定价配置或使用记录的模型",
        });
      }

      return ctx.db.model.delete({
        where: { id: input.id },
      });
    }),

  // 批量操作
  batchUpdateModels: protectedProcedure
    .input(z.object({
      ids: z.array(z.string()),
      action: z.enum(["activate", "deactivate", "delete"]),
    }))
    .mutation(async ({ ctx, input }) => {
      const { ids, action } = input;

      switch (action) {
        case "activate":
          return ctx.db.model.updateMany({
            where: { id: { in: ids } },
            data: { isActive: true },
          });

        case "deactivate":
          return ctx.db.model.updateMany({
            where: { id: { in: ids } },
            data: { isActive: false },
          });

        case "delete":
          // 检查是否有关联数据
          const modelsWithRelations = await ctx.db.model.findMany({
            where: { id: { in: ids } },
            include: {
              customPricings: true,
              usages: true,
            },
          });

          const hasRelations = modelsWithRelations.some(
            (model) => model.customPricings.length > 0 || model.usages.length > 0
          );

          if (hasRelations) {
            throw new TRPCError({
              code: "PRECONDITION_FAILED",
              message: "部分模型有关联数据，无法删除",
            });
          }

          return ctx.db.model.deleteMany({
            where: { id: { in: ids } },
          });

        default:
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "无效的操作类型",
          });
      }
    }),

  // 获取模型使用统计
  getModelUsageStats: protectedProcedure
    .input(z.object({
      modelId: z.string(),
      days: z.number().min(1).max(365).default(30),
    }))
    .query(async ({ ctx, input }) => {
      const { modelId, days } = input;
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const usageStats = await ctx.db.modelUsage.groupBy({
        by: ["createdAt"],
        where: {
          modelId,
          createdAt: {
            gte: startDate,
          },
        },
        _sum: {
          inputTokens: true,
          outputTokens: true,
          costUsd: true,
        },
        _count: {
          id: true,
        },
        orderBy: {
          createdAt: "asc",
        },
      });

      return usageStats.map(stat => ({
        date: stat.createdAt,
        requests: stat._count?.id || 0,
        inputTokens: stat._sum?.inputTokens || 0,
        outputTokens: stat._sum?.outputTokens || 0,
        totalCost: stat._sum?.costUsd || 0,
      }));
    }),
});