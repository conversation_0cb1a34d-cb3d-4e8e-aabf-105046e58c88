import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { UserRole, OrderStatus } from "@prisma/client";

import {
  createTRPCRouter,
  protectedProcedure,
} from "~/server/api/trpc";

// 管理员权限中间件
const adminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  if (!ctx.session?.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  
  // 从数据库获取用户角色
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });
  
  if (!user || (user.role !== UserRole.ADMIN && user.role !== UserRole.SUPER_ADMIN)) {
    throw new TRPCError({ code: "FORBIDDEN" });
  }
  
  return next({
    ctx: {
      ...ctx,
      session: {
        ...ctx.session,
        user: {
          ...ctx.session.user,
          role: user.role,
        },
      },
    },
  });
});

export const orderRouter = createTRPCRouter({
  // 获取订单列表
  getOrders: adminProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        status: z.nativeEnum(OrderStatus).optional(),
        userId: z.string().optional(),
        search: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, status, userId, search } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(status && { status }),
        ...(userId && { userId }),
        ...(search && {
          OR: [
            {
              user: {
                OR: [
                  { name: { contains: search, mode: "insensitive" as const } },
                  { email: { contains: search, mode: "insensitive" as const } },
                ],
              },
            },
            {
              package: {
                name: { contains: search, mode: "insensitive" as const },
              },
            },
          ],
        }),
      };

      const [orders, total] = await Promise.all([
        ctx.db.order.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            package: {
              select: {
                id: true,
                name: true,
                type: true,
                credits: true,
                bonusCredits: true,
              },
            },
          },
        }),
        ctx.db.order.count({ where }),
      ]);

      return {
        orders,
        total,
        pages: Math.ceil(total / limit),
        currentPage: page,
      };
    }),

  // 获取单个订单详情
  getOrderById: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const order = await ctx.db.order.findUnique({
        where: { id: input.id },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
          package: true,
        },
      });

      if (!order) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      return order;
    }),

  // 更新订单状态
  updateOrderStatus: adminProcedure
    .input(
      z.object({
        id: z.string(),
        status: z.nativeEnum(OrderStatus),
        notes: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, status, notes } = input;

      const existingOrder = await ctx.db.order.findUnique({
        where: { id },
        include: {
          user: {
            include: {
              credit: true,
            },
          },
          package: true,
        },
      });

      if (!existingOrder) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      // 如果订单状态从非PAID变为PAID，需要给用户添加积分
      if (existingOrder.status !== OrderStatus.PAID && status === OrderStatus.PAID) {
        return await ctx.db.$transaction(async (tx) => {
          // 更新订单状态
          const updatedOrder = await tx.order.update({
            where: { id },
            data: {
              status,
              updatedAt: new Date(),
            },
          });

          // 确保用户有积分记录
          let userCredit = existingOrder.user.credit;
          if (!userCredit) {
            userCredit = await tx.userCredit.create({
              data: {
                userId: existingOrder.userId,
                balance: 0,
                totalEarned: 0,
                totalSpent: 0,
              },
            });
          }

          // 计算总积分（基础积分 + 赠送积分）
          const totalCredits = existingOrder.package!.credits + existingOrder.package!.bonusCredits;

          // 更新用户积分
          await tx.userCredit.update({
            where: { userId: existingOrder.userId },
            data: {
              balance: userCredit.balance + totalCredits,
              totalEarned: userCredit.totalEarned + totalCredits,
            },
          });

          // 创建积分交易记录
          await tx.creditTransaction.create({
            data: {
              userId: existingOrder.userId,
              creditId: userCredit.id,
              type: "PURCHASE",
              amount: totalCredits,
              description: `Purchase: ${existingOrder.package!.name}`,
              orderId: existingOrder.id,
            },
          });

          return updatedOrder;
        });
      }

      // 如果订单状态从PAID变为REFUNDED，需要扣除用户积分
      if (existingOrder.status === OrderStatus.PAID && status === OrderStatus.REFUNDED) {
        return await ctx.db.$transaction(async (tx) => {
          // 更新订单状态
          const updatedOrder = await tx.order.update({
            where: { id },
            data: {
              status,
              updatedAt: new Date(),
            },
          });

          if (existingOrder.user.credit) {
            const totalCredits = existingOrder.package!.credits + existingOrder.package!.bonusCredits;
            const newBalance = existingOrder.user.credit.balance - totalCredits;

            // 检查积分余额是否足够
            if (newBalance < 0) {
              throw new TRPCError({
                code: "BAD_REQUEST",
                message: "Insufficient credit balance for refund",
              });
            }

            // 更新用户积分
            await tx.userCredit.update({
              where: { userId: existingOrder.userId },
              data: {
                balance: newBalance,
                totalSpent: existingOrder.user.credit.totalSpent + totalCredits,
              },
            });

            // 创建积分交易记录
            await tx.creditTransaction.create({
              data: {
                userId: existingOrder.userId,
                creditId: existingOrder.user.credit.id,
                type: "REFUND",
                amount: -totalCredits,
                description: `Refund: ${existingOrder.package!.name}`,
                orderId: existingOrder.id,
              },
            });
          }

          return updatedOrder;
        });
      }

      // 其他状态更新
      return await ctx.db.order.update({
        where: { id },
        data: {
          status,
          updatedAt: new Date(),
        },
      });
    }),

  // 获取订单统计
  getOrderStats: adminProcedure.query(async ({ ctx }) => {
    const [totalOrders, paidOrders, pendingOrders, failedOrders, refundedOrders, totalRevenue] = await Promise.all([
      ctx.db.order.count(),
      ctx.db.order.count({ where: { status: OrderStatus.PAID } }),
      ctx.db.order.count({ where: { status: OrderStatus.PENDING } }),
      ctx.db.order.count({ where: { status: OrderStatus.FAILED } }),
      ctx.db.order.count({ where: { status: OrderStatus.REFUNDED } }),
      ctx.db.order.aggregate({
        where: { status: OrderStatus.PAID },
        _sum: { amount: true },
      }),
    ]);

    return {
      totalOrders,
      paidOrders,
      pendingOrders,
      failedOrders,
      refundedOrders,
      totalRevenue: totalRevenue._sum.amount || 0,
    };
  }),

  // 获取最近7天的订单趋势
  getOrderTrends: adminProcedure.query(async ({ ctx }) => {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const orders = await ctx.db.order.findMany({
      where: {
        createdAt: {
          gte: sevenDaysAgo,
        },
      },
      select: {
        createdAt: true,
        status: true,
        amount: true,
      },
    });

    // 按日期分组统计
    const dailyStats = new Map();
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateKey = date.toISOString().split('T')[0];
      dailyStats.set(dateKey, {
        date: dateKey,
        orders: 0,
        revenue: 0,
        paidOrders: 0,
      });
    }

    orders.forEach(order => {
      const dateKey = order.createdAt.toISOString().split('T')[0];
      const stats = dailyStats.get(dateKey);
      if (stats) {
        stats.orders += 1;
        if (order.status === OrderStatus.PAID) {
          stats.revenue += order.amount;
          stats.paidOrders += 1;
        }
      }
    });

    return Array.from(dailyStats.values());
  }),
});