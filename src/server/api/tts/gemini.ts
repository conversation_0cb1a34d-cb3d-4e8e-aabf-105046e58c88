/* Gemini TTS 适配（占位实现）
   注意：请根据最新 Gemini TTS API 文档完善此实现，并配置 GEMINI_API_KEY
*/
export type GeminiTtsParams = {
  text: string;
  language?: string; // BCP-47, 如 "zh-CN"
  voice?: string;    // 角色/音色名
  model?: string;    // 默认从角色配置读取
  format?: "mp3" | "wav" | "ogg";
};

export async function synthesizeWithGemini(_params: GeminiTtsParams): Promise<Buffer> {
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) throw new Error("GEMINI_API_KEY 未配置");

  // TODO: 使用官方 SDK 或 HTTP API 完成调用，返回音频二进制 Buffer
  // 为避免伪造接口，这里仅抛出说明性错误，待你填充具体实现。
  throw new Error("请接入 Gemini TTS API 实现合成逻辑（返回音频 Buffer）");
}