/* R2 存储适配（S3 兼容），动态导入 AWS SDK 以避免构建期依赖 */
export type R2UploadResult = { key: string; url: string };

function r2Endpoint() {
  const accountId = process.env.R2_ACCOUNT_ID;
  if (!accountId) throw new Error("R2_ACCOUNT_ID 未配置");
  return `https://${accountId}.r2.cloudflarestorage.com`;
}

function r2PublicBase() {
  const base = process.env.R2_PUBLIC_BASE;
  if (!base) throw new Error("R2_PUBLIC_BASE 未配置");
  return base.endsWith("/") ? base : base + "/";
}

async function getS3() {
  // 动态导入，防止未安装时编译失败
  // 需要依赖：npm i @aws-sdk/client-s3
  // @ts-ignore
  const mod = await import("@aws-sdk/client-s3").catch(() => null);
  if (!mod) {
    throw new Error("缺少 @aws-sdk/client-s3 依赖，请安装后再试：npm i @aws-sdk/client-s3");
  }
  const { S3Client } = mod;
  const region = "auto";
  const endpoint = r2Endpoint();
  const accessKeyId = process.env.R2_ACCESS_KEY_ID;
  const secretAccessKey = process.env.R2_SECRET_ACCESS_KEY;
  if (!accessKeyId || !secretAccessKey) throw new Error("R2_ACCESS_KEY_ID / R2_SECRET_ACCESS_KEY 未配置");

  return new S3Client({
    region,
    endpoint,
    credentials: { accessKeyId, secretAccessKey },
  });
}

export async function r2UploadBuffer(params: {
  bucket?: string;
  key: string;
  buffer: Buffer;
  contentType?: string;
  cacheControl?: string;
}): Promise<R2UploadResult> {
  // @ts-ignore
  const mod = await import("@aws-sdk/client-s3").catch(() => null);
  if (!mod) throw new Error("缺少 @aws-sdk/client-s3 依赖，请安装：npm i @aws-sdk/client-s3");
  const { PutObjectCommand } = mod;

  const client = await getS3();
  const Bucket = params.bucket ?? process.env.R2_BUCKET;
  if (!Bucket) throw new Error("R2_BUCKET 未配置");
  const Key = params.key;
  const Body = params.buffer;
  const ContentType = params.contentType ?? "audio/mpeg";
  const CacheControl = params.cacheControl ?? "public, max-age=31536000, immutable";

  await client.send(new PutObjectCommand({ Bucket, Key, Body, ContentType, CacheControl }));
  const url = r2PublicBase() + Key;
  return { key: Key, url };
}