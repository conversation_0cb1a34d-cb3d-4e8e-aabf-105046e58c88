import { PrismaAdapter } from "@auth/prisma-adapter";
import { type DefaultSession, type NextAuthConfig } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";

import { db } from "~/server/db";

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      role?: string;
      // ...other properties
    } & DefaultSession["user"];
  }

  interface User {
    role?: string;
    // ...other properties
  }
}

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 */
export const authConfig = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
         if (!credentials?.email || !credentials?.password) {
           return null;
         }

         const user = await db.user.findUnique({
           where: {
             email: credentials.email as string
           }
         }) as any;

         if (!user || !user.password) {
           return null;
         }

         const isPasswordValid = await bcrypt.compare(
           credentials.password as string,
           user.password
         );

         if (!isPasswordValid) {
           return null;
         }

         return {
           id: user.id,
           email: user.email,
           name: user.name,
           image: user.image,
         };
       },
    }),
  ],
  adapter: PrismaAdapter(db),
  session: {
    strategy: "jwt",
  },
  callbacks: {
    jwt: async ({ token, user }) => {
      if (user) {
        // 从数据库获取用户完整信息包括角色
        const dbUser = await db.user.findUnique({
          where: { id: user.id },
          select: { id: true, email: true, name: true, image: true, role: true }
        });
        
        if (dbUser) {
          token.id = dbUser.id;
          token.role = dbUser.role;
        }
      }
      return token;
    },
    session: ({ session, token }) => ({
      ...session,
      user: {
        ...session.user,
        id: token.id as string,
        role: token.role as string,
      },
    }),
  },
} satisfies NextAuthConfig;
