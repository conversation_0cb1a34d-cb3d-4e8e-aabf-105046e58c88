// import { GEMINI_OFFICIAL_PRICING } from "./token-utils"; // 暂时禁用

export interface OfficialTokenPricing {
  inputTokenPrice?: number;
  outputTokenPrice?: number;
  requestPrice?: number;
  description?: string;
}

// 根据官方定价和倍率生成新的销售单价
export function applyMultiplierToPricing(official: OfficialTokenPricing, multiplier: number) {
  return {
    inputTokenPrice: official.inputTokenPrice !== undefined ? round6(official.inputTokenPrice * multiplier) : undefined,
    outputTokenPrice: official.outputTokenPrice !== undefined ? round6(official.outputTokenPrice * multiplier) : undefined,
    requestPrice: official.requestPrice !== undefined ? round6(official.requestPrice * multiplier) : undefined,
  };
}

function round6(n: number){
  return parseFloat(n.toFixed(9)); // 保留足够精度，避免浮点累计误差
}

// 获取官方定价（如果存在）
export function getOfficialPricingSnapshot(modelName: string): OfficialTokenPricing | null {
  // 暂时禁用
  return null;
}

// 计算token型使用成本
export function computeTokenCost(args: {
  inputTokens: number;
  outputTokens: number;
  pricing: { inputTokenPrice?: number; outputTokenPrice?: number; requestPrice?: number; multiplier?: number };
}) {
  const { inputTokens, outputTokens, pricing } = args;
  const cost = (inputTokens * (pricing.inputTokenPrice || 0)) + (outputTokens * (pricing.outputTokenPrice || 0)) + (pricing.requestPrice || 0);
  return cost;
}

// 计算相对涨幅（返回百分数字符串, 若基准无效返回 null）
export function calcChangePercent(base?: number, sale?: number): string | null {
  if(base === undefined || sale === undefined) return null;
  if(base === 0) return null;
  const pct = ((sale - base)/base) * 100;
  const sign = pct > 0 ? '+' : '';
  return sign + pct.toFixed(Math.abs(pct) < 1 ? 2 : 1) + '%';
}
