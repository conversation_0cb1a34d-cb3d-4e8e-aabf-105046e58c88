import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { env } from "~/env";

// 这个文件只在服务端使用，不应该被客户端组件直接导入
// 客户端应该通过tRPC API调用这些函数

export const r2 = new S3Client({
  region: "auto",
  endpoint: `https://${env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: env.R2_ACCESS_KEY_ID,
    secretAccessKey: env.R2_SECRET_ACCESS_KEY,
  },
});

/**
 * 上传头像图片到R2存储 (仅服务端使用)
 * @param imageData - Base64编码的图片数据 (data:image/jpeg;base64,... 格式)
 * @param fileName - 文件名 (不包含扩展名)
 * @returns 公共访问URL
 */
export async function uploadAvatarToR2(
  imageData: string,
  fileName: string
): Promise<string> {
  try {
    console.log('[R2 Avatar Upload] 开始上传头像', {
      fileName,
      imageDataLength: imageData.length,
      imageDataPreview: imageData.substring(0, 100) + '...',
      bucket: env.R2_BUCKET_NAME,
      publicUrl: env.R2_PUBLIC_URL
    });
    
    // 解析base64数据格式: data:image/jpeg;base64,/9j/4AAQSkZ...
    const matches = imageData.match(/^data:image\/(\w+);base64,(.+)$/);
    if (!matches || matches.length < 3) {
      throw new Error('Invalid image data format. Expected data:image/type;base64,data');
    }
    
    const imageType = matches[1]!; // jpeg, png, webp等
    const base64Data = matches[2]!; // 纯base64数据
    
    console.log('[R2 Avatar Upload] 解析图片格式', {
      imageType,
      base64DataLength: base64Data.length
    });
    
    // 解码base64图片数据
    const imageBuffer = Buffer.from(base64Data, 'base64');
    
    console.log('[R2 Avatar Upload] 图片数据解码完成', {
      bufferSize: imageBuffer.length,
      imageType
    });
    
    // 生成唯一的头像文件名
    const timestamp = Date.now();
    const uniqueFileName = `avatars/${timestamp}-${fileName}.${imageType}`;
    
    console.log('[R2 Avatar Upload] 生成唯一文件名', {
      originalFileName: fileName,
      uniqueFileName,
      timestamp
    });
    
    // 确定Content-Type
    const contentType = `image/${imageType}`;
    
    // 上传到R2
    const uploadResult = await r2.send(
      new PutObjectCommand({
        Bucket: env.R2_BUCKET_NAME,
        Key: uniqueFileName,
        Body: imageBuffer,
        ContentType: contentType,
        ContentLength: imageBuffer.length,
        // 设置缓存控制
        CacheControl: 'public, max-age=31536000', // 1年缓存
        // 设置元数据
        Metadata: {
          'upload-type': 'avatar',
          'original-name': fileName,
          'uploaded-at': new Date().toISOString()
        }
      })
    );
    
    console.log('[R2 Avatar Upload] 上传完成', {
      uploadResult: {
        ETag: uploadResult.ETag,
        VersionId: uploadResult.VersionId
      },
      uniqueFileName,
      contentType
    });
    
    // 构建公共访问URL
    const publicUrl = `${env.R2_PUBLIC_URL}/${uniqueFileName}`;
    
    console.log('[R2 Avatar Upload] 生成公共访问URL', {
      publicUrl,
      fileName: uniqueFileName
    });
    
    return publicUrl;
  } catch (error) {
    console.error('[R2 Avatar Upload] 上传失败:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      fileName,
      imageDataLength: imageData.length,
      bucket: env.R2_BUCKET_NAME
    });
    throw new Error(`Failed to upload avatar to R2: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
