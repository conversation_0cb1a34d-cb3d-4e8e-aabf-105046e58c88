// 客户端安全的头像工具函数
// 这个文件可以被客户端组件安全导入

/**
 * 验证图片文件
 * @param file - 文件对象
 * @returns 验证结果
 */
export function validateAvatarFile(file: File): { valid: boolean; error?: string } {
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: '不支持的图片格式。请上传 JPEG, PNG, WebP 或 GIF 格式的图片。'
    };
  }
  
  // 检查文件大小 (最大5MB)
  const maxSize = 5 * 1024 * 1024; // 5MB in bytes
  if (file.size > maxSize) {
    return {
      valid: false,
      error: '图片文件过大。请上传小于5MB的图片。'
    };
  }
  
  return { valid: true };
}

/**
 * 将文件转换为base64格式
 * @param file - 文件对象
 * @returns Promise<string> - base64编码的数据URL
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to read file as base64'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
}
