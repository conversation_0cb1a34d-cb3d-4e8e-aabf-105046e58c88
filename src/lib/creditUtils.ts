import { PricingType } from "@prisma/client";

// 积分兑换比例：1美元 = 1000积分
export const USD_TO_CREDITS_RATE = 1000;

// 使用量接口
export interface UsageData {
  // TOKEN类型
  inputTokens?: number;
  outputTokens?: number;
  // 其他类型
  units?: number; // 通用单位数量
  // 请求相关
  requestCount?: number;
  // 字符相关  
  characterCount?: number;
  // 媒体相关
  duration?: number; // 视频/音频时长（秒）
  imageCount?: number;
}

// 积分消费参数
export interface ConsumeCreditsParams {
  modelId: string;
  pricingType: PricingType;
  usage: UsageData;
  description?: string;
  metadata?: Record<string, any>;
}

// 积分计算结果
export interface CreditCalculation {
  costUsd: number;
  creditsNeeded: number;
  pricingDetails: {
    modelId: string;
    pricingType: PricingType;
    usage: UsageData;
    modelPricing: any;
  };
}

// 积分消费结果
export interface ConsumeCreditsResult {
  success: boolean;
  creditsDeducted: number;
  costUsd: number;
  remainingBalance: number;
  transactionId: string;
  usageId: string;
  usageDescription: string;
}

// 格式化积分数量
export function formatCredits(credits: number): string {
  return credits.toLocaleString();
}

// 积分转美元
export function creditsToUsd(credits: number): number {
  return credits / USD_TO_CREDITS_RATE;
}

// 美元转积分
export function usdToCredits(usd: number): number {
  return Math.ceil(usd * USD_TO_CREDITS_RATE);
}

// 格式化定价类型
export function formatPricingType(type: PricingType): string {
  const types: Record<PricingType, string> = {
    TOKEN: "Token计费",
    REQUEST: "请求计费", 
    CHARACTER: "字符计费",
    IMAGE: "图片计费",
    VIDEO: "视频计费",
    AUDIO: "音频计费",
  };
  return types[type] || type;
}

// 格式化使用量描述
export function formatUsageDescription(pricingType: PricingType, usage: UsageData): string {
  switch (pricingType) {
    case "TOKEN":
      return `输入${usage.inputTokens || 0}tokens, 输出${usage.outputTokens || 0}tokens`;
    case "REQUEST":
      return `${usage.requestCount || 1}次请求`;
    case "CHARACTER":
      return `${usage.characterCount || 0}个字符`;
    case "IMAGE":
      return `${usage.imageCount || 1}张图片`;
    case "VIDEO":
      return `${usage.duration || 0}秒视频`;
    case "AUDIO":
      return `${usage.duration || 0}秒音频`;
    default:
      return `${usage.units || 1}个单位`;
  }
}

// 验证使用量数据
export function validateUsageData(pricingType: PricingType, usage: UsageData): boolean {
  switch (pricingType) {
    case "TOKEN":
      return (usage.inputTokens !== undefined && usage.inputTokens >= 0) ||
             (usage.outputTokens !== undefined && usage.outputTokens >= 0);
    case "REQUEST":
      return (usage.requestCount !== undefined && usage.requestCount > 0);
    case "CHARACTER":
      return (usage.characterCount !== undefined && usage.characterCount > 0);
    case "IMAGE":
      return (usage.imageCount !== undefined && usage.imageCount > 0);
    case "VIDEO":
    case "AUDIO":
      return (usage.duration !== undefined && usage.duration > 0);
    default:
      return (usage.units !== undefined && usage.units > 0);
  }
}

// 计算预估成本（不调用API）
export function estimateCost(
  pricingType: PricingType, 
  usage: UsageData, 
  modelPricing: any
): { costUsd: number; creditsNeeded: number } {
  let costUsd = 0;

  switch (pricingType) {
    case "TOKEN":
      const inputCost = (usage.inputTokens || 0) * (modelPricing.inputTokenPrice || 0) / 1000000;
      const outputCost = (usage.outputTokens || 0) * (modelPricing.outputTokenPrice || 0) / 1000000;
      costUsd = inputCost + outputCost;
      break;

    case "REQUEST":
      costUsd = (usage.requestCount || 1) * (modelPricing.requestPrice || 0);
      break;

    case "CHARACTER":
      costUsd = (usage.characterCount || 0) * (modelPricing.characterPrice || 0) / 1000;
      break;

    case "IMAGE":
      costUsd = (usage.imageCount || 1) * (modelPricing.imagePrice || 0);
      break;

    case "VIDEO":
      costUsd = (usage.duration || 0) * (modelPricing.videoPricePerSecond || 0);
      break;

    case "AUDIO":
      costUsd = (usage.duration || 0) * (modelPricing.audioPricePerSecond || 0);
      break;
  }

  const creditsNeeded = usdToCredits(costUsd);

  return { costUsd, creditsNeeded };
}