import { PrismaClient } from "@prisma/client";

/**
 * Minimax TTS请求参数
 */
export interface MinimaxTTSRequest {
  text: string;           // 文本内容（最大5000字符）
  voice_id?: string;      // 语音ID（预置或克隆）
  model?: string;         // 模型名称
  speed?: number;         // 语音速度 (0.5-2.0)
  pitch?: number;         // 音调调节
  emotion?: string;       // 情感表达
  language?: string;      // 语言代码
  format?: 'mp3' | 'wav'; // 输出格式
}

/**
 * Minimax TTS响应数据
 */
export interface MinimaxTTSResponse {
  audioContent: Buffer;   // 音频数据
  metadata: {
    duration: number;     // 时长（秒）
    charactersUsed: number; // 字符数
    modelUsed: string;    // 使用的模型
    voiceId: string;      // 语音ID
    format: string;       // 音频格式
  };
}

/**
 * Minimax提供商配置
 */
interface MinimaxConfig {
  apiKey: string;
  groupId: string;
  baseUrl: string;
}

/**
 * Minimax语音信息
 */
export interface MinimaxVoice {
  voice_id: string;         // 语音ID
  name: string;             // 语音名称
  description?: string;     // 语音描述
  gender?: string;          // 性别
  language?: string;        // 支持的语言
  style?: string;           // 语音风格
  sample_url?: string;      // 示例音频URL
  is_cloned?: boolean;      // 是否为克隆语音
  created_at?: string;      // 创建时间
  voice_type?: string;      // 语音类型（voice_cloning, preset, basic等）
}

/**
 * Minimax语音列表响应
 */
export interface MinimaxVoiceListResponse {
  base_resp: {
    status_code: number;
    status_msg: string;
  };
  voices?: MinimaxVoice[];
}

/**
 * Minimax API错误响应
 */
interface MinimaxErrorResponse {
  base_resp: {
    status_code: number;
    status_msg: string;
  };
}

/**
 * Minimax TTS客户端类
 * 实现Minimax T2A V2 API的文本转语音功能
 */
export class MinimaxTTSClient {
  private db: PrismaClient;
  private config: MinimaxConfig | null = null;

  constructor(db: PrismaClient) {
    this.db = db;
  }

  /**
   * 获取Minimax提供商配置
   */
  private async getConfig(): Promise<MinimaxConfig> {
    if (!this.config) {
      const provider = await this.db.modelProvider.findFirst({
        where: {
          slug: "minimax",
          isActive: true,
        },
      });

      if (!provider) {
        throw new Error("Minimax provider not found");
      }

      if (!provider.apiKey) {
        throw new Error("Minimax API key not configured");
      }

      const config = provider.config as any;
      if (!config?.groupId) {
        throw new Error("Minimax GroupID not configured");
      }

      this.config = {
        apiKey: provider.apiKey,
        groupId: config.groupId,
        baseUrl: config.baseUrl || "https://api.minimaxi.chat"
      };
    }
    return this.config;
  }

  /**
   * 处理Minimax API错误
   */
  private handleError(error: any): Error {
    const errorMap: Record<number, string> = {
      1000: "未知错误",
      1001: "请求超时",
      1002: "触发RPM限制",
      1004: "认证失败",
      1039: "触发TPM限制",
      1042: "非法字符超过最大限制",
      2013: "输入格式无效"
    };

    if (error.base_resp?.status_code) {
      const message = errorMap[error.base_resp.status_code] || error.base_resp.status_msg || "未知错误";
      return new Error(`Minimax API错误 (${error.base_resp.status_code}): ${message}`);
    }

    return new Error(`Minimax API错误: ${error.message || "未知错误"}`);
  }

  /**
   * 文本转语音
   */
  async synthesizeSpeech(request: MinimaxTTSRequest): Promise<MinimaxTTSResponse> {
    const config = await this.getConfig();

    console.log('[Minimax TTS] 开始生成语音', {
      text: request.text.substring(0, 50) + '...',
      textLength: request.text.length,
      voiceId: request.voice_id,
      model: request.model || 'speech-01-turbo'
    });

    // 构建请求体 - 根据官方文档格式
    // 使用官方示例的标准格式
    const requestBody = {
      model: request.model || 'speech-2.5-hd-preview',  // 使用官方推荐的模型
      text: request.text,
      stream: false,  // 官方示例中的参数
      voice_setting: {
        voice_id: request.voice_id || 'Grinch',  // 官方示例中的默认语音
        speed: request.speed || 1,
        vol: 1,
        pitch: request.pitch || 0
      },
      audio_setting: {
        sample_rate: 32000,  // 官方示例中的参数
        bitrate: 128000,     // 官方示例中的参数
        format: request.format || 'mp3',
        channel: 1           // 官方示例中的参数
      }
    };

    // 使用官方文档的API端点格式
    const apiEndpoint = `/v1/t2a_v2?GroupId=${config.groupId}`;
    const apiUrl = `${config.baseUrl}${apiEndpoint}`;

    console.log('[Minimax TTS] 请求参数', {
      url: apiUrl,
      model: request.model,
      body: requestBody,
      headers: {
        'Authorization': `Bearer ${config.apiKey.substring(0, 10)}...`,
        'Content-Type': 'application/json'
      }
    });

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      console.log('[Minimax TTS] API响应接收', {
        hasAudioData: !!result.audio_data,
        responseKeys: Object.keys(result),
        fullResponse: result
      });

      // 检查API响应中的错误
      if (result.base_resp?.status_code !== 0) {
        throw this.handleError(result);
      }

      // 检查音频数据 - Minimax API返回的音频数据在data.audio字段
      const audioData = result.data?.audio;
      if (!audioData) {
        throw new Error("API响应中没有音频数据");
      }

      // 将十六进制音频数据转换为Buffer（Minimax返回的是hex格式，不是base64）
      const audioContent = Buffer.from(audioData, 'hex');

      console.log('[Minimax TTS] 音频数据处理完成', {
        audioDataLength: audioContent.length,
        modelUsed: request.model || 'speech-01-turbo'
      });

      return {
        audioContent,
        metadata: {
          duration: result.duration || 0,
          charactersUsed: request.text.length,
          modelUsed: request.model || 'speech-01-turbo',
          voiceId: request.voice_id || 'default',
          format: request.format || 'mp3'
        }
      };

    } catch (error) {
      console.error('[Minimax TTS] 生成失败:', error);
      throw error;
    }
  }

  /**
   * 上传音频文件用于语音克隆
   */
  async uploadAudioFile(audioBuffer: Buffer, filename: string): Promise<string> {
    const config = await this.getConfig();

    console.log('[Minimax TTS] 上传音频文件', {
      filename,
      fileSize: audioBuffer.length
    });

    try {
      const formData = new FormData();
      formData.append('purpose', 'voice_clone');
      formData.append('file', new Blob([new Uint8Array(audioBuffer)]), filename);

      const response = await fetch(`${config.baseUrl}/v1/files/upload?GroupId=${config.groupId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.base_resp?.status_code !== 0) {
        throw this.handleError(result);
      }

      const fileId = result.file?.file_id;
      if (!fileId) {
        throw new Error("文件上传失败：未返回文件ID");
      }

      console.log('[Minimax TTS] 文件上传成功', { fileId });
      return fileId;

    } catch (error) {
      console.error('[Minimax TTS] 文件上传失败:', error);
      throw error;
    }
  }

  /**
   * 克隆语音
   */
  async cloneVoice(fileId: string, voiceId: string, options?: {
    noiseReduction?: boolean;
    text?: string;
    accuracy?: number;
    needVolumeNormalization?: boolean;
  }): Promise<void> {
    const config = await this.getConfig();

    console.log('[Minimax TTS] 开始语音克隆', {
      fileId,
      voiceId,
      options
    });

    try {
      const response = await fetch(`${config.baseUrl}/v1/voice_clone?GroupId=${config.groupId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          file_id: fileId,
          voice_id: voiceId,
          noise_reduction: options?.noiseReduction || false,
          text: options?.text,
          accuracy: options?.accuracy || 0.7,
          need_volume_normalization: options?.needVolumeNormalization || false
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.base_resp?.status_code !== 0) {
        throw this.handleError(result);
      }

      console.log('[Minimax TTS] 语音克隆成功', { voiceId });

    } catch (error) {
      console.error('[Minimax TTS] 语音克隆失败:', error);
      throw error;
    }
  }

  /**
   * 获取语音列表
   */
  async getVoiceList(): Promise<MinimaxVoice[]> {
    console.log('[Minimax TTS] 获取语音列表...');

    try {
      const config = await this.getConfig();

      // 基于官方文档的正确API调用 - 尝试获取所有类型的语音
      console.log('[Minimax TTS] 使用官方文档的正确端点: /v1/get_voice');

      // 尝试不同的voice_type参数以获取更多语音
      const voiceTypes = [
        'voice_cloning',    // 克隆语音
        'preset',          // 预设语音
        'all',             // 所有语音
        'basic',           // 基础语音
        'premium'          // 高级语音
      ];

      let allVoices: MinimaxVoice[] = [];

      for (const voiceType of voiceTypes) {
        try {
          console.log(`[Minimax TTS] 尝试获取 ${voiceType} 类型的语音...`);

          const response = await fetch(`${config.baseUrl}/v1/get_voice`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${config.apiKey}`
            },
            body: JSON.stringify({
              voice_type: voiceType
            })
          });

          console.log(`[Minimax TTS] ${voiceType} 端点响应状态: ${response.status}`);

          if (response.ok) {
            const result = await response.json();
            console.log(`[Minimax TTS] ${voiceType} 端点响应数据:`, result);

            // 解析响应数据
            let voices: MinimaxVoice[] = [];

            if (result.base_resp?.status_code === 0) {
              // 处理新的响应格式
              if (result.system_voice && Array.isArray(result.system_voice)) {
                // 转换system_voice格式到我们的MinimaxVoice格式
                voices = result.system_voice.map((voice: any) => ({
                  voice_id: voice.voice_id,
                  name: voice.voice_name || voice.voice_id,
                  description: Array.isArray(voice.description)
                    ? voice.description.join(', ')
                    : voice.description || `${voice.voice_name || voice.voice_id}语音`,
                  gender: this.extractGenderFromName(voice.voice_name || voice.voice_id),
                  language: this.extractLanguageFromId(voice.voice_id),
                  style: this.extractStyleFromName(voice.voice_name || voice.voice_id),
                  created_at: voice.created_time,
                  voice_type: voiceType
                }));
                console.log(`[Minimax TTS] 从system_voice解析出 ${voices.length} 个语音`);
              }

              // 处理voice_cloning数组
              if (result.voice_cloning && Array.isArray(result.voice_cloning) && result.voice_cloning.length > 0) {
                const clonedVoices = result.voice_cloning.map((voice: any) => ({
                  voice_id: voice.voice_id,
                  name: voice.voice_name || voice.voice_id,
                  description: voice.description || `${voice.voice_name || voice.voice_id}克隆语音`,
                  gender: voice.gender || 'unknown',
                  language: voice.language || 'zh-CN',
                  style: 'cloned',
                  is_cloned: true,
                  created_at: voice.created_time,
                  voice_type: voiceType
                }));
                voices.push(...clonedVoices);
                console.log(`[Minimax TTS] 从voice_cloning解析出 ${clonedVoices.length} 个克隆语音`);
              }

              // 兼容旧格式
              if (voices.length === 0) {
                if (result.voices && Array.isArray(result.voices)) {
                  voices = result.voices;
                } else if (result.data && Array.isArray(result.data)) {
                  voices = result.data;
                } else if (result.voice_list && Array.isArray(result.voice_list)) {
                  voices = result.voice_list;
                }
              }
            }

            if (voices.length > 0) {
              console.log(`[Minimax TTS] ${voiceType} 成功获取 ${voices.length} 个语音角色`);

              // 添加类型标记并合并到总列表
              voices.forEach(voice => {
                voice.voice_type = voiceType;
              });
              allVoices.push(...voices);
            } else {
              console.log(`[Minimax TTS] ${voiceType} 端点未返回语音数据`);
            }
          } else {
            const errorText = await response.text();
            console.log(`[Minimax TTS] ${voiceType} 端点失败 ${response.status}: ${errorText}`);
          }
        } catch (error) {
          console.log(`[Minimax TTS] ${voiceType} 端点请求失败:`, error);
        }

        // 添加延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // 去重处理
      const uniqueVoices = allVoices.reduce((acc: MinimaxVoice[], voice) => {
        const existing = acc.find(v => v.voice_id === voice.voice_id);
        if (!existing) {
          acc.push(voice);
        }
        return acc;
      }, []);

      if (uniqueVoices.length > 0) {
        console.log(`[Minimax TTS] 总共获取 ${allVoices.length} 个语音，去重后 ${uniqueVoices.length} 个`);
        return uniqueVoices;
      } else {
        console.log(`[Minimax TTS] 所有官方端点都未返回语音数据`);
      }

      // 如果官方端点失败，尝试其他可能的端点作为后备
      console.log('[Minimax TTS] 官方端点失败，尝试后备端点...');
      const fallbackEndpoints = [
        `/v1/voices`,
        `/v1/voice`,
        `/v1/audio/voices`,
        `/audio/voices`
      ];

      for (const endpoint of fallbackEndpoints) {
        try {
          console.log(`[Minimax TTS] 尝试端点: ${endpoint}`);

          // 准备请求头
          const headers: Record<string, string> = {
            'Authorization': `Bearer ${config.apiKey}`,
            'Content-Type': 'application/json'
          };

          // 如果端点不包含GroupId参数，添加到headers中
          if (!endpoint.includes('GroupId')) {
            headers['GroupId'] = config.groupId;
          }

          const response = await fetch(`${config.baseUrl}${endpoint}`, {
            method: 'GET',
            headers
          });

          console.log(`[Minimax TTS] 端点 ${endpoint} 响应状态: ${response.status}`);
          console.log(`[Minimax TTS] 请求头:`, Object.keys(headers));

          if (!response.ok) {
            const errorText = await response.text();
            console.log(`[Minimax TTS] 端点 ${endpoint} 返回 ${response.status}: ${errorText.substring(0, 200)}...`);

            // 特殊处理一些常见错误
            if (response.status === 401) {
              console.log(`[Minimax TTS] 认证失败 - 检查API Key`);
            } else if (response.status === 403) {
              console.log(`[Minimax TTS] 权限不足 - 检查GroupID和账户权限`);
            } else if (response.status === 404) {
              console.log(`[Minimax TTS] 端点不存在 - 尝试下一个端点`);
            }
            continue;
          }

          const result = await response.json();
          console.log(`[Minimax TTS] 端点 ${endpoint} 响应数据:`, result);

          // 尝试多种响应格式
          let voices: MinimaxVoice[] = [];

          if (result.base_resp?.status_code === 0 && result.voices) {
            // Minimax标准响应格式
            voices = result.voices;
          } else if (Array.isArray(result)) {
            // 直接返回数组
            voices = result;
          } else if (result.data && Array.isArray(result.data)) {
            // 数据在data字段中
            voices = result.data;
          } else if (result.voices && Array.isArray(result.voices)) {
            // 语音在voices字段中
            voices = result.voices;
          }

          if (voices.length > 0) {
            console.log(`[Minimax TTS] 成功获取语音列表`, {
              endpoint,
              count: voices.length
            });
            return voices;
          } else {
            console.log(`[Minimax TTS] 端点 ${endpoint} 未返回有效的语音数据`);
          }

        } catch (error) {
          console.log(`[Minimax TTS] 端点 ${endpoint} 请求失败:`, error);
        }
      }

      // 如果所有端点都失败，返回默认语音列表
      console.log('[Minimax TTS] 所有端点都失败，返回默认语音列表');
      return this.getDefaultVoices();

    } catch (error) {
      console.error('[Minimax TTS] 获取语音列表失败:', error);
      // 返回默认语音列表作为后备
      return this.getDefaultVoices();
    }
  }

  /**
   * 获取默认语音列表（后备方案）- 基于真实的Minimax语音
   */
  private getDefaultVoices(): MinimaxVoice[] {
    return [
      // 英文语音 (高质量选择)
      {
        voice_id: 'English_expressive_narrator',
        name: 'Expressive Narrator',
        description: 'Expressive Narrator语音',
        gender: 'male',
        language: 'en-US',
        style: 'expressive'
      },
      {
        voice_id: 'English_radiant_girl',
        name: 'Radiant Girl',
        description: 'Radiant Girl语音',
        gender: 'female',
        language: 'en-US',
        style: 'radiant'
      },
      {
        voice_id: 'English_magnetic_voiced_man',
        name: 'Magnetic-voiced Male',
        description: 'Magnetic-voiced Male语音',
        gender: 'male',
        language: 'en-US',
        style: 'magnetic'
      },
      {
        voice_id: 'English_compelling_lady1',
        name: 'Compelling Lady',
        description: 'Compelling Lady语音',
        gender: 'female',
        language: 'en-US',
        style: 'compelling'
      },
      {
        voice_id: 'English_Trustworth_Man',
        name: 'Trustworthy Man',
        description: 'Trustworthy Man语音',
        gender: 'male',
        language: 'en-US',
        style: 'trustworthy'
      },
      {
        voice_id: 'English_CalmWoman',
        name: 'Calm Woman',
        description: 'Calm Woman语音',
        gender: 'female',
        language: 'en-US',
        style: 'calm'
      },

      // 中文语音 (普通话)
      {
        voice_id: 'Chinese (Mandarin)_Reliable_Executive',
        name: 'Reliable Executive',
        description: 'Reliable Executive语音',
        gender: 'male',
        language: 'zh-CN',
        style: 'professional'
      },
      {
        voice_id: 'Chinese (Mandarin)_News_Anchor',
        name: 'News Anchor',
        description: 'News Anchor语音',
        gender: 'female',
        language: 'zh-CN',
        style: 'professional'
      },
      {
        voice_id: 'Chinese (Mandarin)_Mature_Woman',
        name: 'Mature Woman',
        description: 'Mature Woman语音',
        gender: 'female',
        language: 'zh-CN',
        style: 'mature'
      },
      {
        voice_id: 'Chinese (Mandarin)_Gentleman',
        name: 'Gentleman',
        description: 'Gentleman语音',
        gender: 'male',
        language: 'zh-CN',
        style: 'gentle'
      },
      {
        voice_id: 'Chinese (Mandarin)_Warm_Girl',
        name: 'Warm Girl',
        description: 'Warm Girl语音',
        gender: 'female',
        language: 'zh-CN',
        style: 'warm'
      },
      {
        voice_id: 'Chinese (Mandarin)_Sweet_Lady',
        name: 'Sweet Lady',
        description: 'Sweet Lady语音',
        gender: 'female',
        language: 'zh-CN',
        style: 'sweet'
      },

      // 日文语音
      {
        voice_id: 'Japanese_KindLady',
        name: 'Kind Lady',
        description: 'Kind Lady语音',
        gender: 'female',
        language: 'ja-JP',
        style: 'kind'
      },
      {
        voice_id: 'Japanese_GentleButler',
        name: 'Gentle Butler',
        description: 'Gentle Butler语音',
        gender: 'male',
        language: 'ja-JP',
        style: 'gentle'
      },

      // 粤语语音
      {
        voice_id: 'Cantonese_GentleLady',
        name: 'Gentle Lady',
        description: 'Gentle Lady语音',
        gender: 'female',
        language: 'zh-HK',
        style: 'gentle'
      },
      {
        voice_id: 'Cantonese_ProfessionalHost（M)',
        name: 'Professional Male Host',
        description: 'Professional Male Host语音',
        gender: 'male',
        language: 'zh-HK',
        style: 'professional'
      }
    ];
  }

  /**
   * 从语音名称提取性别信息
   */
  private extractGenderFromName(name: string): string {
    const lowerName = name.toLowerCase();
    if (lowerName.includes('woman') || lowerName.includes('girl') || lowerName.includes('lady') ||
        lowerName.includes('female') || lowerName.includes('queen') || lowerName.includes('maiden') ||
        lowerName.includes('aunt') || lowerName.includes('miss') || lowerName.includes('princess')) {
      return 'female';
    }
    if (lowerName.includes('man') || lowerName.includes('boy') || lowerName.includes('male') ||
        lowerName.includes('gentleman') || lowerName.includes('knight') || lowerName.includes('commander') ||
        lowerName.includes('elder') || lowerName.includes('warrior') || lowerName.includes('scholar')) {
      return 'male';
    }
    return 'unknown';
  }

  /**
   * 从语音ID提取语言信息
   */
  private extractLanguageFromId(voiceId: string): string {
    if (voiceId.startsWith('English_')) {
      return 'en-US';
    }
    if (voiceId.startsWith('Chinese') || voiceId.includes('Mandarin')) {
      return 'zh-CN';
    }
    if (voiceId.startsWith('Japanese_')) {
      return 'ja-JP';
    }
    if (voiceId.startsWith('Cantonese_')) {
      return 'zh-HK';
    }
    return 'en-US'; // 默认英文
  }

  /**
   * 从语音名称提取风格信息
   */
  private extractStyleFromName(name: string): string {
    const lowerName = name.toLowerCase();
    if (lowerName.includes('calm') || lowerName.includes('gentle') || lowerName.includes('soft')) {
      return 'gentle';
    }
    if (lowerName.includes('confident') || lowerName.includes('assertive') || lowerName.includes('bossy')) {
      return 'confident';
    }
    if (lowerName.includes('playful') || lowerName.includes('jovial') || lowerName.includes('whimsical')) {
      return 'playful';
    }
    if (lowerName.includes('wise') || lowerName.includes('mature') || lowerName.includes('reliable')) {
      return 'mature';
    }
    if (lowerName.includes('deep') || lowerName.includes('magnetic')) {
      return 'deep';
    }
    return 'normal';
  }

  /**
   * 生成文本哈希（用于缓存）
   */
  static generateTextHash(text: string): string {
    // 简单的哈希函数，实际项目中可以使用更复杂的算法
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 生成音频文件名
   */
  static generateFileName(voiceId: string, textHash: string, format: string): string {
    return `minimax-tts-${voiceId}-${textHash}.${format.toLowerCase()}`;
  }

  /**
   * 获取内容类型
   */
  static getContentType(format: string): string {
    switch (format.toLowerCase()) {
      case "mp3":
        return "audio/mpeg";
      case "wav":
        return "audio/wav";
      default:
        return "audio/mpeg";
    }
  }
}

/**
 * 创建Minimax TTS客户端实例
 */
export function createMinimaxTTSClient(db: PrismaClient): MinimaxTTSClient {
  return new MinimaxTTSClient(db);
}
