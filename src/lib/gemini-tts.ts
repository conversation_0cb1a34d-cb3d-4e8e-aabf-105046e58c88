import { GoogleGenAI } from "@google/genai";
import { type PrismaClient } from "@prisma/client";

// 按照最新Gemini TTS API规范定义的接口类型
export interface VoiceConfig {
  voiceName: string; // 预设语音名称，如 "Kore", "Puck" 等
}

export interface SpeakerVoiceConfig {
  speaker: string;
  voiceConfig: {
    prebuiltVoiceConfig: {
      voiceName: string;
    };
  };
}

export interface TTSRequest {
  text: string;
  voiceName?: string; // 单说话者模式
  speakers?: SpeakerVoiceConfig[]; // 多说话者模式
  model?: "gemini-2.5-flash-preview-tts" | "gemini-2.5-pro-preview-tts";
  style?: string; // 自然语言风格控制
}

export interface TTSResponse {
  audioContent: string; // Base64 encoded audio
  metadata: {
    textLength: number;
    generatedAt: string;
    modelUsed: string;
    duration?: number;
  };
}

/**
 * Gemini TTS 客户端类
 * 按照最新官方API规范实现
 */
export class GeminiTTSClient {
  private genAI: GoogleGenAI | null = null;
  private defaultModel: "gemini-2.5-flash-preview-tts" | "gemini-2.5-pro-preview-tts" = "gemini-2.5-flash-preview-tts";
  private db: PrismaClient;

  constructor(db: PrismaClient) {
    this.db = db;
  }

  /**
   * 从数据库获取 Gemini API Key
   */
  private async getApiKey(): Promise<string> {
    const provider = await this.db.modelProvider.findFirst({
      where: {
        slug: "gemini",
        isActive: true,
      },
    });

    if (!provider) {
      throw new Error("Gemini provider not found");
    }

    // 优先使用数据库中的API密钥
    if (provider.apiKey) {
      return provider.apiKey;
    }

    // 回退到环境变量
    const envApiKey = process.env.GEMINI_API_KEY;
    if (envApiKey) {
      return envApiKey;
    }

    throw new Error("Gemini API key not configured in database or environment variables");
  }

  /**
   * 初始化 GoogleGenAI 客户端
   */
  private async initializeClient(): Promise<void> {
    if (!this.genAI) {
      const apiKey = await this.getApiKey();
      this.genAI = new GoogleGenAI({ apiKey });
    }
  }

  /**
   * 单说话者语音合成
   * @param request TTS请求参数
   * @returns 合成的音频数据
   */
  async synthesizeSpeech(request: TTSRequest): Promise<TTSResponse> {
    await this.initializeClient();
    
    if (!this.genAI) {
      throw new Error("Failed to initialize Gemini client");
    }

    const startTime = Date.now();
    const model = request.model || this.defaultModel;
    
    try {
      console.log("[Gemini TTS] 开始生成语音", {
        text: request.text.substring(0, 100) + (request.text.length > 100 ? "..." : ""),
        textLength: request.text.length,
        voiceName: request.voiceName,
        model: model
      });
      
      // 使用自然语言提示来控制音频质量和风格
      let enhancedText = request.text;
      
      if (request.style && request.style.trim()) {
        enhancedText = `${request.style}: ${request.text}`;
      }
      // 默认不添加任何风格提示，保持原始文本用于生成试听音频
      
      const result = await this.genAI.models.generateContent({
        model: model,
        contents: [{ parts: [{ text: enhancedText }] }],
        config: {
          responseModalities: ['AUDIO'],
          speechConfig: {
            voiceConfig: {
              prebuiltVoiceConfig: { 
                voiceName: request.voiceName || 'Kore'
              },
            },
          },
        },
      });

      console.log("[Gemini TTS] API响应接收", {
        hasCandidates: !!(result.candidates && result.candidates.length > 0),
        candidatesCount: result.candidates?.length || 0,
        responseStructure: JSON.stringify(result, null, 2).substring(0, 500)
      });
      
      // 检查响应结构
      if (!result.candidates || result.candidates.length === 0) {
        console.error("[Gemini TTS] 响应结构错误 - 无候选项:", result);
        throw new Error("Failed to generate speech: No candidates in response");
      }

      const candidate = result.candidates[0];
      console.log("[Gemini TTS] 候选项结构:", {
        hasContent: !!candidate?.content,
        hasParts: !!(candidate?.content?.parts),
        partsLength: candidate?.content?.parts?.length || 0,
        candidateStructure: JSON.stringify(candidate, null, 2).substring(0, 300)
      });

      if (!candidate?.content?.parts || candidate.content.parts.length === 0) {
        console.error("[Gemini TTS] 响应结构错误 - 无内容部分:", candidate);
        throw new Error("Failed to generate speech: No content parts in response");
      }

      // 查找音频数据
      let audioData: string | undefined;
      for (const part of candidate.content.parts) {
        if (part.inlineData?.data) {
          audioData = part.inlineData.data;
          break;
        }
        // 检查其他可能的音频数据位置
        if ((part as any).audio?.data) {
          audioData = (part as any).audio.data;
          break;
        }
      }

      if (!audioData || typeof audioData !== "string") {
        console.error("[Gemini TTS] 响应结构错误 - 无音频数据:", candidate.content.parts);
        throw new Error("Failed to generate speech: No audio data in response");
      }
      
      console.log("[Gemini TTS] 解析响应成功", {
        hasAudioData: !!audioData,
        audioDataLength: audioData?.length || 0
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      return {
        audioContent: audioData,
        metadata: {
          textLength: request.text.length,
          generatedAt: new Date().toISOString(),
          modelUsed: model,
          duration,
        },
      };
    } catch (error) {
      console.error("[Gemini TTS] 生成语音失败:", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        request: {
          text: request.text.substring(0, 100),
          voiceName: request.voiceName,
          model: model
        }
      });
      throw new Error(
        `TTS generation failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * 多说话者语音合成（实验性功能）
   * @param requests 多个TTS请求
   * @returns 合成的音频数据数组
   */
  async synthesizeMultipleSpeakers(requests: TTSRequest[]): Promise<TTSResponse[]> {
    await this.initializeClient();
    
    const results: TTSResponse[] = [];
    
    for (const request of requests) {
      const result = await this.synthesizeSpeech(request);
      results.push(result);
    }
    
    return results;
  }



  /**
   * 获取支持的语音列表
   * @returns 支持的语音配置列表
   */
  getSupportedVoices(): VoiceConfig[] {
    return [
      { voiceName: "Kore" },
      { voiceName: "Puck" },
      { voiceName: "Charon" },
      { voiceName: "Fenrir" },
    ];
  }
}

// 创建客户端实例的工厂函数
export function createGeminiTTSClient(db: PrismaClient): GeminiTTSClient {
  return new GeminiTTSClient(db);
}