import { BaseProviderAdapter, type ProviderConfig } from './BaseProviderAdapter';
import { GeminiAdapter } from './GeminiAdapter';
import { ModernGeminiAdapter } from './ModernGeminiAdapter';
import type { PrismaClient } from '@prisma/client';

/**
 * 适配器工厂类
 * 根据提供商配置动态创建适配器实例
 */
export class AdapterFactory {
  private static adapters = new Map<string, new (provider: any, db: any) => BaseProviderAdapter>();
  private static instances = new Map<string, BaseProviderAdapter>();

  static {
    // 注册内置适配器
    this.registerAdapter('gemini', ModernGeminiAdapter);
    this.registerAdapter('google', ModernGeminiAdapter);
  }

  /**
   * 注册新的适配器
   */
  static registerAdapter(providerSlug: string, adapterClass: new (provider: any, db: any) => BaseProviderAdapter) {
    this.adapters.set(providerSlug.toLowerCase(), adapterClass);
  }

  /**
   * 创建适配器实例
   */
  static async createAdapter(
    db: PrismaClient,
    providerSlug: string,
    forceNew = false
  ): Promise<BaseProviderAdapter> {
    const cacheKey = providerSlug.toLowerCase();
    
    // 如果不强制创建新实例且缓存中存在，直接返回
    if (!forceNew && this.instances.has(cacheKey)) {
      return this.instances.get(cacheKey)!;
    }

    // 从数据库获取提供商配置
    const provider = await db.modelProvider.findFirst({
      where: {
        slug: providerSlug,
        isActive: true
      }
    });

    if (!provider) {
      throw new Error(`Provider '${providerSlug}' not found or inactive`);
    }

    // 获取适配器类
    const AdapterClass = this.adapters.get(cacheKey);
    if (!AdapterClass) {
      throw new Error(`No adapter registered for provider '${providerSlug}'`);
    }

    // 创建适配器实例
    const adapter = new AdapterClass(provider, db);
    
    // 缓存实例
    this.instances.set(cacheKey, adapter);
    
    return adapter;
  }

  /**
   * 获取 TTS 适配器
   */
  static async createTTSAdapter(db: PrismaClient): Promise<BaseProviderAdapter> {
    // 查找支持 TTS 的提供商
    const provider = await db.modelProvider.findFirst({
      where: {
        isActive: true
      }
    });

    if (!provider) {
      // 如果没有明确标记支持 TTS 的提供商，尝试使用 Gemini
      return this.createAdapter(db, 'gemini');
    }

    return this.createAdapter(db, provider.slug);
  }

  /**
   * 获取文本生成适配器
   */
  static async createTextAdapter(db: PrismaClient, modelName?: string): Promise<BaseProviderAdapter> {
    let provider;

    if (modelName) {
      // 根据模型名称查找提供商
      provider = await db.modelProvider.findFirst({
        where: {
          isActive: true,
          models: {
            some: {
              name: modelName,
              isActive: true
            }
          }
        }
      });
    }

    if (!provider) {
      // 查找支持文本生成的提供商
      provider = await db.modelProvider.findFirst({
        where: {
          isActive: true
        }
      });
    }

    if (!provider) {
      throw new Error('No text generation provider available');
    }

    return this.createAdapter(db, provider.slug);
  }

  /**
   * 清除缓存
   */
  static clearCache(providerSlug?: string) {
    if (providerSlug) {
      this.instances.delete(providerSlug.toLowerCase());
    } else {
      this.instances.clear();
    }
  }

  /**
   * 获取所有已注册的适配器
   */
  static getRegisteredAdapters(): string[] {
    return Array.from(this.adapters.keys());
  }

  /**
   * 健康检查所有活跃的提供商
   */
  static async healthCheckAll(db: PrismaClient): Promise<Record<string, boolean>> {
    const providers = await db.modelProvider.findMany({
      where: {
        isActive: true
      },
      select: { slug: true }
    });

    const results: Record<string, boolean> = {};

    await Promise.all(
      providers.map(async (provider) => {
        try {
          const adapter = await this.createAdapter(db, provider.slug);
          results[provider.slug] = await adapter.healthCheck();
        } catch (error) {
          console.error(`Health check failed for ${provider.slug}:`, error);
          results[provider.slug] = false;
        }
      })
    );

    return results;
  }
}

/**
 * 便捷函数：创建 TTS 客户端
 */
export async function createTTSClient(db: PrismaClient): Promise<BaseProviderAdapter> {
  return AdapterFactory.createTTSAdapter(db);
}

/**
 * 便捷函数：创建文本生成客户端
 */
export async function createTextClient(db: PrismaClient, modelName?: string): Promise<BaseProviderAdapter> {
  return AdapterFactory.createTextAdapter(db, modelName);
}