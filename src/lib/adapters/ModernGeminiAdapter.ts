import { GoogleGenAI } from "@google/genai";
import { BaseProviderAdapter, type ProviderConfig, type ModelCapabilities, type APIResponse } from './BaseProviderAdapter';

/**
 * 现代化的 Gemini 适配器，使用最新的 @google/genai 库
 */
export class ModernGeminiAdapter extends BaseProviderAdapter {
  private genAI: GoogleGenAI;

  constructor(provider: any, db: any) {
    super(provider, db);
    
    if (!provider.configuration?.apiKey) {
      throw new Error('Gemini API key is required');
    }
    
    this.genAI = new GoogleGenAI(provider.configuration.apiKey);
  }

  async getModelCapabilities(modelName: string): Promise<ModelCapabilities> {
    if (modelName.includes('tts') || modelName.includes('flash-preview-tts')) {
      return {
        textToSpeech: true,
        streaming: false,
        multimodal: false,
      };
    }
    
    if (modelName.includes('flash') || modelName.includes('pro')) {
      return {
        textGeneration: true,
        multimodal: true,
        functionCalling: true,
        streaming: true,
      };
    }

    return {
      textGeneration: true,
      streaming: true,
    };
  }

  async healthCheck(): Promise<boolean> {
    try {
      // 尝试生成简单内容来检查 API 连接
      await this.genAI.models.generateContent({
        model: "gemini-1.5-flash",
        contents: [{ parts: [{ text: "Hello" }] }]
      });
      return true;
    } catch (error) {
      console.error('Gemini health check failed:', error);
      return false;
    }
  }

  async generateText(params: {
    model: string;
    prompt: string;
    maxTokens?: number;
    temperature?: number;
    stream?: boolean;
  }): Promise<APIResponse> {
    try {
      const result = await this.genAI.models.generateContent({
        model: params.model,
        contents: [{ parts: [{ text: params.prompt }] }]
      });
      
      return {
        success: true,
        data: {
          text: result.candidates?.[0]?.content?.parts?.[0]?.text || '',
          finishReason: result.candidates?.[0]?.finishReason,
        },
        usage: {
          inputTokens: result.usageMetadata?.promptTokenCount,
          outputTokens: result.usageMetadata?.candidatesTokenCount,
          totalTokens: result.usageMetadata?.totalTokenCount,
        },
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async textToSpeech(params: {
    model: string;
    text: string;
    voice?: string;
    format?: string;
  }): Promise<APIResponse<ArrayBuffer>> {
    try {
      // 注意：当前 @google/genai 包可能不支持 TTS
      // 这里提供一个基础实现，可能需要使用其他 API
      const result = await this.genAI.models.generateContent({
        model: params.model,
        contents: [{ parts: [{ text: params.text }] }]
      });
      
      // 获取音频数据（如果支持的话）
      const audioData = result.candidates?.[0]?.content?.parts?.[0]?.inlineData;
      
      if (!audioData?.data) {
        throw new Error('TTS not supported or no audio data in response');
      }

      // 解码 base64 音频数据
      const audioBuffer = Buffer.from(audioData.data, 'base64');

      return {
        success: true,
        data: audioBuffer.buffer,
        usage: {
          characters: params.text.length,
        },
        metadata: {
          format: audioData.mimeType || 'audio/wav',
          voice: params.voice || "Aoede",
          model: params.model,
        },
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async speechToText(params: {
    model: string;
    audio: ArrayBuffer;
    format?: string;
    language?: string;
  }): Promise<APIResponse> {
    return {
      success: false,
      error: 'Speech-to-text not supported by Gemini adapter',
    };
  }

  async generateImage(params: {
    model: string;
    prompt: string;
    size?: string;
    quality?: string;
    n?: number;
  }): Promise<APIResponse> {
    return {
      success: false,
      error: 'Image generation not supported by Gemini adapter',
    };
  }

  async getEmbedding(params: {
    model: string;
    input: string | string[];
  }): Promise<APIResponse> {
    try {
      // 暂时返回错误，因为 @google/genai 包可能不支持嵌入
      throw new Error('Embedding not supported in current @google/genai version');
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 获取可用的语音列表
   */
  async getAvailableVoices(): Promise<string[]> {
    // Gemini TTS 支持的预设语音
    return [
      "Aoede",    // 女性，英语
      "Kore",     // 女性，英语  
      "Puck",     // 男性，英语
      "Charon",   // 男性，英语
    ];
  }

  /**
   * 验证语音名称是否有效
   */
  async isValidVoice(voiceName: string): Promise<boolean> {
    const availableVoices = await this.getAvailableVoices();
    return availableVoices.includes(voiceName);
  }
}