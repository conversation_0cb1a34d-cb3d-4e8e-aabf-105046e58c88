/**
 * 提供商适配器基类
 * 用于统一不同AI提供商的API调用接口
 */

export interface ProviderConfig {
  apiKey?: string;
  baseUrl?: string;
  timeout?: number;
  retryCount?: number;
  headers?: Record<string, string>;
  [key: string]: any;
}

export interface ModelCapabilities {
  textGeneration?: boolean;
  imageGeneration?: boolean;
  textToSpeech?: boolean;
  speechToText?: boolean;
  embedding?: boolean;
  functionCalling?: boolean;
  streaming?: boolean;
  multimodal?: boolean;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  usage?: {
    inputTokens?: number;
    outputTokens?: number;
    totalTokens?: number;
    characters?: number;
    images?: number;
  };
  metadata?: Record<string, any>;
}

export abstract class BaseProviderAdapter {
  protected config: ProviderConfig;
  protected providerName: string;
  protected provider: any;
  protected db: any;

  constructor(provider: any, db: any) {
    this.provider = provider;
    this.db = db;
    this.providerName = provider.name;
    this.config = {
      timeout: 30000,
      retryCount: 3,
      apiKey: provider.configuration?.apiKey,
      baseUrl: provider.baseUrl,
      headers: provider.defaultHeaders || {},
      ...provider.configuration as Record<string, any>,
    };
  }

  /**
   * 获取提供商名称
   */
  getProviderName(): string {
    return this.providerName;
  }

  /**
   * 获取模型能力
   */
  abstract getModelCapabilities(modelName: string): Promise<ModelCapabilities>;

  /**
   * 健康检查
   */
  abstract healthCheck(): Promise<boolean>;

  /**
   * 文本生成
   */
  abstract generateText(params: {
    model: string;
    prompt: string;
    maxTokens?: number;
    temperature?: number;
    stream?: boolean;
  }): Promise<APIResponse>;

  /**
   * 语音合成
   */
  abstract textToSpeech(params: {
    model: string;
    text: string;
    voice?: string;
    format?: string;
  }): Promise<APIResponse<ArrayBuffer>>;

  /**
   * 语音识别
   */
  abstract speechToText(params: {
    model: string;
    audio: ArrayBuffer;
    format?: string;
    language?: string;
  }): Promise<APIResponse>;

  /**
   * 图像生成
   */
  abstract generateImage(params: {
    model: string;
    prompt: string;
    size?: string;
    quality?: string;
    n?: number;
  }): Promise<APIResponse>;

  /**
   * 获取嵌入向量
   */
  abstract getEmbedding(params: {
    model: string;
    input: string | string[];
  }): Promise<APIResponse>;

  /**
   * 通用 HTTP 请求方法
   */
  protected async makeRequest(
    endpoint: string,
    options: {
      method?: string;
      headers?: Record<string, string>;
      body?: any;
      timeout?: number;
    } = {}
  ): Promise<Response> {
    const {
      method = 'POST',
      headers = {},
      body,
      timeout = this.config.timeout,
    } = options;

    const url = this.config.baseUrl 
      ? `${this.config.baseUrl.replace(/\/$/, '')}/${endpoint.replace(/^\//, '')}`
      : endpoint;

    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...this.config.headers,
      ...headers,
    };

    // 添加认证头
    if (this.config.apiKey) {
      requestHeaders['Authorization'] = `Bearer ${this.config.apiKey}`;
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        method,
        headers: requestHeaders,
        body: body ? JSON.stringify(body) : undefined,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * 重试机制
   */
  protected async withRetry<T>(
    operation: () => Promise<T>,
    retryCount: number = this.config.retryCount || 3
  ): Promise<T> {
    let lastError: Error;

    for (let i = 0; i <= retryCount; i++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (i === retryCount) {
          throw lastError;
        }

        // 指数退避
        const delay = Math.min(1000 * Math.pow(2, i), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * 错误处理
   */
  protected handleError(error: any): APIResponse {
    console.error(`[${this.providerName}] API Error:`, error);
    
    return {
      success: false,
      error: error.message || 'Unknown error occurred',
      metadata: {
        provider: this.providerName,
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * 使用量计算
   */
  protected calculateUsage(response: any, type: 'text' | 'audio' | 'image'): APIResponse['usage'] {
    // 子类可以重写此方法来计算具体的使用量
    return {};
  }
}