import { BaseProviderAdapter, type ProviderConfig, type ModelCapabilities, type APIResponse } from './BaseProviderAdapter';

/**
 * Google Gemini 提供商适配器
 */
export class GeminiAdapter extends BaseProviderAdapter {
  constructor(provider: any, db: any) {
    super(provider, db);
  }

  async getModelCapabilities(modelName: string): Promise<ModelCapabilities> {
    // 根据模型名称返回能力
    if (modelName.includes('tts')) {
      return {
        textToSpeech: true,
        streaming: false,
      };
    }
    
    if (modelName.includes('flash') || modelName.includes('pro')) {
      return {
        textGeneration: true,
        multimodal: true,
        functionCalling: true,
        streaming: true,
      };
    }

    return {
      textGeneration: true,
      streaming: true,
    };
  }

  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.makeRequest('models', {
        method: 'GET',
        timeout: 5000,
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  async generateText(params: {
    model: string;
    prompt: string;
    maxTokens?: number;
    temperature?: number;
    stream?: boolean;
  }): Promise<APIResponse> {
    try {
      const response = await this.withRetry(async () => {
        return await this.makeRequest(`models/${params.model}:generateContent`, {
          body: {
            contents: [{
              parts: [{ text: params.prompt }]
            }],
            generationConfig: {
              maxOutputTokens: params.maxTokens,
              temperature: params.temperature,
            },
          },
        });
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: true,
        data: {
          text: data.candidates?.[0]?.content?.parts?.[0]?.text || '',
          finishReason: data.candidates?.[0]?.finishReason,
        },
        usage: {
          inputTokens: data.usageMetadata?.promptTokenCount,
          outputTokens: data.usageMetadata?.candidatesTokenCount,
          totalTokens: data.usageMetadata?.totalTokenCount,
        },
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async textToSpeech(params: {
    model: string;
    text: string;
    voice?: string;
    format?: string;
  }): Promise<APIResponse<ArrayBuffer>> {
    try {
      const response = await this.withRetry(async () => {
        return await this.makeRequest(`models/${params.model}:generateContent`, {
          body: {
            contents: [{
              parts: [{ text: params.text }]
            }],
            generationConfig: {
              responseModalities: ["AUDIO"],
              speechConfig: {
                voiceConfig: {
                  prebuiltVoiceConfig: {
                    voiceName: params.voice || "Aoede"
                  }
                }
              }
            },
          },
        });
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.candidates?.[0]?.content?.parts?.[0]?.inlineData?.data) {
        throw new Error('No audio data in response');
      }

      // 解码 base64 音频数据
      const base64Audio = data.candidates[0].content.parts[0].inlineData.data;
      const audioBuffer = Buffer.from(base64Audio, 'base64');

      return {
        success: true,
        data: audioBuffer.buffer,
        usage: {
          characters: params.text.length,
        },
        metadata: {
          format: data.candidates[0].content.parts[0].inlineData.mimeType,
          voice: params.voice,
        },
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async speechToText(params: {
    model: string;
    audio: ArrayBuffer;
    format?: string;
    language?: string;
  }): Promise<APIResponse> {
    // Gemini 目前不直接支持 STT，可以返回不支持的错误
    return {
      success: false,
      error: 'Speech-to-text not supported by Gemini adapter',
    };
  }

  async generateImage(params: {
    model: string;
    prompt: string;
    size?: string;
    quality?: string;
    n?: number;
  }): Promise<APIResponse> {
    // Gemini 目前不直接支持图像生成，可以返回不支持的错误
    return {
      success: false,
      error: 'Image generation not supported by Gemini adapter',
    };
  }

  async getEmbedding(params: {
    model: string;
    input: string | string[];
  }): Promise<APIResponse> {
    try {
      const inputs = Array.isArray(params.input) ? params.input : [params.input];
      
      const response = await this.withRetry(async () => {
        return await this.makeRequest(`models/${params.model}:embedContent`, {
          body: {
            content: {
              parts: inputs.map(text => ({ text }))
            },
          },
        });
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        success: true,
        data: {
          embeddings: data.embedding?.values || [],
        },
        usage: {
          totalTokens: inputs.join(' ').length, // 简单估算
        },
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 重写认证头设置，Gemini 使用 API Key 参数而不是 Bearer token
   */
  protected async makeRequest(
    endpoint: string,
    options: {
      method?: string;
      headers?: Record<string, string>;
      body?: any;
      timeout?: number;
    } = {}
  ): Promise<Response> {
    // 为 Gemini API 添加 key 参数
    const url = this.config.baseUrl 
      ? `${this.config.baseUrl.replace(/\/$/, '')}/${endpoint.replace(/^\//, '')}?key=${this.config.apiKey}`
      : `${endpoint}?key=${this.config.apiKey}`;

    const {
      method = 'POST',
      headers = {},
      body,
      timeout = this.config.timeout,
    } = options;

    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...this.config.headers,
      ...headers,
    };

    // Gemini 不需要 Authorization header，使用 URL 参数
    delete requestHeaders['Authorization'];

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        method,
        headers: requestHeaders,
        body: body ? JSON.stringify(body) : undefined,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }
}