import { S3Client, PutObjectCommand, GetObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { env } from "~/env";

/**
 * R2音频存储客户端
 * 用于上传和管理TTS生成的音频文件
 */
export class R2AudioClient {
  private s3Client: S3Client;
  private bucketName: string;

  constructor() {
    this.s3Client = new S3Client({
      region: "auto",
      endpoint: env.R2_ENDPOINT,
      credentials: {
        accessKeyId: env.R2_ACCESS_KEY_ID,
        secretAccessKey: env.R2_SECRET_ACCESS_KEY,
      },
    });
    this.bucketName = env.R2_BUCKET_NAME;
  }

  /**
   * 将LINEAR16 PCM数据转换为WAV格式
   * @param pcmData PCM音频数据Buffer
   * @param sampleRate 采样率，默认16000
   * @param channels 声道数，默认1
   * @returns WAV格式的Buffer
   */
  private createWavBuffer(pcmData: any, sampleRate: number = 24000, channels: number = 1): Buffer {
    const bitsPerSample = 16;
    const byteRate = sampleRate * channels * bitsPerSample / 8;
    const blockAlign = channels * bitsPerSample / 8;
    const dataSize = pcmData.length;
    const fileSize = 36 + dataSize;

    const wavBuffer = Buffer.alloc(44 + dataSize);
    let offset = 0;

    // RIFF header
    wavBuffer.write('RIFF', offset); offset += 4;
    wavBuffer.writeUInt32LE(fileSize, offset); offset += 4;
    wavBuffer.write('WAVE', offset); offset += 4;

    // fmt chunk
    wavBuffer.write('fmt ', offset); offset += 4;
    wavBuffer.writeUInt32LE(16, offset); offset += 4; // chunk size
    wavBuffer.writeUInt16LE(1, offset); offset += 2; // audio format (PCM)
    wavBuffer.writeUInt16LE(channels, offset); offset += 2;
    wavBuffer.writeUInt32LE(sampleRate, offset); offset += 4;
    wavBuffer.writeUInt32LE(byteRate, offset); offset += 4;
    wavBuffer.writeUInt16LE(blockAlign, offset); offset += 2;
    wavBuffer.writeUInt16LE(bitsPerSample, offset); offset += 2;

    // data chunk
    wavBuffer.write('data', offset); offset += 4;
    wavBuffer.writeUInt32LE(dataSize, offset); offset += 4;
    pcmData.copy(wavBuffer, offset);

    console.log('[R2Audio] WAV文件创建完成', {
      originalSize: pcmData.length,
      wavSize: wavBuffer.length,
      sampleRate,
      channels,
      bitsPerSample
    });

    return wavBuffer;
  }

  /**
   * 上传音频文件到R2存储
   * @param audioData Base64编码的音频数据
   * @param fileName 文件名
   * @param contentType 音频MIME类型
   * @param audioFormat 音频格式，用于判断是否需要转换
   * @returns 上传结果包含文件URL
   */
  async uploadAudio(
    audioData: string | Buffer,
    fileName: string,
    contentType: string,
    audioFormat?: string
  ): Promise<{
    url: string;
    key: string;
    bucket: string;
    size: number;
  }> {
    try {
      console.log('[R2Audio] 准备上传音频文件', {
        fileName,
        contentType,
        audioEncoding: audioFormat
      });

      console.log('[R2Audio] 开始上传音频', {
        fileName,
        contentType,
        audioFormat,
        dataLength: audioData.length
      });

      // 处理音频数据：支持Base64字符串或Buffer
      let buffer: Buffer;
      if (Buffer.isBuffer(audioData)) {
        console.log('[R2Audio] 输入数据是Buffer，直接使用');
        buffer = audioData;
      } else {
        console.log('[R2Audio] 输入数据是Base64字符串，转换为Buffer');
        buffer = Buffer.from(audioData, "base64");
      }
      
      // 如果是LINEAR16格式，需要转换为WAV
      if (audioFormat === 'LINEAR16') {
        console.log('[R2Audio] 检测到LINEAR16格式，转换为WAV');
        
        // Gemini TTS官方文档显示默认采样率为24kHz
        // 强制使用24kHz以确保正确的播放速度
        const sampleRate = 24000;
        
        console.log('[R2Audio] 使用标准采样率', {
          audioDataSize: buffer.length,
          sampleRate: sampleRate
        });
        
        buffer = this.createWavBuffer(buffer as Buffer, sampleRate);
      }
      
      // 生成唯一的文件键
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const key = `audio/${timestamp}-${randomId}/${fileName}`;

      // 上传到R2
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: buffer,
        ContentType: contentType,
        Metadata: {
          uploadedAt: new Date().toISOString(),
          originalFileName: fileName,
          fileSize: buffer.length.toString(),
        },
      });

      await this.s3Client.send(command);

      // 构建公共URL
      const url = `${env.R2_PUBLIC_URL}/${key}`;

      return {
        url,
        key,
        bucket: this.bucketName,
        size: buffer.length,
      };
    } catch (error) {
      console.error("R2 Audio Upload Error:", error);
      throw new Error(
        `Failed to upload audio to R2: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * 获取音频文件的预签名URL
   * @param key 文件键
   * @param expiresIn 过期时间（秒），默认1小时
   * @returns 预签名URL
   */
  async getSignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      const signedUrl = await getSignedUrl(this.s3Client, command, {
        expiresIn,
      });

      return signedUrl;
    } catch (error) {
      console.error("R2 Signed URL Error:", error);
      throw new Error(
        `Failed to generate signed URL: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * 删除音频文件
   * @param key 文件键
   */
  async deleteAudio(key: string): Promise<void> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
    } catch (error) {
      console.error("R2 Audio Delete Error:", error);
      throw new Error(
        `Failed to delete audio from R2: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * 生成音频文件名
   * @param roleId 角色ID
   * @param textHash 文本哈希
   * @param audioFormat 音频格式
   * @returns 文件名
   */
  static generateFileName(
    roleId: string,
    textHash: string,
    audioFormat: string
  ): string {
    const extension = audioFormat.toLowerCase() === "mp3" ? "mp3" : 
                     audioFormat.toLowerCase() === "ogg_opus" ? "ogg" : "wav";
    return `tts-${roleId}-${textHash}.${extension}`;
  }

  /**
   * 生成文本哈希
   * @param text 文本内容
   * @returns 哈希值
   */
  static generateTextHash(text: string): string {
    // 简单的哈希函数，实际项目中可以使用更强的哈希算法
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 获取音频MIME类型
   * @param audioFormat 音频格式
   * @returns MIME类型
   */
  static getContentType(audioFormat: string): string {
    switch (audioFormat.toUpperCase()) {
      case "MP3":
        return "audio/mpeg";
      case "OGG_OPUS":
        return "audio/ogg";
      case "LINEAR16":
        return "audio/wav";
      default:
        return "audio/mpeg";
    }
  }
}

// 默认导出单例实例
export const r2Audio = new R2AudioClient();