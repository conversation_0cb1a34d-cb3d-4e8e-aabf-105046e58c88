import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { env } from "~/env";

export const r2 = new S3Client({
  region: "auto",
  endpoint: `https://${env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: env.R2_ACCESS_KEY_ID,
    secretAccessKey: env.R2_SECRET_ACCESS_KEY,
  },
});

export async function uploadAudioToR2(
  audioData: string,
  fileName: string
): Promise<string> {
  try {
    console.log('[R2 Upload] 开始上传音频文件', {
      fileName,
      audioDataLength: audioData.length,
      audioDataPreview: audioData.substring(0, 50) + '...',
      bucket: env.R2_BUCKET_NAME,
      publicUrl: env.R2_PUBLIC_URL
    });
    
    // 解码base64音频数据
    const audioBuffer = Buffer.from(audioData, 'base64');
    
    console.log('[R2 Upload] 音频数据解码完成', {
      bufferSize: audioBuffer.length,
      bufferType: typeof audioBuffer
    });
    
    // 生成唯一的文件名
    const timestamp = Date.now();
    const uniqueFileName = `${timestamp}-${fileName}`;
    
    console.log('[R2 Upload] 生成唯一文件名', {
      originalFileName: fileName,
      uniqueFileName,
      timestamp
    });
    
    // 上传到R2
    const uploadResult = await r2.send(
      new PutObjectCommand({
        Bucket: env.R2_BUCKET_NAME,
        Key: uniqueFileName,
        Body: audioBuffer,
        ContentType: 'audio/wav',
        ContentLength: audioBuffer.length,
      })
    );
    
    console.log('[R2 Upload] 上传完成', {
      uploadResult: {
        ETag: uploadResult.ETag,
        VersionId: uploadResult.VersionId,
        ServerSideEncryption: uploadResult.ServerSideEncryption
      },
      uniqueFileName
    });
    
    // 构建公共访问URL
    const publicUrl = `${env.R2_PUBLIC_URL}/${uniqueFileName}`;
    
    console.log('[R2 Upload] 生成公共访问URL', {
      publicUrl,
      fileName: uniqueFileName
    });
    
    return publicUrl;
  } catch (error) {
    console.error('[R2 Upload] 上传失败:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      fileName,
      audioDataLength: audioData.length,
      bucket: env.R2_BUCKET_NAME
    });
    throw new Error(`Failed to upload audio to R2: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}