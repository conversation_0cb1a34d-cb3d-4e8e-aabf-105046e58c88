# Voctana - AI语音合成平台

Voctana 是一个基于 T3 Stack 构建的现代化 AI 语音合成平台，提供多语言、多角色的高质量文本转语音服务。

## 🚀 核心功能

### 🎵 语音合成 (TTS)
- **多模型支持**: 集成 Google Gemini TTS API
- **30+ 语音角色**: 支持不同风格和特色的语音
- **28种语言**: 覆盖全球主要语言和地区
- **质量分级**: 标准版和高级版语音质量选择
- **实时生成**: 快速响应的语音合成服务

### 👥 用户管理
- **安全认证**: NextAuth.js 提供的完整认证系统
- **积分系统**: 灵活的积分购买和消费机制
- **使用统计**: 详细的API调用和成本统计
- **订单管理**: 完整的支付和订单处理流程

### 🛠️ 管理后台
- **模型配置**: 动态模型和接口管理
- **定价管理**: 灵活的模型定价策略设置
- **用户管理**: 用户信息和权限管理
- **数据统计**: 实时的使用情况和成本分析
- **系统设置**: 全局配置和参数管理

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Next.js 15 + React 19
- **样式**: Tailwind CSS + shadcn/ui
- **状态管理**: tRPC + React Query
- **类型安全**: TypeScript

### 后端技术栈
- **API**: tRPC + Next.js API Routes
- **数据库**: PostgreSQL + Prisma ORM
- **认证**: NextAuth.js
- **存储**: AWS S3 (音频文件)

### AI服务集成
- **语音合成**: Google Gemini TTS API
- **模型管理**: 统一的模型提供商架构
- **成本控制**: 精确的Token计算和定价管理

## 📊 数据库设计

### 核心模型
- **用户系统**: User, Account, Session, UserCredit
- **语音管理**: TtsRole, Language, RoleLanguageSupport
- **模型架构**: ModelProvider, Model, ModelInterface
- **计费系统**: CustomPricing, ModelUsage, TokenUsage
- **订单系统**: Order, CreditTransaction, CreditPackage

## 🚀 快速开始

### 环境要求
- Node.js 18+
- PostgreSQL 14+
- Google Gemini API Key

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd voctana
```

2. **安装依赖**
```bash
npm install
```

3. **环境配置**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和API密钥
```

4. **数据库初始化**
```bash
# 运行数据库迁移
npm run db:migrate

# 初始化基础数据
npx tsx scripts/seed-providers.ts
npx tsx scripts/seed-real-voice-roles.ts
npx tsx scripts/init-project-specific-pricing.ts
```

5. **创建管理员账号**
```bash
npx tsx scripts/create-admin.ts
```

6. **启动开发服务器**
```bash
npm run dev
```

访问 http://localhost:3000 开始使用！

## 📝 主要脚本

### 数据库管理
- `npm run db:generate` - 生成Prisma客户端
- `npm run db:migrate` - 运行数据库迁移
- `npm run db:studio` - 打开Prisma Studio

### 开发工具
- `npm run dev` - 启动开发服务器
- `npm run build` - 构建生产版本
- `npm run lint` - 代码检查
- `npm run typecheck` - 类型检查

### 初始化脚本
- `scripts/seed-providers.ts` - 初始化模型提供商
- `scripts/seed-real-voice-roles.ts` - 创建语音角色数据
- `scripts/init-project-specific-pricing.ts` - 配置模型定价
- `scripts/create-admin.ts` - 创建管理员账号

## 🔧 配置说明

### 环境变量
```env
# 数据库
DATABASE_URL="postgresql://..."

# NextAuth
NEXTAUTH_SECRET="your-secret"
NEXTAUTH_URL="http://localhost:3000"

# Google Gemini API
GOOGLE_GENAI_API_KEY="your-api-key"

# AWS S3 (音频存储)
AWS_ACCESS_KEY_ID="your-access-key"
AWS_SECRET_ACCESS_KEY="your-secret-key"
AWS_REGION="your-region"
AWS_S3_BUCKET="your-bucket"
```

### 模型配置
系统支持动态配置多个AI模型提供商，当前主要集成：
- Google Gemini 2.5 Flash TTS (快速版本)
- Google Gemini 2.5 Pro TTS (高质量版本)

## 📚 文档

项目文档位于 `docs/` 目录：
- `gemini-billing-integration.md` - Gemini API计费集成指南
- `model-pricing-guide.md` - 模型定价设置详细指南
- `unified-model-management.md` - 统一模型管理系统说明
- `voice-samples-management.md` - 语音样本管理功能文档

## 🛡️ 安全特性

- **认证授权**: 基于NextAuth.js的安全认证
- **权限控制**: 细粒度的用户权限管理
- **数据验证**: 完整的输入验证和类型检查
- **成本控制**: 精确的使用量统计和限制

## 🌟 特色功能

### 智能语音角色
- 30个精心设计的语音角色
- 每个角色支持28种语言
- 标准版和高级版质量选择
- 个性化的语音风格和特色

### 灵活定价系统
- 支持多种定价模式（Token、字符、请求）
- 实时成本计算和统计
- 用户积分系统
- 批量购买优惠

### 统一管理后台
- 模型配置、定价管理、使用统计一体化
- 直观的数据可视化
- 实时监控和告警
- 批量操作和导入导出

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

本项目采用 MIT 许可证。

## 🔗 相关链接

- [Next.js 文档](https://nextjs.org/docs)
- [Prisma 文档](https://www.prisma.io/docs)
- [tRPC 文档](https://trpc.io/docs)
- [Google Gemini API](https://ai.google.dev/docs)