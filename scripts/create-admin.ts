import { PrismaClient, UserRole } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createAdmin() {
  try {
    // 管理员账号信息
    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123456';
    const adminName = '系统管理员';

    // 检查是否已存在管理员账号
    const existingAdmin = await prisma.user.findUnique({
      where: { email: adminEmail }
    });

    if (existingAdmin) {
      console.log('管理员账号已存在:', adminEmail);
      
      // 如果存在但不是管理员角色，则更新为管理员
      if (existingAdmin.role !== UserRole.ADMIN) {
        await prisma.user.update({
          where: { id: existingAdmin.id },
          data: { role: UserRole.ADMIN }
        });
        console.log('已将现有用户更新为管理员角色');
      }
      return;
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(adminPassword, 12);

    // 创建管理员账号 - 使用类型断言来处理password字段
    const admin = await prisma.user.create({
      data: {
        email: adminEmail,
        name: adminName,
        password: hashedPassword,
        role: UserRole.ADMIN,
        emailVerified: new Date(),
      } as any // 临时类型断言解决password字段类型问题
    });

    // 创建积分记录
    await prisma.userCredit.create({
      data: {
        userId: admin.id,
        balance: 1000, // 给管理员1000积分
        totalEarned: 1000,
        totalSpent: 0,
      }
    });

    console.log('管理员账号创建成功!');
    console.log('邮箱:', adminEmail);
    console.log('密码:', adminPassword);
    console.log('请登录后及时修改密码!');

  } catch (error) {
    console.error('创建管理员账号失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdmin();