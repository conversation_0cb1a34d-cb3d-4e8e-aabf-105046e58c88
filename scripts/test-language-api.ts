#!/usr/bin/env npx tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testLanguageAPI() {
  console.log('🌍 Testing Language Support API...\n');

  try {
    // 1. 测试语言统计
    console.log('1. 测试语言统计...');
    const languages = await prisma.language.findMany({
      include: {
        _count: {
          select: {
            roleSupports: {
              where: {
                role: {
                  isActive: true
                }
              }
            }
          }
        }
      },
      orderBy: { name: 'asc' },
      take: 10
    });

    console.log(`✅ 找到 ${languages.length} 种语言\n`);

    for (const lang of languages) {
      console.log(`🌐 ${lang.name} (${lang.code})`);
      console.log(`   本地名称: ${lang.nativeName}`);
      console.log(`   地区: ${lang.region}`);
      console.log(`   支持角色数: ${lang._count.roleSupports}`);
      console.log('');
    }

    // 2. 测试按语言查询角色
    const testLanguage = languages.find(l => l._count.roleSupports > 0);
    if (testLanguage) {
      console.log(`\n2. 测试按语言查询角色 (${testLanguage.name})...`);
      
      const rolesByLanguage = await prisma.ttsRole.findMany({
        where: {
          isActive: true,
          languageSupports: {
            some: { 
              languageCode: testLanguage.code 
            }
          }
        },
        include: {
          languageSupports: {
            where: { languageCode: testLanguage.code },
            include: {
              language: true
            }
          },
          modelMappings: {
            include: {
              model: {
                include: {
                  provider: true
                }
              }
            },
            orderBy: { priority: 'asc' }
          }
        },
        orderBy: { createdAt: "desc" },
        take: 5
      });

      console.log(`✅ 找到 ${rolesByLanguage.length} 个支持 ${testLanguage.name} 的角色\n`);

      for (const role of rolesByLanguage) {
        console.log(`🎭 ${role.nameEn} (${role.nameZh})`);
        console.log(`   语音名称: ${role.voiceName}`);
        console.log(`   性别: ${role.gender || '未设置'}`);
        
        // 显示模型映射
        const primaryMapping = role.modelMappings[0];
        if (primaryMapping) {
          console.log(`   主要提供商: ${primaryMapping.model.provider.name} (${primaryMapping.model.provider.slug})`);
          console.log(`   主要模型: ${primaryMapping.model.name}`);
        }
        console.log('');
      }
    }

    // 3. 测试多语言支持统计
    console.log('\n3. 多语言支持统计...');
    const multiLanguageRoles = await prisma.ttsRole.findMany({
      where: { isActive: true },
      include: {
        languageSupports: {
          include: {
            language: true
          }
        }
      },
      take: 5
    });

    for (const role of multiLanguageRoles) {
      const languageCount = role.languageSupports.length;
      const languageNames = role.languageSupports
        .slice(0, 3)
        .map(ls => ls.language.name)
        .join(', ');
      
      console.log(`🎭 ${role.nameEn}: 支持 ${languageCount} 种语言`);
      console.log(`   主要语言: ${languageNames}${languageCount > 3 ? ` +${languageCount - 3}种` : ''}`);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testLanguageAPI().catch(console.error);
