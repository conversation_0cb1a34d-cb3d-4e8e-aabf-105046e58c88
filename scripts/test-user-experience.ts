#!/usr/bin/env npx tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testUserExperience() {
  console.log('🎯 用户体验测试开始...\n');

  try {
    // 1. 测试语言加载性能
    console.log('1. 测试语言加载性能...');
    console.time('语言统计加载');
    
    const languages = await prisma.language.findMany({
      include: {
        _count: {
          select: {
            roleSupports: {
              where: {
                role: {
                  isActive: true
                }
              }
            }
          }
        }
      },
      where: {
        isActive: true,
        roleSupports: {
          some: {
            role: {
              isActive: true
            }
          }
        }
      },
      orderBy: { name: 'asc' }
    });
    
    console.timeEnd('语言统计加载');
    console.log(`   ✅ 加载了 ${languages.length} 种语言`);
    
    // 检查语言数据质量
    const languagesWithRoles = languages.filter(lang => lang._count.roleSupports > 0);
    const topLanguages = languages
      .sort((a, b) => b._count.roleSupports - a._count.roleSupports)
      .slice(0, 5);
    
    console.log(`   📊 有角色的语言: ${languagesWithRoles.length}/${languages.length}`);
    console.log('   🔝 角色最多的语言:');
    topLanguages.forEach((lang, index) => {
      console.log(`      ${index + 1}. ${lang.name}: ${lang._count.roleSupports} 个角色`);
    });

    // 2. 测试角色加载性能
    console.log('\n2. 测试角色加载性能...');
    
    const testLanguage = topLanguages[0];
    if (testLanguage) {
      console.time('角色列表加载');
      
      const roles = await prisma.ttsRole.findMany({
        where: {
          isActive: true,
          languageSupports: {
            some: {
              languageCode: testLanguage.code
            }
          }
        },
        include: {
          languageSupports: {
            where: { languageCode: testLanguage.code },
            include: {
              language: true
            }
          },
          modelMappings: {
            include: {
              model: {
                include: {
                  provider: true
                }
              }
            },
            orderBy: { priority: 'asc' }
          }
        },
        orderBy: { createdAt: "desc" },
        take: 50
      });
      
      console.timeEnd('角色列表加载');
      console.log(`   ✅ 加载了 ${roles.length} 个 ${testLanguage.name} 角色`);
      
      // 检查角色数据质量
      const rolesWithModels = roles.filter(role => role.modelMappings.length > 0);
      const rolesWithProviders = roles.filter(role => 
        role.modelMappings.some(mapping => mapping.model.provider.isActive)
      );
      
      console.log(`   📊 有模型映射的角色: ${rolesWithModels.length}/${roles.length}`);
      console.log(`   🔗 有活跃提供商的角色: ${rolesWithProviders.length}/${roles.length}`);
      
      // 分析提供商分布
      const providerStats = new Map<string, number>();
      roles.forEach(role => {
        role.modelMappings.forEach(mapping => {
          const providerName = mapping.model.provider.name;
          providerStats.set(providerName, (providerStats.get(providerName) || 0) + 1);
        });
      });
      
      console.log('   🏢 提供商分布:');
      Array.from(providerStats.entries()).forEach(([provider, count]) => {
        console.log(`      - ${provider}: ${count} 个映射`);
      });
    }

    // 3. 测试搜索功能
    console.log('\n3. 测试搜索功能...');
    
    const searchTerms = ['en', 'chinese', 'english', 'spanish', 'french'];
    
    for (const term of searchTerms) {
      console.time(`搜索 "${term}"`);
      
      const searchResults = languages.filter(lang =>
        lang.name.toLowerCase().includes(term.toLowerCase()) ||
        lang.nativeName.toLowerCase().includes(term.toLowerCase()) ||
        lang.code.toLowerCase().includes(term.toLowerCase())
      );
      
      console.timeEnd(`搜索 "${term}"`);
      console.log(`   🔍 "${term}" 找到 ${searchResults.length} 个结果`);
    }

    // 4. 测试数据完整性
    console.log('\n4. 测试数据完整性...');
    
    // 简化的数据完整性检查
    const totalRoles = await prisma.ttsRole.count({ where: { isActive: true } });
    const rolesWithLanguages = await prisma.ttsRole.count({
      where: {
        isActive: true,
        languageSupports: {
          some: {}
        }
      }
    });
    const rolesWithModels = await prisma.ttsRole.count({
      where: {
        isActive: true,
        modelMappings: {
          some: {}
        }
      }
    });

    const rolesWithoutLanguages = totalRoles - rolesWithLanguages;
    const rolesWithoutModels = totalRoles - rolesWithModels;

    console.log('   📋 数据完整性检查:');
    console.log(`   - 总活跃角色数: ${totalRoles}`);
    console.log(`   - 有语言支持的角色: ${rolesWithLanguages}`);
    console.log(`   - 有模型映射的角色: ${rolesWithModels}`);
    console.log(`   - 无语言支持的活跃角色: ${rolesWithoutLanguages}`);
    console.log(`   - 无模型映射的活跃角色: ${rolesWithoutModels}`);

    const hasIntegrityIssues = rolesWithoutLanguages > 0 || rolesWithoutModels > 0;
    if (hasIntegrityIssues) {
      console.log('   ⚠️  发现数据完整性问题，建议进行数据清理');
    } else {
      console.log('   ✅ 数据完整性良好');
    }

    // 5. 用户体验评分
    console.log('\n5. 用户体验评分...');
    
    let score = 100;
    const issues = [];

    // 语言加载性能评分
    if (languages.length < 10) {
      score -= 10;
      issues.push('语言数量较少，可能影响用户选择');
    }

    // 角色数量评分
    const totalActiveRoles = await prisma.ttsRole.count({ where: { isActive: true } });
    if (totalActiveRoles < 100) {
      score -= 15;
      issues.push('活跃角色数量较少');
    }

    // 提供商覆盖评分
    const activeProviders = await prisma.modelProvider.count({ where: { isActive: true } });
    if (activeProviders < 2) {
      score -= 20;
      issues.push('提供商数量不足，缺乏冗余');
    }

    // 数据完整性评分
    if (hasIntegrityIssues) {
      score -= 25;
      issues.push('存在数据完整性问题');
    }

    console.log(`   📊 用户体验评分: ${score}/100`);
    
    if (score >= 90) {
      console.log('   🎉 优秀 - 用户体验非常好');
    } else if (score >= 80) {
      console.log('   👍 良好 - 用户体验较好');
    } else if (score >= 70) {
      console.log('   ⚠️  一般 - 用户体验需要改进');
    } else {
      console.log('   ❌ 较差 - 用户体验需要大幅改进');
    }

    if (issues.length > 0) {
      console.log('\n   🔧 改进建议:');
      issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`);
      });
    }

    // 6. 性能建议
    console.log('\n6. 性能优化建议...');
    
    console.log('   🚀 前端优化:');
    console.log('   - 实现语言列表的虚拟滚动');
    console.log('   - 添加角色卡片的懒加载');
    console.log('   - 使用 React.memo 优化组件渲染');
    console.log('   - 实现音频文件的预加载');

    console.log('\n   🗄️  后端优化:');
    console.log('   - 实现 Redis 缓存层');
    console.log('   - 添加数据库连接池');
    console.log('   - 优化复杂查询的索引');
    console.log('   - 实现 API 响应压缩');

    console.log('\n🎯 用户体验测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testUserExperience().catch(console.error);
