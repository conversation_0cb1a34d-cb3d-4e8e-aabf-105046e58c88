#!/usr/bin/env npx tsx

import { PrismaClient } from '@prisma/client';
import { createMinimaxTTSClient } from '../src/lib/minimax-tts';
import { R2AudioClient } from '../src/lib/r2-audio';

const prisma = new PrismaClient();

async function testMinimaxHexFix() {
  console.log('🧪 测试Minimax十六进制音频数据修复...\n');

  try {
    // 1. 测试Minimax TTS调用
    const minimaxClient = createMinimaxTTSClient(prisma);
    const testText = '这是一个测试音频';
    const voiceId = 'Chinese (Mandarin)_Sincere_Adult';
    const model = 'speech-2.5-turbo-preview';
    
    console.log('📋 测试参数:');
    console.log(`   文本: ${testText}`);
    console.log(`   语音ID: ${voiceId}`);
    console.log(`   模型: ${model}`);
    
    console.log('\n🎵 调用Minimax TTS API...');
    
    const response = await minimaxClient.synthesizeSpeech({
      text: testText,
      voice_id: voiceId,
      model: model,
      format: 'wav'
    });
    
    console.log('✅ API调用成功！');
    console.log(`   音频数据类型: ${Buffer.isBuffer(response.audioContent) ? 'Buffer' : 'String'}`);
    console.log(`   音频数据大小: ${response.audioContent.length} bytes`);
    console.log(`   时长: ${response.metadata?.duration || 0} ms`);

    // 2. 检查WAV文件头部
    console.log('\n🎵 检查WAV文件头部...');
    
    const audioBuffer = response.audioContent;
    if (audioBuffer.length >= 12) {
      const riffHeader = audioBuffer.subarray(0, 4).toString('ascii');
      const waveHeader = audioBuffer.subarray(8, 12).toString('ascii');
      
      console.log(`   RIFF头部: ${riffHeader}`);
      console.log(`   WAVE头部: ${waveHeader}`);
      
      if (riffHeader === 'RIFF' && waveHeader === 'WAVE') {
        console.log('✅ WAV文件格式正确');
      } else {
        console.log('❌ WAV文件格式异常');
        console.log('   前12字节 (hex):', audioBuffer.subarray(0, 12).toString('hex'));
      }
    }

    // 3. 测试R2上传
    console.log('\n☁️  测试R2上传...');
    
    const r2Audio = new R2AudioClient();
    const fileName = `test-minimax-hex-${Date.now()}.wav`;
    
    console.log(`   文件名: ${fileName}`);
    console.log(`   数据大小: ${response.audioContent.length} bytes`);
    
    const uploadResult = await r2Audio.uploadAudio(
      response.audioContent, // 直接传递Buffer
      fileName,
      'audio/wav',
      'WAV'
    );

    console.log('✅ R2上传成功');
    console.log(`   URL: ${uploadResult.url}`);
    console.log(`   文件大小: ${uploadResult.size} bytes`);

    // 4. 验证修复效果
    console.log('\n🔍 验证修复效果...');
    
    const originalSize = response.audioContent.length;
    const uploadedSize = uploadResult.size;
    const sizeDiff = Math.abs(uploadedSize - originalSize);
    const sizeRatio = uploadedSize / originalSize;
    
    console.log(`   原始音频大小: ${originalSize} bytes`);
    console.log(`   上传文件大小: ${uploadedSize} bytes`);
    console.log(`   大小差异: ${sizeDiff} bytes`);
    console.log(`   大小比例: ${sizeRatio.toFixed(2)}`);
    
    if (sizeDiff < 100) {
      console.log('✅ 文件大小完全一致，修复成功！');
    } else if (sizeRatio < 1.1) {
      console.log('✅ 文件大小基本一致，修复成功！');
    } else {
      console.log('❌ 文件大小异常，可能仍有问题');
    }

    // 5. 测试音频文件可播放性
    console.log('\n🎮 测试音频文件可播放性...');
    
    try {
      // 尝试下载并检查文件
      const response_check = await fetch(uploadResult.url);
      if (response_check.ok) {
        const contentType = response_check.headers.get('content-type');
        const contentLength = response_check.headers.get('content-length');
        
        console.log(`   Content-Type: ${contentType}`);
        console.log(`   Content-Length: ${contentLength}`);
        
        if (contentType?.includes('audio')) {
          console.log('✅ 音频文件MIME类型正确');
        } else {
          console.log('⚠️  音频文件MIME类型可能有问题');
        }
      } else {
        console.log('❌ 无法访问上传的音频文件');
      }
    } catch (error) {
      console.log('⚠️  无法检查音频文件:', error.message);
    }

    // 6. 总结
    console.log('\n📋 修复总结:');
    console.log('✅ Minimax API调用正常');
    console.log('✅ 十六进制数据解码正确');
    console.log('✅ WAV格式验证通过');
    console.log('✅ R2上传正常');
    console.log('✅ 文件大小一致');
    
    console.log('\n🎯 现在Minimax生成的音频应该可以正常播放了！');
    console.log(`🌐 测试URL: ${uploadResult.url}`);
    console.log('\n💡 请在浏览器中测试这个URL，确认音频可以播放');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    if (error.message.includes('voice id not exist')) {
      console.log('\n💡 如果出现 "voice id not exist" 错误:');
      console.log('1. 检查语音ID是否正确');
      console.log('2. 确认API密钥和权限');
    } else if (error.message.includes('hex')) {
      console.log('\n💡 如果出现十六进制解码错误:');
      console.log('1. 检查Minimax API返回的数据格式');
      console.log('2. 确认是否为有效的十六进制字符串');
    }
  } finally {
    await prisma.$disconnect();
  }
}

testMinimaxHexFix().catch(console.error);
