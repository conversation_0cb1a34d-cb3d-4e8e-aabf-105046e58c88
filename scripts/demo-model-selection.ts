#!/usr/bin/env npx tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function demoModelSelection() {
  console.log('🎯 单语音生成页面 - 模型选择功能演示\n');

  try {
    // 1. 模拟用户选择语言
    const selectedLanguage = 'zh-CN';
    console.log(`1️⃣ 用户选择语言: ${selectedLanguage}`);

    // 2. 获取该语言的角色列表
    const roles = await prisma.ttsRole.findMany({
      where: {
        isActive: true,
        languageSupports: {
          some: { languageCode: selectedLanguage }
        }
      },
      include: {
        modelMappings: {
          include: {
            model: {
              include: {
                provider: true
              }
            }
          },
          orderBy: { priority: 'asc' }
        }
      },
      take: 3
    });

    console.log(`2️⃣ 获取到 ${roles.length} 个角色\n`);

    // 3. 模拟用户选择角色
    const selectedRole = roles[0];
    console.log(`3️⃣ 用户选择角色: ${selectedRole.nameEn} (${selectedRole.nameZh})`);
    console.log(`   语音ID: ${selectedRole.voiceName}\n`);

    // 4. 显示可用的模型选项
    const availableModels = selectedRole.modelMappings.filter(mapping => 
      mapping.model.isActive && mapping.model.provider.isActive
    );

    console.log('4️⃣ 系统显示可用的模型选项:');
    console.log('┌─────────────────────────────────────────────────────────────┐');
    console.log('│                        选择生成模型                          │');
    console.log('├─────────────────────────────────────────────────────────────┤');

    availableModels.forEach((mapping, index) => {
      const isHighQuality = mapping.model.name.includes('hd') || mapping.model.name.includes('pro');
      const isQuick = mapping.model.name.includes('turbo') || mapping.model.name.includes('flash');
      
      let qualityBadge = '🔵 标准';
      let description = '标准质量';
      
      if (isHighQuality) {
        qualityBadge = '🟢 高质量';
        description = '高质量语音，生成时间较长';
      } else if (isQuick) {
        qualityBadge = '🔵 快速';
        description = '快速生成，适合预览';
      }

      const isDefault = index === 0;
      const prefix = isDefault ? '● ' : '○ ';
      const defaultLabel = isDefault ? ' (默认)' : '';
      
      console.log(`│ ${prefix}${mapping.model.provider.name} - ${mapping.model.displayName}${defaultLabel}`);
      console.log(`│   ${qualityBadge} ${description}`);
      console.log('│');
    });

    console.log('└─────────────────────────────────────────────────────────────┘\n');

    // 5. 自动选择默认模型
    const defaultModel = availableModels[0];
    console.log(`5️⃣ 系统自动选择默认模型: ${defaultModel.model.provider.name} - ${defaultModel.model.displayName}`);

    // 6. 模拟用户选择不同的模型
    const highQualityModel = availableModels.find(mapping => 
      mapping.model.name.includes('hd') || mapping.model.name.includes('pro')
    );

    if (highQualityModel && highQualityModel.id !== defaultModel.id) {
      console.log(`6️⃣ 用户手动选择高质量模型: ${highQualityModel.model.provider.name} - ${highQualityModel.model.displayName}`);
    } else {
      console.log('6️⃣ 用户保持默认模型选择');
    }

    // 7. 模拟生成语音的API调用
    const selectedModel = highQualityModel || defaultModel;
    const testText = "选择语言和角色，输入文本生成高质量的AI语音";

    console.log('\n7️⃣ 模拟生成语音API调用:');
    console.log('┌─────────────────────────────────────────────────────────────┐');
    console.log('│                        API 调用参数                          │');
    console.log('├─────────────────────────────────────────────────────────────┤');
    console.log(`│ roleId: ${selectedRole.id}`);
    console.log(`│ language: ${selectedLanguage}`);
    console.log(`│ text: "${testText}"`);
    console.log(`│ modelMappingId: ${selectedModel.id}`);
    console.log('├─────────────────────────────────────────────────────────────┤');
    console.log(`│ 选择的提供商: ${selectedModel.model.provider.name}`);
    console.log(`│ 选择的模型: ${selectedModel.model.displayName}`);
    console.log(`│ 语音ID: ${selectedRole.voiceName}`);
    console.log('└─────────────────────────────────────────────────────────────┘\n');

    // 8. 显示用户体验流程
    console.log('8️⃣ 完整的用户体验流程:');
    console.log('┌─────────────────────────────────────────────────────────────┐');
    console.log('│                        用户操作流程                          │');
    console.log('├─────────────────────────────────────────────────────────────┤');
    console.log('│ 1. 选择语言 → 显示该语言的角色列表                           │');
    console.log('│ 2. 选择角色 → 自动选择默认模型，显示模型选择器               │');
    console.log('│ 3. (可选) 手动选择模型 → 高质量 vs 快速生成                  │');
    console.log('│ 4. 输入文本 → 显示预估消费                                   │');
    console.log('│ 5. 点击生成 → 使用指定模型生成语音                           │');
    console.log('│ 6. 播放音频 → 下载、历史记录等功能                           │');
    console.log('└─────────────────────────────────────────────────────────────┘\n');

    // 9. 显示技术优势
    console.log('9️⃣ 技术优势:');
    console.log('✅ 智能提供商选择 - 根据语音ID格式自动切换Gemini/Minimax');
    console.log('✅ 灵活模型选择 - 用户可选择高质量或快速生成模型');
    console.log('✅ 统一WAV输出 - 所有提供商统一输出WAV格式');
    console.log('✅ R2云存储 - 音频文件存储在云端，支持下载和分享');
    console.log('✅ 实时预览 - 支持音频播放、暂停、停止等控制');
    console.log('✅ 历史记录 - 保存最近的生成记录，方便重复播放');

    console.log('\n🎉 模型选择功能演示完成！');
    console.log('🌐 访问地址: http://localhost:3000/single-voice');

  } catch (error) {
    console.error('❌ 演示失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

demoModelSelection().catch(console.error);
