#!/usr/bin/env npx tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testTTSFunctionality() {
  console.log('🔍 Testing TTS Functionality...\n');

  try {
    // 1. 查询可用的语音角色
    console.log('1. 查询可用的语音角色...');
    const roles = await prisma.ttsRole.findMany({
      where: { isActive: true },
      include: {
        modelMappings: {
          include: {
            model: {
              include: {
                provider: true
              }
            }
          }
        },
        languageSupports: {
          include: {
            language: true
          }
        }
      },
      take: 5
    });

    console.log(`✅ 找到 ${roles.length} 个活跃角色\n`);

    for (const role of roles) {
      console.log(`📢 角色: ${role.nameEn} (${role.nameZh})`);
      console.log(`   语音名称: ${role.voiceName}`);
      console.log(`   性别: ${role.gender}`);
      
      // 显示支持的语言
      const languages = role.languageSupports.map(ls => ls.language.name).join(', ');
      console.log(`   支持语言: ${languages}`);
      
      // 显示模型映射
      for (const mapping of role.modelMappings) {
        console.log(`   🔗 模型: ${mapping.model.name} (${mapping.model.provider.name})`);
        console.log(`      提供商: ${mapping.model.provider.slug}`);
        console.log(`      优先级: ${mapping.priority}`);
      }
      console.log('');
    }

    // 2. 测试一个具体的角色
    if (roles.length > 0) {
      const testRole = roles[0];
      console.log(`\n2. 测试角色: ${testRole.nameEn}`);
      
      // 检查模型映射
      const activeMapping = testRole.modelMappings.find(mapping => 
        mapping.model.isActive && mapping.model.provider.isActive
      );
      
      if (activeMapping) {
        console.log(`✅ 找到活跃的模型映射:`);
        console.log(`   模型: ${activeMapping.model.name}`);
        console.log(`   提供商: ${activeMapping.model.provider.slug}`);
        console.log(`   API密钥配置: ${activeMapping.model.provider.apiKey ? '✅' : '❌'}`);
      } else {
        console.log('❌ 没有找到活跃的模型映射');
      }
    }

    // 3. 检查提供商配置
    console.log('\n3. 检查提供商配置...');
    const providers = await prisma.modelProvider.findMany({
      where: { isActive: true },
      include: {
        models: {
          where: { isActive: true }
        }
      }
    });

    for (const provider of providers) {
      console.log(`🏢 提供商: ${provider.name} (${provider.slug})`);
      console.log(`   API密钥: ${provider.apiKey ? '✅ 已配置' : '❌ 未配置'}`);
      console.log(`   活跃模型数: ${provider.models.length}`);
      
      for (const model of provider.models) {
        console.log(`   📱 模型: ${model.name} (${model.displayName})`);
      }
      console.log('');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testTTSFunctionality().catch(console.error);
