#!/usr/bin/env npx tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function analyzePerformance() {
  console.log('📊 性能分析开始...\n');

  try {
    // 1. 分析数据库查询性能
    console.log('1. 数据库查询性能分析...');
    
    const startTime = Date.now();
    
    // 测试语言统计查询
    console.time('语言统计查询');
    const languages = await prisma.language.findMany({
      include: {
        _count: {
          select: {
            roleSupports: {
              where: {
                role: {
                  isActive: true
                }
              }
            }
          }
        }
      },
      where: {
        isActive: true,
        roleSupports: {
          some: {
            role: {
              isActive: true
            }
          }
        }
      }
    });
    console.timeEnd('语言统计查询');
    console.log(`   结果: ${languages.length} 种语言`);

    // 测试角色查询性能
    console.time('角色查询');
    const roles = await prisma.ttsRole.findMany({
      where: {
        isActive: true,
        languageSupports: {
          some: { 
            languageCode: 'en-US'
          }
        }
      },
      include: {
        languageSupports: {
          where: { languageCode: 'en-US' },
          include: {
            language: true
          }
        },
        modelMappings: {
          include: {
            model: {
              include: {
                provider: true
              }
            }
          },
          orderBy: { priority: 'asc' }
        }
      },
      take: 50
    });
    console.timeEnd('角色查询');
    console.log(`   结果: ${roles.length} 个角色`);

    // 测试所有角色查询
    console.time('所有角色查询');
    const allRoles = await prisma.ttsRole.findMany({
      include: {
        languageSupports: {
          include: {
            language: true,
          }
        }
      },
      orderBy: { createdAt: "desc" },
    });
    console.timeEnd('所有角色查询');
    console.log(`   结果: ${allRoles.length} 个角色`);

    const totalTime = Date.now() - startTime;
    console.log(`\n总查询时间: ${totalTime}ms\n`);

    // 2. 分析数据库索引
    console.log('2. 数据库索引分析...');
    
    // 检查关键表的记录数
    const stats = await prisma.$transaction([
      prisma.ttsRole.count(),
      prisma.language.count(),
      prisma.roleLanguageSupport.count(),
      prisma.roleModelMapping.count(),
      prisma.model.count(),
      prisma.modelProvider.count()
    ]);

    console.log('   表记录统计:');
    console.log(`   - TtsRole: ${stats[0]} 条记录`);
    console.log(`   - Language: ${stats[1]} 条记录`);
    console.log(`   - RoleLanguageSupport: ${stats[2]} 条记录`);
    console.log(`   - RoleModelMapping: ${stats[3]} 条记录`);
    console.log(`   - Model: ${stats[4]} 条记录`);
    console.log(`   - ModelProvider: ${stats[5]} 条记录`);

    // 3. 分析查询复杂度
    console.log('\n3. 查询复杂度分析...');
    
    // 分析最复杂的查询 - 角色详情查询
    console.time('复杂角色查询');
    const complexRole = await prisma.ttsRole.findFirst({
      where: { isActive: true },
      include: {
        languageSupports: {
          include: {
            language: true
          }
        },
        modelMappings: {
          include: {
            model: {
              include: {
                provider: true
              }
            }
          }
        }
      }
    });
    console.timeEnd('复杂角色查询');
    
    if (complexRole) {
      console.log(`   角色 ${complexRole.nameEn}:`);
      console.log(`   - 支持 ${complexRole.languageSupports.length} 种语言`);
      console.log(`   - 有 ${complexRole.modelMappings.length} 个模型映射`);
    }

    // 4. 性能建议
    console.log('\n4. 性能优化建议...');
    
    const suggestions = [];
    
    if (stats[2] > 1000) {
      suggestions.push('考虑为 RoleLanguageSupport 表添加复合索引 (roleId, languageCode)');
    }
    
    if (stats[3] > 500) {
      suggestions.push('考虑为 RoleModelMapping 表添加复合索引 (roleId, priority)');
    }
    
    if (totalTime > 1000) {
      suggestions.push('查询时间较长，建议实现查询结果缓存');
    }
    
    if (languages.length > 20) {
      suggestions.push('语言数量较多，建议在前端实现语言选择的搜索功能');
    }
    
    if (allRoles.length > 300) {
      suggestions.push('角色数量较多，建议实现分页和虚拟滚动');
    }

    if (suggestions.length > 0) {
      console.log('   建议的优化措施:');
      suggestions.forEach((suggestion, index) => {
        console.log(`   ${index + 1}. ${suggestion}`);
      });
    } else {
      console.log('   ✅ 当前性能表现良好');
    }

    // 5. 缓存策略建议
    console.log('\n5. 缓存策略建议...');
    
    console.log('   推荐缓存的数据:');
    console.log('   - 语言列表 (TTL: 1小时)');
    console.log('   - 活跃提供商列表 (TTL: 30分钟)');
    console.log('   - 角色基本信息 (TTL: 15分钟)');
    console.log('   - 模型映射关系 (TTL: 10分钟)');

    console.log('\n   推荐的缓存实现:');
    console.log('   - Redis 用于服务器端缓存');
    console.log('   - React Query 用于客户端缓存');
    console.log('   - 浏览器 localStorage 用于用户偏好设置');

  } catch (error) {
    console.error('❌ 性能分析失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

analyzePerformance().catch(console.error);
