#!/usr/bin/env npx tsx

import { PrismaClient } from '@prisma/client';
import { GeminiTTSClient } from '../src/lib/gemini-tts';
import { MinimaxTTSClient } from '../src/lib/minimax-tts';
import { R2AudioClient } from '../src/lib/r2-audio';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

async function testWAVOutput() {
  console.log('🎵 Testing WAV Output Consistency...\n');

  try {
    // 1. 获取测试角色
    const testRole = await prisma.ttsRole.findFirst({
      where: { 
        isActive: true,
        voiceName: 'Zephyr' // Aurora角色
      },
      include: {
        modelMappings: {
          include: {
            model: {
              include: {
                provider: true
              }
            }
          }
        }
      }
    });

    if (!testRole) {
      console.log('❌ 没有找到测试角色');
      return;
    }

    console.log(`🎭 测试角色: ${testRole.nameEn} (${testRole.voiceName})`);

    // 测试文本
    const testText = "Hello, this is a test of the TTS system.";
    console.log(`📝 测试文本: "${testText}"\n`);

    // 2. 测试Minimax TTS
    console.log('🔵 测试 Minimax TTS...');
    const minimaxMapping = testRole.modelMappings.find(m =>
      m.model.provider.slug === 'minimax' && m.model.isActive
    );

    if (minimaxMapping) {
      try {
        const minimaxClient = new MinimaxTTSClient(prisma);
        // 使用一个已知的Minimax语音ID进行测试
        const minimaxResponse = await minimaxClient.synthesizeSpeech({
          text: testText,
          voice_id: 'female-shaonv', // 使用已知的Minimax语音ID
          model: minimaxMapping.model.name,
          format: 'wav' // 确保WAV格式
        });

        console.log(`✅ Minimax TTS 成功`);
        console.log(`   音频长度: ${minimaxResponse.audioContent.length} bytes`);
        console.log(`   格式: WAV (直接输出)`);

        // 保存测试文件
        const minimaxPath = path.join(process.cwd(), 'test-minimax.wav');
        fs.writeFileSync(minimaxPath, Buffer.from(minimaxResponse.audioContent, 'base64'));
        console.log(`   已保存: ${minimaxPath}`);

      } catch (error) {
        console.log(`❌ Minimax TTS 失败:`, error);
      }
    } else {
      console.log('❌ 没有找到Minimax模型映射');
    }

    console.log('');

    // 3. 测试Gemini TTS
    console.log('🟢 测试 Gemini TTS...');
    const geminiMapping = testRole.modelMappings.find(m =>
      m.model.provider.slug === 'gemini' && m.model.isActive
    );

    if (geminiMapping) {
      try {
        const geminiClient = new GeminiTTSClient(prisma);
        const geminiResponse = await geminiClient.synthesizeSpeech({
          text: testText,
          voice: testRole.voiceName,
          model: geminiMapping.model.name
        });

        console.log(`✅ Gemini TTS 成功`);
        console.log(`   音频长度: ${geminiResponse.audioContent.length} bytes`);
        console.log(`   格式: LINEAR16 (需要转换为WAV)`);

        // 使用R2AudioClient转换为WAV
        const r2Audio = new R2AudioClient();
        const wavBuffer = (r2Audio as any).createWavBuffer(
          Buffer.from(geminiResponse.audioContent, 'base64'),
          24000, // 采样率 (Gemini默认24kHz)
          1      // 声道数
        );

        console.log(`✅ 转换为WAV成功`);
        console.log(`   WAV长度: ${wavBuffer.length} bytes`);

        // 保存测试文件
        const geminiPath = path.join(process.cwd(), 'test-gemini.wav');
        fs.writeFileSync(geminiPath, wavBuffer);
        console.log(`   已保存: ${geminiPath}`);

      } catch (error) {
        console.log(`❌ Gemini TTS 失败:`, error);
      }
    } else {
      console.log('❌ 没有找到Gemini模型映射');
    }

    console.log('\n🎯 WAV格式一致性测试完成！');
    console.log('📁 测试文件已保存到项目根目录');
    console.log('   - test-minimax.wav (Minimax直接输出)');
    console.log('   - test-gemini.wav (Gemini转换输出)');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testWAVOutput().catch(console.error);
