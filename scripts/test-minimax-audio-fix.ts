#!/usr/bin/env npx tsx

import { PrismaClient } from '@prisma/client';
import { createMinimaxTTSClient } from '../src/lib/minimax-tts';
import { R2AudioClient } from '../src/lib/r2-audio';

const prisma = new PrismaClient();

async function testMinimaxAudioFix() {
  console.log('🧪 测试Minimax音频修复...\n');

  try {
    // 1. 找一个Minimax角色进行测试
    const minimaxRole = await prisma.ttsRole.findFirst({
      where: {
        isActive: true,
        voiceName: 'Straightforward_Boy', // 使用之前测试的角色
        modelMappings: {
          some: {
            model: {
              provider: {
                slug: 'minimax'
              }
            }
          }
        }
      },
      include: {
        modelMappings: {
          include: {
            model: {
              include: {
                provider: true
              }
            }
          },
          orderBy: { priority: 'asc' }
        }
      }
    });

    if (!minimaxRole) {
      console.log('❌ 没有找到测试角色');
      return;
    }

    console.log('📋 测试角色信息:');
    console.log(`   角色: ${minimaxRole.nameEn} (${minimaxRole.nameZh})`);
    console.log(`   语音ID: ${minimaxRole.voiceName}`);
    
    const primaryMapping = minimaxRole.modelMappings[0];
    console.log(`   提供商: ${primaryMapping.model.provider.name}`);
    console.log(`   模型: ${primaryMapping.model.name}`);

    // 2. 测试Minimax TTS调用
    console.log('\n🎵 测试Minimax TTS调用...');
    
    const minimaxClient = createMinimaxTTSClient(prisma);
    const testText = "这是一个测试音频";
    
    console.log(`   文本: "${testText}"`);
    console.log(`   语音ID: ${minimaxRole.voiceName}`);
    console.log(`   模型: ${primaryMapping.model.name}`);
    
    const minimaxResponse = await minimaxClient.synthesizeSpeech({
      text: testText,
      voice_id: minimaxRole.voiceName,
      model: primaryMapping.model.name,
      format: 'wav'
    });

    console.log('✅ Minimax TTS调用成功');
    console.log(`   音频数据类型: ${Buffer.isBuffer(minimaxResponse.audioContent) ? 'Buffer' : 'String'}`);
    console.log(`   音频数据大小: ${minimaxResponse.audioContent.length} bytes`);
    console.log(`   时长: ${minimaxResponse.metadata?.duration || 0} ms`);

    // 3. 测试R2上传（使用修复后的方法）
    console.log('\n☁️  测试R2上传...');
    
    const r2Audio = new R2AudioClient();
    const fileName = `test-minimax-${Date.now()}.wav`;
    
    console.log('   上传方式: 直接传递Buffer（修复后）');
    console.log(`   文件名: ${fileName}`);
    console.log(`   数据大小: ${minimaxResponse.audioContent.length} bytes`);
    
    const uploadResult = await r2Audio.uploadAudio(
      minimaxResponse.audioContent, // 直接传递Buffer
      fileName,
      'audio/wav',
      'WAV'
    );

    console.log('✅ R2上传成功');
    console.log(`   URL: ${uploadResult.url}`);
    console.log(`   文件大小: ${uploadResult.size} bytes`);
    console.log(`   存储键: ${uploadResult.key}`);

    // 4. 验证修复效果
    console.log('\n🔍 验证修复效果...');
    
    const originalSize = minimaxResponse.audioContent.length;
    const uploadedSize = uploadResult.size;
    const sizeDiff = Math.abs(uploadedSize - originalSize);
    const sizeRatio = uploadedSize / originalSize;
    
    console.log(`   原始音频大小: ${originalSize} bytes`);
    console.log(`   上传文件大小: ${uploadedSize} bytes`);
    console.log(`   大小差异: ${sizeDiff} bytes`);
    console.log(`   大小比例: ${sizeRatio.toFixed(2)}`);
    
    if (sizeRatio > 1.5) {
      console.log('❌ 文件大小异常，可能仍有Base64编码问题');
    } else if (sizeDiff < 1000) {
      console.log('✅ 文件大小正常，修复成功！');
    } else {
      console.log('⚠️  文件大小有轻微差异，但在正常范围内');
    }

    // 5. 测试音频文件头部
    console.log('\n🎵 检查WAV文件头部...');
    
    const audioBuffer = minimaxResponse.audioContent;
    if (audioBuffer.length >= 12) {
      const riffHeader = audioBuffer.subarray(0, 4).toString('ascii');
      const waveHeader = audioBuffer.subarray(8, 12).toString('ascii');
      
      console.log(`   RIFF头部: ${riffHeader}`);
      console.log(`   WAVE头部: ${waveHeader}`);
      
      if (riffHeader === 'RIFF' && waveHeader === 'WAVE') {
        console.log('✅ WAV文件格式正确');
      } else {
        console.log('❌ WAV文件格式异常');
      }
    }

    // 6. 总结
    console.log('\n📋 修复总结:');
    console.log('✅ Minimax TTS API调用正常');
    console.log('✅ 音频数据格式正确（Buffer）');
    console.log('✅ R2上传支持Buffer输入');
    console.log('✅ 文件大小正常（无Base64重复编码）');
    console.log('✅ WAV格式验证通过');
    
    console.log('\n🎯 现在Minimax生成的音频应该可以正常播放了！');
    console.log(`🌐 测试URL: ${uploadResult.url}`);

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testMinimaxAudioFix().catch(console.error);
