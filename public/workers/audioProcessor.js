// Audio processing Web Worker
// 处理音频相关的计算任务，避免阻塞主线程

self.onmessage = function(e) {
  const { type, data } = e.data;

  switch (type) {
    case 'init':
      self.postMessage({ type: 'ready' });
      break;

    case 'calculateWaveform':
      calculateWaveform(data);
      break;

    case 'analyzeAudio':
      analyzeAudio(data);
      break;

    case 'processAudioBuffer':
      processAudioBuffer(data);
      break;

    case 'generateVisualization':
      generateVisualization(data);
      break;

    default:
      self.postMessage({ type: 'error', message: 'Unknown task type' });
  }
};

// 计算音频波形数据
function calculateWaveform(data) {
  try {
    const { audioBuffer, samples = 1000 } = data;
    const waveform = [];
    const blockSize = Math.floor(audioBuffer.length / samples);

    for (let i = 0; i < samples; i++) {
      const start = i * blockSize;
      const end = start + blockSize;
      let sum = 0;

      for (let j = start; j < end && j < audioBuffer.length; j++) {
        sum += Math.abs(audioBuffer[j]);
      }

      waveform.push(sum / blockSize);
    }

    // 归一化
    const max = Math.max(...waveform);
    const normalizedWaveform = waveform.map(value => value / max);

    self.postMessage({
      type: 'waveformCalculated',
      data: normalizedWaveform
    });
  } catch (error) {
    self.postMessage({
      type: 'error',
      message: 'Failed to calculate waveform: ' + error.message
    });
  }
}

// 分析音频特征
function analyzeAudio(data) {
  try {
    const { audioBuffer, sampleRate } = data;
    
    // 计算音频时长
    const duration = audioBuffer.length / sampleRate;
    
    // 计算RMS (均方根)
    let rmsSum = 0;
    for (let i = 0; i < audioBuffer.length; i++) {
      rmsSum += audioBuffer[i] * audioBuffer[i];
    }
    const rms = Math.sqrt(rmsSum / audioBuffer.length);
    
    // 计算峰值
    const peak = Math.max(...audioBuffer.map(Math.abs));
    
    // 检测静音段
    const silenceThreshold = 0.01;
    const silentSamples = audioBuffer.filter(sample => Math.abs(sample) < silenceThreshold).length;
    const silencePercentage = (silentSamples / audioBuffer.length) * 100;
    
    // 计算动态范围
    const dynamicRange = 20 * Math.log10(peak / rms);

    self.postMessage({
      type: 'audioAnalyzed',
      data: {
        duration,
        rms,
        peak,
        silencePercentage,
        dynamicRange,
        quality: peak > 0.1 ? 'good' : 'low'
      }
    });
  } catch (error) {
    self.postMessage({
      type: 'error',
      message: 'Failed to analyze audio: ' + error.message
    });
  }
}

// 处理音频缓冲区
function processAudioBuffer(data) {
  try {
    const { buffer, operation, params = {} } = data;
    let processedBuffer;

    switch (operation) {
      case 'normalize':
        processedBuffer = normalizeAudio(buffer);
        break;
      
      case 'amplify':
        processedBuffer = amplifyAudio(buffer, params.gain || 1.0);
        break;
      
      case 'fade':
        processedBuffer = applyFade(buffer, params.fadeIn || 0, params.fadeOut || 0);
        break;
      
      case 'trim':
        processedBuffer = trimAudio(buffer, params.start || 0, params.end || buffer.length);
        break;
      
      default:
        throw new Error('Unknown audio operation: ' + operation);
    }

    self.postMessage({
      type: 'audioProcessed',
      data: processedBuffer
    });
  } catch (error) {
    self.postMessage({
      type: 'error',
      message: 'Failed to process audio: ' + error.message
    });
  }
}

// 生成可视化数据
function generateVisualization(data) {
  try {
    const { audioBuffer, type = 'bars', resolution = 64 } = data;
    let visualizationData;

    switch (type) {
      case 'bars':
        visualizationData = generateBarVisualization(audioBuffer, resolution);
        break;
      
      case 'waveform':
        visualizationData = generateWaveformVisualization(audioBuffer, resolution);
        break;
      
      case 'spectrum':
        visualizationData = generateSpectrumVisualization(audioBuffer, resolution);
        break;
      
      default:
        throw new Error('Unknown visualization type: ' + type);
    }

    self.postMessage({
      type: 'visualizationGenerated',
      data: visualizationData
    });
  } catch (error) {
    self.postMessage({
      type: 'error',
      message: 'Failed to generate visualization: ' + error.message
    });
  }
}

// 辅助函数
function normalizeAudio(buffer) {
  const peak = Math.max(...buffer.map(Math.abs));
  if (peak === 0) return buffer;
  
  const normalizedBuffer = new Float32Array(buffer.length);
  for (let i = 0; i < buffer.length; i++) {
    normalizedBuffer[i] = buffer[i] / peak;
  }
  return normalizedBuffer;
}

function amplifyAudio(buffer, gain) {
  const amplifiedBuffer = new Float32Array(buffer.length);
  for (let i = 0; i < buffer.length; i++) {
    amplifiedBuffer[i] = Math.max(-1, Math.min(1, buffer[i] * gain));
  }
  return amplifiedBuffer;
}

function applyFade(buffer, fadeInSamples, fadeOutSamples) {
  const fadedBuffer = new Float32Array(buffer.length);
  
  for (let i = 0; i < buffer.length; i++) {
    let gain = 1;
    
    // Fade in
    if (i < fadeInSamples) {
      gain = i / fadeInSamples;
    }
    
    // Fade out
    if (i > buffer.length - fadeOutSamples) {
      gain = (buffer.length - i) / fadeOutSamples;
    }
    
    fadedBuffer[i] = buffer[i] * gain;
  }
  
  return fadedBuffer;
}

function trimAudio(buffer, start, end) {
  const trimmedLength = end - start;
  const trimmedBuffer = new Float32Array(trimmedLength);
  
  for (let i = 0; i < trimmedLength; i++) {
    trimmedBuffer[i] = buffer[start + i];
  }
  
  return trimmedBuffer;
}

function generateBarVisualization(buffer, resolution) {
  const blockSize = Math.floor(buffer.length / resolution);
  const bars = [];
  
  for (let i = 0; i < resolution; i++) {
    const start = i * blockSize;
    const end = start + blockSize;
    let sum = 0;
    
    for (let j = start; j < end && j < buffer.length; j++) {
      sum += Math.abs(buffer[j]);
    }
    
    bars.push(sum / blockSize);
  }
  
  return bars;
}

function generateWaveformVisualization(buffer, resolution) {
  const blockSize = Math.floor(buffer.length / resolution);
  const waveform = [];
  
  for (let i = 0; i < resolution; i++) {
    const start = i * blockSize;
    const end = start + blockSize;
    let min = 1, max = -1;
    
    for (let j = start; j < end && j < buffer.length; j++) {
      min = Math.min(min, buffer[j]);
      max = Math.max(max, buffer[j]);
    }
    
    waveform.push({ min, max });
  }
  
  return waveform;
}

function generateSpectrumVisualization(buffer, resolution) {
  // 简化的频谱分析 (实际应用中应使用FFT)
  const spectrum = [];
  const blockSize = Math.floor(buffer.length / resolution);
  
  for (let i = 0; i < resolution; i++) {
    const start = i * blockSize;
    const end = start + blockSize;
    let energy = 0;
    
    for (let j = start; j < end && j < buffer.length; j++) {
      energy += buffer[j] * buffer[j];
    }
    
    spectrum.push(Math.sqrt(energy / blockSize));
  }
  
  return spectrum;
}
